{"calls": {"https://partner.steampowered.com/login": [{"body": {"data": "https_partner.steampowered.com/login", "encoding": "file"}, "headers": {"content-security-policy": "default-src blob: data: https: 'unsafe-inline' 'unsafe-eval'; connect-src 'self' https://community.akamai.steamstatic.com/ https://store.steampowered.com/ wss://community.steam-api.com/websocket/ https://api.steampowered.com/ https://login.steampowered.com/ https://help.steampowered.com/ https://*.valvesoftware.com https://*.steambeta.net https://*.steamcontent.com; report-uri /actions/CSPReport", "content-encoding": "gzip", "vary": "Accept-Encoding"}}, {"status": 302, "req_headers": {"cookie": "steamLoginSecure=76561199079710050%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.WnfSvX2shqFlYxmmguu_Xwzi_RV_bTVeggEFGiF418W3hV0e9kdjpI-6Fiyw2npoyGt967JA_IJLiiQNb-POBw; steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6;"}, "headers": {"content-security-policy": "default-src blob: data: https: 'unsafe-inline' 'unsafe-eval'; connect-src 'self' https://community.akamai.steamstatic.com/ https://store.steampowered.com/ wss://community.steam-api.com/websocket/ https://api.steampowered.com/ https://login.steampowered.com/ https://help.steampowered.com/ https://*.valvesoftware.com https://*.steambeta.net https://*.steamcontent.com; report-uri /actions/CSPReport", "location": "https://partner.steampowered.com/"}}], "https://scraper-api.indiebi.dev/1/auth-code/steam_sales": [{"req_headers": {"authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************.BWjkwK3KECSqJLTjOVZ88_0CAJd68BDpthJxzeLu3Y4", "host": "scraper-api.indiebi.dev", "connection": "keep-alive"}, "body": "{\"authCode\":null}", "headers": {"content-type": "application/json; charset=utf-8", "connection": "keep-alive", "request-context": "appId=cid-v1:1ae09687-f2ab-424c-b8e7-c482afdc19c9", "x-powered-by": "Express", "vary": "Origin", "access-control-allow-credentials": "true", "etag": "W/\"11-CSaGiLTQJ36vkT+ep4rzOLCKeXw\"", "strict-transport-security": "max-age=15724800; includeSubDomains", "server": "", "content-security-policy": ""}}, {"req_headers": {"authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************.BWjkwK3KECSqJLTjOVZ88_0CAJd68BDpthJxzeLu3Y4", "host": "scraper-api.indiebi.dev", "connection": "keep-alive"}, "body": "{\"authCode\":\"R6XYD\"}", "headers": {"content-type": "application/json; charset=utf-8", "connection": "keep-alive", "request-context": "appId=cid-v1:1ae09687-f2ab-424c-b8e7-c482afdc19c9", "x-powered-by": "Express", "vary": "Origin", "access-control-allow-credentials": "true", "etag": "W/\"14-uo+LHWgAgCAcV8LH7XPh/gCGztI\"", "strict-transport-security": "max-age=15724800; includeSubDomains", "server": "", "content-security-policy": ""}}], "https://partner.steampowered.com/": [{"status": 302, "req_headers": {"cookie": "steamLoginSecure=76561199079710050%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.WnfSvX2shqFlYxmmguu_Xwzi_RV_bTVeggEFGiF418W3hV0e9kdjpI-6Fiyw2npoyGt967JA_IJLiiQNb-POBw; steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6;"}, "headers": {"location": "https://partner.steampowered.com/app/top/", "set-cookie": "dateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:37:08 GMT; Max-Age=10800; path=/, dateEnd=2023-08-08; expires=Tue, 08-Aug-2023 15:37:08 GMT; Max-Age=10800; path=/, priorDateStart=2023-08-07; expires=Tue, 08-Aug-2023 15:37:08 GMT; Max-Age=10800; path=/, priorDateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:37:08 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}, {"status": 302, "req_headers": {"cookie": "steamLoginSecure=76561199079710050%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.P3w37tKS07iD4xLx_vGcaB2amt0SPd2pnOS5Wdlp_XgC24YGq0B_mLyh9xMyEbD1N7_6cRgWpzzSQQfc0ZnsCQ; sessionid=7511fc3539832dd7a0926cb8; dateStart=2023-08-08; steamworksRunas=3204;"}, "headers": {"location": "https://partner.steampowered.com/app/top/", "set-cookie": "dateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:37:12 GMT; Max-Age=10800; path=/, dateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:37:12 GMT; Max-Age=10800; path=/, priorDateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:37:12 GMT; Max-Age=10800; path=/, priorDateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:37:12 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/app/top/": [{"status": 302, "req_headers": {"cookie": "steamLoginSecure=76561199079710050%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.WnfSvX2shqFlYxmmguu_Xwzi_RV_bTVeggEFGiF418W3hV0e9kdjpI-6Fiyw2npoyGt967JA_IJLiiQNb-POBw; steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; dateStart=2023-08-08;"}, "headers": {"location": "https://partner.steampowered.com/nav_games.php", "set-cookie": "dateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:37:08 GMT; Max-Age=10800; path=/, dateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:37:08 GMT; Max-Age=10800; path=/, priorDateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:37:08 GMT; Max-Age=10800; path=/, priorDateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:37:08 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/nav_games.php": [{"req_headers": {"cookie": "steamLoginSecure=76561199079710050%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.WnfSvX2shqFlYxmmguu_Xwzi_RV_bTVeggEFGiF418W3hV0e9kdjpI-6Fiyw2npoyGt967JA_IJLiiQNb-POBw; steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; dateStart=2023-08-08;"}, "body": {"data": "https_partner.steampowered.com/nav_games.php", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:37:09 GMT; Max-Age=10800; path=/, dateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:37:09 GMT; Max-Age=10800; path=/, priorDateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:37:09 GMT; Max-Age=10800; path=/, priorDateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:37:09 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}, {"req_headers": {"cookie": "steamLoginSecure=76561199079710050%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.WnfSvX2shqFlYxmmguu_Xwzi_RV_bTVeggEFGiF418W3hV0e9kdjpI-6Fiyw2npoyGt967JA_IJLiiQNb-POBw; steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; dateStart=2023-08-08;"}, "body": {"data": "https_partner.steampowered.com/nav_games.php", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:37:09 GMT; Max-Age=10800; path=/, dateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:37:09 GMT; Max-Age=10800; path=/, priorDateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:37:09 GMT; Max-Age=10800; path=/, priorDateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:37:09 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}, {"req_headers": {"cookie": "steamLoginSecure=76561199079710050%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.P3w37tKS07iD4xLx_vGcaB2amt0SPd2pnOS5Wdlp_XgC24YGq0B_mLyh9xMyEbD1N7_6cRgWpzzSQQfc0ZnsCQ; sessionid=7511fc3539832dd7a0926cb8;"}, "body": {"data": "https_partner.steampowered.com/nav_games_4e46e3bc8d61ae7600fe5edec3f4aa0a.php", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:37:11 GMT; Max-Age=10800; path=/, dateEnd=2023-08-08; expires=Tue, 08-Aug-2023 15:37:11 GMT; Max-Age=10800; path=/, priorDateStart=2023-08-07; expires=Tue, 08-Aug-2023 15:37:11 GMT; Max-Age=10800; path=/, priorDateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:37:11 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/dir.php": [{"req_headers": {"cookie": "steamLoginSecure=76561199079710050%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.P3w37tKS07iD4xLx_vGcaB2amt0SPd2pnOS5Wdlp_XgC24YGq0B_mLyh9xMyEbD1N7_6cRgWpzzSQQfc0ZnsCQ; sessionid=7511fc3539832dd7a0926cb8; dateStart=2023-08-08; steamworksRunas=3204;"}, "body": {"data": "https_partner.steampowered.com/dir.php", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:37:13 GMT; Max-Age=10800; path=/, dateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:37:13 GMT; Max-Age=10800; path=/, priorDateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:37:13 GMT; Max-Age=10800; path=/, priorDateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:37:13 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/package/details/50666/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"req_headers": {"cookie": "steamLoginSecure=76561199079710050%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.P3w37tKS07iD4xLx_vGcaB2amt0SPd2pnOS5Wdlp_XgC24YGq0B_mLyh9xMyEbD1N7_6cRgWpzzSQQfc0ZnsCQ; sessionid=7511fc3539832dd7a0926cb8; dateStart=2023-08-08; steamworksRunas=3204;"}, "body": {"data": "https_partner.steampowered.com/package_details_50666_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:13 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:13 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:13 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:13 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=50666": [{"req_headers": {"cookie": "steamLoginSecure=76561199079710050%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.P3w37tKS07iD4xLx_vGcaB2amt0SPd2pnOS5Wdlp_XgC24YGq0B_mLyh9xMyEbD1N7_6cRgWpzzSQQfc0ZnsCQ; sessionid=7511fc3539832dd7a0926cb8; dateStart=2023-08-08; steamworksRunas=3204;"}, "body": {"data": "https_partner.steampowered.com/report_csv.php", "encoding": "file"}, "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/364120/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_364120_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:14 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:14 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:14 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:14 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=364120": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/427502/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_427502_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:15 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:15 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:15 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:15 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=427502": [{"body": {"data": "https_partner.steampowered.com/report_csv_a8c5dbdc065b9e9577d997b20a55d10b.php", "encoding": "file"}, "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\"", "transfer-encoding": "chunked", "connection": "close, Transfer-Encoding"}}], "https://partner.steampowered.com/package/details/165761/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_165761_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:15 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:15 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:15 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:15 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=165761": [{"body": {"data": "https_partner.steampowered.com/report_csv_b730ba34fee257ece2e37dd02ebc78c5.php", "encoding": "file"}, "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\"", "transfer-encoding": "chunked", "connection": "close, Transfer-Encoding"}}], "https://partner.steampowered.com/package/details/181885/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_181885_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:16 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:16 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:16 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:16 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=181885": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n2021-11-16,-1,Direct Package Sale,181885,\"SUPERHOT VR - Recurring Commercial License\",Steam,\"SUPERHOT VR\",Windows,DE,Germany,Western Europe,1,0,1,45.99,45.99,EUR,51.9800,0.0000,-8.2960,43.6840,\"\"\n2021-11-16,-1,Direct Package Sale,181885,\"SUPERHOT VR - Recurring Commercial License\",Steam,\"SUPERHOT VR\",Windows,GB,United Kingdom,Western Europe,6,0,6,179.94,179.94,GBP,241.9800,0.0000,-40.3300,201.6500,\"\"\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=181886": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/181886/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_181886_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:17 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:17 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:17 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:17 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/package/details/357027/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_357027_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:18 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:18 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:18 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:18 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=357027": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n2021-11-01,-1,Direct Package Sale,357027,\"SUPERHOT VR: Arcade Edition Commercial License (Monthly)\",Steam,\"SUPERHOT VR\",Windows,RU,Russian Federation,Russia Ukraine & CIS,1,0,1,899.00,899.00,RUB,12.7000,0.0000,-2.1166,10.5834,\"\"\n2021-11-08,-1,Direct Package Sale,357027,\"SUPERHOT VR: Arcade Edition Commercial License (Monthly)\",Steam,\"SUPERHOT VR\",Windows,RU,Russian Federation,Russia Ukraine & CIS,4,0,4,3596.00,3596.00,RUB,50.7800,0.0000,-8.4633,42.3167,\"\"\n2021-11-09,-1,Direct Package Sale,357027,\"SUPERHOT VR: Arcade Edition Commercial License (Monthly)\",Steam,\"SUPERHOT VR\",Windows,FR,France,Western Europe,1,0,1,45.99,45.99,EUR,53.1700,0.0000,-8.8674,44.3026,\"\"\n2021-11-16,-1,Direct Package Sale,357027,\"SUPERHOT VR: Arcade Edition Commercial License (Monthly)\",Steam,\"SUPERHOT VR\",Windows,US,United States,North America,3,0,3,49.99,49.99,USD,158.9700,0.0000,-9.0000,149.9700,\"\"\n2021-11-18,-1,Direct Package Sale,357027,\"SUPERHOT VR: Arcade Edition Commercial License (Monthly)\",Steam,\"SUPERHOT VR\",Windows,AU,Australia,Oceania,1,0,1,69.95,69.95,AUD,50.7700,0.0000,-4.6161,46.1539,\"\"\n2021-11-27,-1,Direct Package Sale,357027,\"SUPERHOT VR: Arcade Edition Commercial License (Monthly)\",Steam,\"SUPERHOT VR\",Windows,CZ,Czech Republic,Eastern Europe,1,0,1,45.99,45.99,EUR,52.1400,0.0000,-9.0471,43.0929,\"\"\n2021-11-30,-1,Direct Package Sale,357027,\"SUPERHOT VR: Arcade Edition Commercial License (Monthly)\",Steam,\"SUPERHOT VR\",Windows,PL,Poland,Eastern Europe,1,0,1,69.99,69.99,PLN,17.2000,0.0000,-3.2169,13.9831,\"\"\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=357028": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/357028/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_357028_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:18 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:18 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:18 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:18 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=362452": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/362452/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_362452_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:19 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:19 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:19 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:19 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=372014": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/372014/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_372014_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:20 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:20 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:20 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:20 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=372015": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/372015/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_372015_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:20 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:20 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:20 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:20 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/package/details/197766/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_197766_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:21 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:21 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:21 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:21 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=197766": [{"body": {"data": "https_partner.steampowered.com/report_csv_9762206d58f44186e3f0f1b88cb8563f.php", "encoding": "file"}, "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\"", "transfer-encoding": "chunked", "connection": "close, Transfer-Encoding"}}], "https://partner.steampowered.com/package/details/349970/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_349970_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:22 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:22 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:22 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:22 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=349970": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=511991": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/511991/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_511991_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:22 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:22 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:22 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:22 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=50664": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/50664/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_50664_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:23 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:23 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:23 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:23 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=50665": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/50665/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_50665_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:23 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:23 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:23 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:23 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=138227": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/138227/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_138227_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:24 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:24 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:24 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:24 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=138228": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/138228/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_138228_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:24 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:24 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:24 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:24 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=427503": [{"body": {"data": "https_partner.steampowered.com/report_csv_6337cdd80a337c2f9adfd1a6db4cde8e.php", "encoding": "file"}, "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/427503/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_427503_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:25 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:25 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:25 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:25 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=427504": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/427504/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_427504_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:25 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:25 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:25 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:25 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=564501": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/564501/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_564501_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:26 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:26 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:26 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:26 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=564502": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/564502/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_564502_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:27 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:27 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:27 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:27 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=564503": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/564503/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_564503_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:27 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:27 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:27 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:27 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=564504": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/564504/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_564504_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:28 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:28 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:28 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:28 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=564505": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/564505/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_564505_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:28 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:28 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:28 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:28 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=894193": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/894193/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_894193_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:29 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:29 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:29 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:29 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=165759": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/165759/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_165759_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:29 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:29 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:29 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:29 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=165760": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/165760/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_165760_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:30 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:30 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:30 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:30 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=305597": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/305597/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_305597_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:30 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:30 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:30 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:30 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=322267": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/322267/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_322267_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:35 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:35 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:35 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:35 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=564509": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n2021-11-01,-1,Direct Package Sale,564509,\"SUPERHOT VR (RU + CIS) (retail)\",Retail,\"SUPERHOT VR\",Windows,RU,Russian Federation,Russia Ukraine & CIS,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"Genba\"\n2021-11-03,-1,Direct Package Sale,564509,\"SUPERHOT VR (RU + CIS) (retail)\",Retail,\"SUPERHOT VR\",Windows,RU,Russian Federation,Russia Ukraine & CIS,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"Genba\"\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/564509/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_564509_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:42 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:42 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:42 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:42 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=564510": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/564510/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_564510_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:43 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:43 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:43 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:43 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=564511": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/564511/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_564511_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:43 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:43 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:43 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:43 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=564512": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/564512/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_564512_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:44 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:44 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:44 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:44 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/package/details/564513/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_564513_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:44 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:44 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:44 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:44 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=564513": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=886309": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/886309/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_886309_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:45 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:45 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:45 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:45 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=894194": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/894194/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_894194_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:50 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:50 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:50 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:50 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=197764": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/197764/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_197764_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:50 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:50 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:50 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:50 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=197765": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/197765/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_197765_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:51 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:51 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:51 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:51 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=484977": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/484977/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_484977_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:37:51 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:37:51 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:37:51 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:37:51 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=564514": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n2021-11-01,-1,Direct Package Sale,564514,\"SUPERHOT : MIND CONTROL DELETE (RU + CIS) (retail)\",Retail,\"SUPERHOT: MIND CONTROL DELETE\",Windows,RU,Russian Federation,Russia Ukraine & CIS,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"Genba\"\n2021-11-03,-1,Direct Package Sale,564514,\"SUPERHOT : MIND CONTROL DELETE (RU + CIS) (retail)\",Retail,\"SUPERHOT: MIND CONTROL DELETE\",Windows,RU,Russian Federation,Russia Ukraine & CIS,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"Genba\"\n2021-11-04,-1,Direct Package Sale,564514,\"SUPERHOT : MIND CONTROL DELETE (RU + CIS) (retail)\",Retail,\"SUPERHOT: MIND CONTROL DELETE\",Windows,RU,Russian Federation,Russia Ukraine & CIS,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"Genba\"\n2021-11-06,-1,Direct Package Sale,564514,\"SUPERHOT : MIND CONTROL DELETE (RU + CIS) (retail)\",Retail,\"SUPERHOT: MIND CONTROL DELETE\",Windows,RU,Russian Federation,Russia Ukraine & CIS,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"Genba\"\n2021-11-08,-1,Direct Package Sale,564514,\"SUPERHOT : MIND CONTROL DELETE (RU + CIS) (retail)\",Retail,\"SUPERHOT: MIND CONTROL DELETE\",Windows,RU,Russian Federation,Russia Ukraine & CIS,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"Genba\"\n2021-11-12,-1,Direct Package Sale,564514,\"SUPERHOT : MIND CONTROL DELETE (RU + CIS) (retail)\",Retail,\"SUPERHOT: MIND CONTROL DELETE\",Windows,RU,Russian Federation,Russia Ukraine & CIS,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"Genba\"\n2021-11-13,-1,Direct Package Sale,564514,\"SUPERHOT : MIND CONTROL DELETE (RU + CIS) (retail)\",Retail,\"SUPERHOT: MIND CONTROL DELETE\",Windows,RU,Russian Federation,Russia Ukraine & CIS,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"Genba\"\n2021-11-14,-1,Direct Package Sale,564514,\"SUPERHOT : MIND CONTROL DELETE (RU + CIS) (retail)\",Retail,\"SUPERHOT: MIND CONTROL DELETE\",Windows,RU,Russian Federation,Russia Ukraine & CIS,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"Genba\"\n2021-11-16,-1,Direct Package Sale,564514,\"SUPERHOT : MIND CONTROL DELETE (RU + CIS) (retail)\",Retail,\"SUPERHOT: MIND CONTROL DELETE\",Windows,UA,Ukraine,Russia Ukraine & CIS,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"Genba\"\n2021-11-18,-1,Direct Package Sale,564514,\"SUPERHOT : MIND CONTROL DELETE (RU + CIS) (retail)\",Retail,\"SUPERHOT: MIND CONTROL DELETE\",Linux,RU,Russian Federation,Russia Ukraine & CIS,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"Genba\"\n2021-11-19,-1,Direct Package Sale,564514,\"SUPERHOT : MIND CONTROL DELETE (RU + CIS) (retail)\",Retail,\"SUPERHOT: MIND CONTROL DELETE\",Windows,RU,Russian Federation,Russia Ukraine & CIS,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"Genba\"\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/564514/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_564514_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:38:03 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:38:03 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:38:03 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:38:03 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=564515": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/564515/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_564515_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:38:03 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:38:03 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:38:03 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:38:03 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=564516": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/564516/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_564516_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:38:04 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:38:04 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:38:04 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:38:04 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=564517": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/564517/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_564517_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:38:04 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:38:04 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:38:04 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:38:04 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=564518": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/564518/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_564518_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:38:05 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:38:05 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:38:05 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:38:05 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=894195": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/894195/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_894195_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:38:06 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:38:06 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:38:06 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:38:06 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=349968": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/349968/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_349968_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:38:06 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:38:06 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:38:06 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:38:06 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=349969": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/349969/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_349969_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:38:07 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:38:07 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:38:07 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:38:07 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=409886": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/409886/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_409886_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:38:07 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:38:07 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:38:07 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:38:07 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=409887": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n2021-11-04,-1,Direct Package Sale,409887,\"IndieBI for Beta Testing (retail)\",Retail,\"IndieBI\",Unknown,US,United States,North America,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"test\"\n2021-11-10,-1,Direct Package Sale,409887,\"IndieBI for Beta Testing (retail)\",Retail,\"IndieBI\",Mac,GB,United Kingdom,Western Europe,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"test\"\n2021-11-15,-1,Direct Package Sale,409887,\"IndieBI for Beta Testing (retail)\",Retail,\"IndieBI\",Unknown,GB,United Kingdom,Western Europe,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"test\"\n2021-11-15,-1,Direct Package Sale,409887,\"IndieBI for Beta Testing (retail)\",Retail,\"IndieBI\",Windows,GB,United Kingdom,Western Europe,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"test\"\n2021-11-16,-1,Direct Package Sale,409887,\"IndieBI for Beta Testing (retail)\",Retail,\"IndieBI\",Windows,SE,Sweden,Western Europe,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"test\"\n2021-11-17,-1,Direct Package Sale,409887,\"IndieBI for Beta Testing (retail)\",Retail,\"IndieBI\",Windows,US,United States,North America,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"test\"\n2021-11-18,-1,Direct Package Sale,409887,\"IndieBI for Beta Testing (retail)\",Retail,\"IndieBI\",Windows,PL,Poland,Eastern Europe,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"test\"\n2021-11-19,-1,Direct Package Sale,409887,\"IndieBI for Beta Testing (retail)\",Retail,\"IndieBI\",Windows,US,United States,North America,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"test\"\n2021-11-25,-1,Direct Package Sale,409887,\"IndieBI for Beta Testing (retail)\",Retail,\"IndieBI\",Windows,PL,Poland,Eastern Europe,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"test\"\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/409887/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_409887_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:38:08 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:38:08 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:38:08 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:38:08 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=438202": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/438202/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_438202_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:38:08 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:38:08 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:38:08 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:38:08 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=438203": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/438203/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_438203_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:38:09 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:38:09 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:38:09 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:38:09 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=511989": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/511989/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_511989_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:38:09 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:38:09 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:38:09 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:38:09 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/package/details/511990/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_511990_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:38:10 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:38:10 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:38:10 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:38:10 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=511990": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=283161": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/283161/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_283161_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:38:10 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:38:10 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:38:10 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:38:10 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=576117": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/576117/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_576117_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:38:11 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:38:11 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:38:11 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:38:11 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=759681": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/759681/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_759681_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:38:11 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:38:11 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:38:11 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:38:11 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=262183": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/262183/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_262183_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:38:12 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:38:12 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:38:12 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:38:12 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=365509": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/365509/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_365509_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:38:15 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:38:15 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:38:15 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:38:15 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=576523": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/576523/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_576523_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:38:16 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:38:16 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:38:16 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:38:16 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=409888": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/409888/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_409888_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:38:16 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:38:16 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:38:16 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:38:16 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=438204": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\""}}], "https://partner.steampowered.com/package/details/438204/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_438204_", "encoding": "file"}, "headers": {"content-encoding": "gzip", "vary": "Accept-Encoding", "set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:38:17 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:38:17 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:38:17 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:38:17 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}]}, "defaults": {"request_headers": {"accept": "application/json, text/plain, */*", "cookie": "steamLoginSecure=76561199079710050%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.P3w37tKS07iD4xLx_vGcaB2amt0SPd2pnOS5Wdlp_XgC24YGq0B_mLyh9xMyEbD1N7_6cRgWpzzSQQfc0ZnsCQ; sessionid=7511fc3539832dd7a0926cb8; dateStart=2021-11-01; steamworksRunas=3204;", "user-agent": "axios/0.27.2", "host": "partner.steampowered.com", "connection": "close"}, "headers": {"server": "nginx", "content-type": "text/html; charset=UTF-8", "content-security-policy": "script-src 'self' 'unsafe-inline' 'unsafe-eval'; object-src 'none'; connect-src 'self'; child-src 'self'; report-uri /actions/cspreport", "connection": "close"}}}