import * as _ from 'lodash';
import {LogLevel} from '../../utils/logger';
import {CustomException} from '../CustomException';
import {errorTypes} from '../errorTypes';

export class ApiRequestErrorException extends CustomException {
    constructor(url: string, error: Error) {
        const errorMessage: string | undefined = _.get(error, 'response.data', undefined);

        super({
            message: `Error while making HTTP request to: ${url}.${errorMessage ? `\nError: ${JSON.stringify(errorMessage)}` : ''}`,
            error,
            suggestedAction: 'There was a network communication issue while communicating with the portal. It will automatically try again.',
            telemetryLogLevel: LogLevel.WARN,
            errorType: errorTypes.TEMPORARY_PORTAL_ISSUE
        });
    }
}
