import {expect} from 'chai';
import * as moment from 'moment';
import {getDaysBetweenDates} from '../../../../src/utils/dates/getDaysBetweenDates';

describe('getDaysBetweenDates', () => {
    it('should return single date when start and end date are the same', () => {
        const startDate = moment('2020-01-01');
        const endDate = moment('2020-01-01');
        const actualDates = getDaysBetweenDates(startDate, endDate);
        expect(actualDates).to.have.lengthOf(1);
        expect(actualDates[0].format('YYYY-MM-DD')).to.be.equal('2020-01-01');
    });
    it('should return an array of dates between the start and end date', () => {
        const startDate = moment('2020-01-01');
        const endDate = moment('2020-01-03');
        const expectedDatesString = ['2020-01-01', '2020-01-02', '2020-01-03'];
        const actualDates = getDaysBetweenDates(startDate, endDate);
        expect(actualDates).to.have.lengthOf(3);
        expect(actualDates.map((d) => d.format('YYYY-MM-DD'))).to.be.deep.equal(expectedDatesString);
    });
});
