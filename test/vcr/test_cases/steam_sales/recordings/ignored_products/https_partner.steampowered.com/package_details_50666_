<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>Steam And Retail package: SUPERHOT</title>
		<link href="https://partner.steampowered.com//public/shared/css/motiva_sans.css?v=4PcevOsxrNNN&amp;l=english" rel="stylesheet" type="text/css" >
<link href="https://partner.steampowered.com//public/shared/css/shared_global.css?v=0eWv6yZVGYl9&amp;l=english" rel="stylesheet" type="text/css" >
<link href="https://partner.steampowered.com//public/shared/css/buttons.css?v=q-AeMgeWqerW&amp;l=english" rel="stylesheet" type="text/css" >
<link href="https://partner.steampowered.com//public/css/../../styles_global.css?v=.ADTp-ykXxQJy" rel="stylesheet" type="text/css" >
<link href="https://partner.steampowered.com//public/css/styles_financials.css?v=b8232820&amp;l=english" rel="stylesheet" type="text/css" >
<link href="https://partner.steampowered.com//public/css/styles_flot_charts.css?v=b8232820&amp;l=english" rel="stylesheet" type="text/css" >
<!--[if lte IE 7]> <style type="text/css"> .iepopupfix{ display: block; } </style> <![endif]-->
		<script type="text/javascript">VALVE_PUBLIC_PATH = "https:\/\/partner.steampowered.com\/\/public\/";</script>
<script type="text/javascript" src="https://partner.steampowered.com//public/javascript/prototype-*******.js?v=.he8XHzCaLikk" ></script>

<script type="text/javascript" src="https://partner.steampowered.com//public/shared/javascript/jquery-1.8.3.min.js?v=.TZ2NKhB-nliU" ></script>

<script type="text/javascript" src="https://partner.steampowered.com//public/shared/javascript/shared_global.js?v=8Imy0d3iccAr&amp;l=english" ></script>

<script type="text/javascript" src="https://partner.steampowered.com//public/shared/javascript/flot-0.8/jquery.flot.min.js?v=.-m414tR-pxn_" ></script>

<script type="text/javascript" src="https://partner.steampowered.com//public/shared/javascript/flot-0.8/jquery.flot.stack.min.js?v=.q21noVBcZ0YV" ></script>

<script type="text/javascript" src="https://partner.steampowered.com//public/shared/javascript/flot-0.8/jquery.flot.pie.min.js?v=.t3jSfg5N5S60" ></script>

<script type="text/javascript" src="https://partner.steampowered.com//public/shared/javascript/flot-0.8/jquery.flot.resize.min.js?v=.4PeWDSmdkiqV" ></script>

<script type="text/javascript" src="https://partner.steampowered.com//public/shared/javascript/flot-0.8/jquery.flot.navigate.min.js?v=.4oqHDPjEb2ji" ></script>

<script type="text/javascript" src="https://partner.steampowered.com//public/shared/javascript/flot-0.8/jquery.flot.time.min.js?v=.tcjKevZLo5Un" ></script>

<script type="text/javascript" src="https://partner.steampowered.com//public/shared/javascript/flot-0.8/excanvas.min.js?v=.rmNQYg3NI41m" ></script>

<script type="text/javascript" src="https://partner.steampowered.com//public/javascript/d3.v3.min.js?v=.SrJBgiZ580ji" ></script>

<script type="text/javascript" src="https://partner.steampowered.com//public/javascript/flot.charthelper.js?v=.BhlexENE4tqC" ></script>

<script type="text/javascript" src="https://partner.steampowered.com//public/javascript/global.js?v=FSQLI_zid_Av&amp;l=english" ></script>

<script type="text/javascript">$J = jQuery.noConflict();</script><script type="text/javascript" src="https://partner.steampowered.com/swf/swfobject.js"></script>
		<script type="text/javascript" src="https://partner.steampowered.com/swf/FABridge.js"></script>
		<script type="text/javascript" src="https://partner.steampowered.com/swf/timeline_support.js"></script>
		<script language="javascript" src="https://partner.steampowered.com/datafeedhandler.js"></script>
		<script type="text/javascript" src="https://partner.steampowered.com/swf/fusioncharts.js"></script>	</head>
	<body>
		<div id="header"><img src="https://partner.steampowered.com/img/logo_steamworks_sales.png" height="76" width="600" border="0" /></div>
		<div id="header_menu"><span id="header_left">	<div style="float:right;padding-right:46px;"><span style="color: #acacac;">Signed in as <strong>supersuperdata</strong></span>
	

	<span style="color:#414141;">|</span>
	<a href="https://partner.steampowered.com/login/logout">Logout</a><span style="color:#414141;">|</span>
	<a href="https://partner.steampowered.com/dir.php">Mega Site Map</a><span style="color:#414141;">|</span>

			<form>
		<input name="runasGoto" type="hidden" autocomplete="off">
		View as: <select style="width: 10em;" onchange="this.form.submit();" name="runasPubid">
		<option value="-1">Default</option>
<option selected value=3204>SUPERHOT Sp. z o.o.</option>
</select>
		</form>
		
	<form method="get" action="https://partner.steampowered.com/dir.php" >Search
	<input type="text" name="filter" size=9 value=""/>
	</form>
	</div>
	<div style="text-align:left;float:left;padding-left:46px;">
	<a href="https://partner.steampowered.com/">Home</a> <span style="color:#414141;">|</span>
	<a href="https://partner.steampowered.com/app/top/">Products</a> <span style="color:#414141;">|</span>
	<a href="https://partner.steampowered.com/package/steam/">Packages</a> <span style="color:#414141;">|</span>
	<a href="https://partner.steampowered.com/bundles/">Bundles</a> <span style="color:#414141;">|</span>
	<a href="https://partner.steampowered.com/region/">Regions</a> <span style="color:#414141;">|</span>
	<a href="https://partner.steampowered.com/wishlist/daily/">Wishlists</a> <span style="color:#414141;">|</span>
	<a href="https://partner.steampowered.com/players.php">Players</a> <span style="color:#414141;">|</span>

	<a href="https://partner.steampowered.com/coupons.php">Coupons</a> <span style="color:#2C2C2C;">|</span> <a href="https://partner.steampowered.com/partner_report2.php?partnerid=3204">Reports</a> <span style="color:#2C2C2C;">|</span>	</div>
	</span></div>
		<center>
			<div class="ContentWrapper">
				<h1>Steam And Retail package: SUPERHOT</h1>
				<div class="hr-color"><hr></div>
				<br>
								<div>
					<div id="leftParent" style="float:left; width: 620px;";>
						<div id="leftUpperChild" class="lifetimeSummaryCtn">
							<table>
										<tr>			<td>Lifetime Steam revenue (gross)</td>
			<td align="right">$12,127,029</td>
					</tr>
				<tr>			<td>Lifetime Steam revenue (net)</td>
			<td align="right">$10,214,315</td>
			<td align=right colspan="3">(gross revenues less returns, chargebacks, and taxes)</td>		</tr>
				<tr>			<td>Lifetime Steam units</td>
			<td align="right">822,898</td>
					</tr>
				<tr>			<td>Lifetime Steam units returned</td>
			<td align="right">-93,512</td>
			<td align=right colspan="3">&nbsp;</td>		</tr>
				<tr>			<td>Lifetime Retail units</td>
			<td align="right">662,853</td>
					</tr>
									</table>
						</div>
						<br>
						<div id="leftLowerChild">		<b>Apps in package:</b><br>
		<a href="https://partner.steampowered.com/app/details/322500/?dateStart=2021-11-01&amp;dateEnd=2021-11-30">SUPERHOT</a><a href="#" class="tooltip"><img src="https://partner.steampowered.com/img/primary_app.png"><span>Sales of this package will contribute to the additional revenue calculation of this app.</span></a><br><a href="https://partner.steampowered.com/app/details/690040/?dateStart=2021-11-01&amp;dateEnd=2021-11-30">SUPERHOT: MIND CONTROL DELETE</a><br></div>
						<br>
					</div>
					<div id="rightParent" style="float:right; width:300px;">
													<b>Links:</b>
								<br><a href="https://partner.steampowered.com/package/refunds/50666/">Refund data</a>
								<br><a href="https://partner.steampowered.com//sub_carts.php?subID=50666">Shopping cart analysis</a>
																									<br><a href="https://partner.steampowered.com//sub_appcontribution.php?subID=50666">Game contribution analysis</a>
																												</div>
					<br clear="all"/>
					<div><div class="PeriodLinks">View most recent: &nbsp; <a href="https://partner.steampowered.com/package/details/50666/?specialPeriod=today">today</a> <span style="color:#424242;">|</span> <a href="https://partner.steampowered.com/package/details/50666/?dateStart=2023-08-07&amp;dateEnd=2023-08-07&amp;priorDateStart=2023-08-06&amp;priorDateEnd=2023-08-06">yesterday</a> <span style="color:#424242;">|</span> <a href="https://partner.steampowered.com/package/details/50666/?dateStart=2023-08-01&amp;dateEnd=2023-08-07&amp;priorDateStart=2023-07-25&amp;priorDateEnd=2023-07-31">1 week</a> <span style="color:#424242;">|</span> <a href="https://partner.steampowered.com/package/details/50666/?dateStart=2023-07-25&amp;dateEnd=2023-08-07&amp;priorDateStart=2023-07-11&amp;priorDateEnd=2023-07-24">2 weeks</a> <span style="color:#424242;">|</span> <a href="https://partner.steampowered.com/package/details/50666/?dateStart=2023-07-08&amp;dateEnd=2023-08-07&amp;priorDateStart=2023-06-07&amp;priorDateEnd=2023-07-07">1 month</a> <span style="color:#424242;">|</span> <a href="https://partner.steampowered.com/package/details/50666/?dateStart=2023-05-09&amp;dateEnd=2023-08-07&amp;priorDateStart=2023-02-07&amp;priorDateEnd=2023-05-08">3 months</a> <span style="color:#424242;">|</span> <a href="https://partner.steampowered.com/package/details/50666/?dateStart=2022-08-09&amp;dateEnd=2023-08-07&amp;priorDateStart=2021-08-10&amp;priorDateEnd=2022-08-08">1 year</a> <span style="color:#424242;">|</span> <a href="https://partner.steampowered.com/package/details/50666/?dateStart=2023-01-01&amp;dateEnd=2023-08-08&amp;priorDateStart=2022-05-26&amp;priorDateEnd=2022-12-31">ytd</a> <span style="color:#424242;">|</span> <a href="https://partner.steampowered.com/package/details/50666/?dateStart=2000-01-01&amp;dateEnd=2023-08-08&amp;priorDateStart=1976-05-25&amp;priorDateEnd=1999-12-31">all history</a> <span style="color:#424242;">|</span> <a href="javascript:toggleThing('DateRangeSelector')">custom</a> <span style="color:#424242;">|</span> (<em>11/1/2021 - 11/30/2021)</em> &nbsp;&nbsp;&nbsp; <div style="float:right;"><a href="javascript:toggleThing('Preferences')">Preferences</a> </div></div>	<script language="javascript">
	function toggleThing( thingID )
	{
		el = document.getElementById( thingID );
		if ( el.style.display != 'block' )
		{
			el.style.display = 'block';
		}
		else
		{
			el.style.display = 'none';
		}
	}

	function toggleThingVis( thingID )
	{
		el = document.getElementById( thingID );
		if ( el.style.visibility != 'visible' )
		{
			el.style.visibility = 'visible';
		}
		else
		{
			el.style.visibility = 'hidden';
		}
	}
	</script>
	<style type="text/css">

	.initHidden {
		display:none;
	}

	.initInvisible {
		visibility:hidden;
	}
	</style>
	<script language="JavaScript" src="https://partner.steampowered.com//controls/calendar/calendar.js"></script>
	<script language=javascript>
	<!--
		var cal1 = new CalendarPopup();
		var tomorrow = new Date() ; with (tomorrow) setDate(getDate()+1)
		var cal1 = new CalendarPopup();
		cal1.addDisabledDates(formatDate(tomorrow,"yyyy-MM-dd"), null);
	-->

		// Check whether or not we should be displaying date selection
		function showPriorDateSelect(elem)
		{
			if ( elem.value == 'Custom' )
			{
				document.getElementById('DateRangeSelector2').style.display = "block";
			}
			else
			{
				document.getElementById('DateRangeSelector2').style.display = "none";
			}
		}
	</script>

	
	<div id="DateRangeSelector" class="initHidden" style="background:#2C2C2C;padding:16px 0px 0px 0px;vertical-align:middle;">

		<form method="get" name="cal_form" >

			<div style="float:left; margin:0px 32px 0px 0px;">
				Start Date:
				<INPUT TYPE="text" NAME="dateStart" SIZE=9 VALUE="2021-11-01" />
				<A HREF="javascript:cal1.select(document.forms['cal_form'].dateStart,'anchor1','yyyy-MM-dd');" NAME="anchor1" ID="anchor1"><IMG style="vertical-align:middle;" src="https://partner.steampowered.com//controls/calendar/cal.jpg"></A>
				12:00 AM			</div>

			<div style="float:left; margin:0px 32px 0px 0px;">
				End Date:
				<INPUT TYPE="text" NAME="dateEnd" SIZE=9 VALUE="2021-11-30" />
				<A HREF="javascript:cal1.select(document.forms['cal_form'].dateEnd,'anchor2','yyyy-MM-dd');" NAME="anchor2" ID="anchor2"><IMG style="vertical-align:middle;" src="https://partner.steampowered.com//controls/calendar/cal.jpg"></A>
				11:59 PM			</div>
							<input type="submit" value="Update" name="submit">
		</form>
	</div>

	<div id="Preferences" class="initHidden" style="background:#2C2C2C;padding:16px 0px 16px 0px;vertical-align:middle;">

		<form method="get" name="pref_form" >

			<div style="float:left; margin:0px 32px 0px 0px;">
				Date Link Style:
								<select style="width: 8em;" name="dateLinkStyle">
					<option selected  value="Standard">Standard</option>
					<option value="Financial">Financial</option>
				</select>
			</div>

			<div style="float:left; margin:0px 32px 0px 0px;">
				Prior period:
								<select id = "PriorPeriodSelect" style="width: 13em;" name="alignPriorAnnual" onchange= "showPriorDateSelect(this)" >
					<option selected	value="Immediate" title="Prior period will immediately precede selected date range">Immediate prior</option>
					<option	value="One Year"   title="Prior period will align with date range one year earlier">Year-over-year, comp</option>
					<option		value="Custom"   title="Prior period can be any range">Custom period</option>
				</select>
			</div>
			<div id="DateRangeSelector2" class="initHidden" style="background:#2C2C2C;">
				<div style="float:left; margin:0px 32px 0px 0px;">
					Prior Start:
					<INPUT TYPE="text" NAME="priorDateStart" SIZE=9 VALUE="2021-10-02" />
				</div>
				<div style="float:left; margin:0px 32px 0px 0px;">
					Prior End:
					<INPUT TYPE="text" NAME="priorDateEnd" SIZE=9 VALUE="2021-10-31" />
				</div>
				<script>
					// check if we should be showing date values initially
					document.addEventListener("load", showPriorDateSelect( document.getElementById('PriorPeriodSelect') ) );
				</script>
			</div>

						<div style="float:right;">
				<input type="submit" value="Update" name="submit">
			</div>
		</form>
	</div>
	</div>
						<br>
					<table>
	<tr style="border-bottom-style:solid; border-bottom-width:1px;">
				<td></td>
		<td></td>
		<td align=right title='2021-11-01 to 2021-11-30'><b>11/1/2021 to 11/30/2021</b></td>
			<td></td>										<!--Expandable percentage column-->
					<td align=right><b>Daily average during period<b></td>
				<td align=right><b>Change vs. previous period</b></td>
		<td align=right></td>
		<td class="dim" align=right  title='2021-10-02 to 2021-10-31'><b>Previous 30 days</b></td>
		<td></td>										<!--Expandable percentage column-->
					<td class="dim" align=right><b>Previous daily average<b></td>
			</tr>
		<tr >
	
	<td><a href="https://partner.steampowered.com/package/steam/">Total revenue</a> 		</td>
	<td></td>
	<td align=right>$0</td>
	<td align=right></td>
				<td align=right>$0</td>
		
				<td align=right><span style="color:#BD4D18;"></span></td>
		
		<td width=16></td>
		<td class="dim" align=right>$0</td>
		<td class="dim" align=right></td>
				<td class="dim" align=right width=100>$0</td>
		
		<td></td>
	</tr>
		<tr >
	
	<td>&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://partner.steampowered.com/package/steam/">Steam revenue</a> 		</td>
	<td></td>
	<td align=right>$0</td>
	<td align=right></td>
				<td align=right>$0</td>
		
				<td align=right><span style="color:#BD4D18;"></span></td>
		
		<td width=16></td>
		<td class="dim" align=right>$0</td>
		<td class="dim" align=right></td>
				<td class="dim" align=right width=100>$0</td>
		
		<td></td>
	</tr>
		<tr id=extended_PeriodRemainingRevenue style="display: none;">
	
	<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Remaining revenue 		</td>
	<td></td>
	<td align=right>$0</td>
	<td align=right></td>
				<td align=right>$0</td>
		
				<td align=right><span style="color:#BD4D18;"></span></td>
		
		<td width=16></td>
		<td class="dim" align=right>$0</td>
		<td class="dim" align=right></td>
				<td class="dim" align=right width=100>$0</td>
		
		<td></td>
	</tr>
		<tr id=extended_PeriodMacRevenue style="display: none;">
	
	<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://partner.steampowered.com/package/mac/">Mac revenue</a> 		</td>
	<td></td>
	<td align=right>$0</td>
	<td align=right></td>
				<td align=right>$0</td>
		
				<td align=right><span style="color:#BD4D18;"></span></td>
		
		<td width=16></td>
		<td class="dim" align=right>$0</td>
		<td class="dim" align=right></td>
				<td class="dim" align=right width=100>$0</td>
		
		<td></td>
	</tr>
		<tr id=extended_PeriodLinuxRevenue style="display: none;">
	
	<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://partner.steampowered.com/package/linux/">Linux revenue</a> 		</td>
	<td></td>
	<td align=right>$0</td>
	<td align=right></td>
				<td align=right>$0</td>
		
				<td align=right><span style="color:#BD4D18;"></span></td>
		
		<td width=16></td>
		<td class="dim" align=right>$0</td>
		<td class="dim" align=right></td>
				<td class="dim" align=right width=100>$0</td>
		
		<td></td>
	</tr>
		<tr id=extended_PeriodSoundtrackRevenue style="display: none;">
	
	<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://partner.steampowered.com/package/music/">Soundtrack revenue</a> 		</td>
	<td></td>
	<td align=right>$0</td>
	<td align=right></td>
				<td align=right>$0</td>
		
				<td align=right><span style="color:#BD4D18;"></span></td>
		
		<td width=16></td>
		<td class="dim" align=right>$0</td>
		<td class="dim" align=right></td>
				<td class="dim" align=right width=100>$0</td>
		
		<td></td>
	</tr>
		<tr id=extended_PeriodSoftwareRevenue style="display: none;">
	
	<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://partner.steampowered.com/package/software/">Software revenue</a> 		</td>
	<td></td>
	<td align=right>$0</td>
	<td align=right></td>
				<td align=right>$0</td>
		
				<td align=right><span style="color:#BD4D18;"></span></td>
		
		<td width=16></td>
		<td class="dim" align=right>$0</td>
		<td class="dim" align=right></td>
				<td class="dim" align=right width=100>$0</td>
		
		<td></td>
	</tr>
		<tr id=extended_PeriodVideoRevenue style="display: none;">
	
	<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://partner.steampowered.com/package/video/">Video revenue</a> 		</td>
	<td></td>
	<td align=right>$0</td>
	<td align=right></td>
				<td align=right>$0</td>
		
				<td align=right><span style="color:#BD4D18;"></span></td>
		
		<td width=16></td>
		<td class="dim" align=right>$0</td>
		<td class="dim" align=right></td>
				<td class="dim" align=right width=100>$0</td>
		
		<td></td>
	</tr>
		<tr id=extended_PeriodVRRevenue style="display: none;">
	
	<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a title="Some titles may also support non-VR play" href="https://partner.steampowered.com/package/vr/">VR revenue</a> 		</td>
	<td></td>
	<td align=right>$0</td>
	<td align=right></td>
				<td align=right>$0</td>
		
				<td align=right><span style="color:#BD4D18;"></span></td>
		
		<td width=16></td>
		<td class="dim" align=right>$0</td>
		<td class="dim" align=right></td>
				<td class="dim" align=right width=100>$0</td>
		
		<td></td>
	</tr>
		<tr id=extended_PeriodBundleRevenue style="display: none;">
	
	<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Bundle revenue 		</td>
	<td></td>
	<td align=right>$0</td>
	<td align=right></td>
				<td align=right>$0</td>
		
				<td align=right><span style="color:#BD4D18;"></span></td>
		
		<td width=16></td>
		<td class="dim" align=right>$0</td>
		<td class="dim" align=right></td>
				<td class="dim" align=right width=100>$0</td>
		
		<td></td>
	</tr>
		<tr style="border-top-style:solid; border-top-width:1px;" >
	
	<td><a href="https://partner.steampowered.com/package/steam/">Total units</a> 		</td>
	<td></td>
	<td align=right>275</td>
	<td align=right></td>
				<td align=right>9</td>
		
				<td align=right><span style="color:#B5DB42;">+12%</span></td>
		
		<td width=16></td>
		<td class="dim" align=right>245</td>
		<td class="dim" align=right></td>
				<td class="dim" align=right width=100>8</td>
		
		<td></td>
	</tr>
		<tr >
	
	<td>&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://partner.steampowered.com/package/steam/">Steam units</a> 		</td>
	<td></td>
	<td align=right>0</td>
	<td align=right></td>
				<td align=right>0</td>
		
				<td align=right><span style="color:#BD4D18;"></span></td>
		
		<td width=16></td>
		<td class="dim" align=right>0</td>
		<td class="dim" align=right></td>
				<td class="dim" align=right width=100>0</td>
		
		<td></td>
	</tr>
		<tr id=extended_PeriodRemainingUnits style="display: none;">
	
	<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Remaining units 		</td>
	<td></td>
	<td align=right>0</td>
	<td align=right></td>
				<td align=right>0</td>
		
				<td align=right><span style="color:#BD4D18;"></span></td>
		
		<td width=16></td>
		<td class="dim" align=right>0</td>
		<td class="dim" align=right></td>
				<td class="dim" align=right width=100>0</td>
		
		<td></td>
	</tr>
		<tr id=extended_PeriodMacUnits style="display: none;">
	
	<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://partner.steampowered.com/package/mac/">Mac units</a> 		</td>
	<td></td>
	<td align=right>0</td>
	<td align=right></td>
				<td align=right>0</td>
		
				<td align=right><span style="color:#BD4D18;"></span></td>
		
		<td width=16></td>
		<td class="dim" align=right>0</td>
		<td class="dim" align=right></td>
				<td class="dim" align=right width=100>0</td>
		
		<td></td>
	</tr>
		<tr id=extended_PeriodLinuxUnits style="display: none;">
	
	<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://partner.steampowered.com/package/linux/">Linux units</a> 		</td>
	<td></td>
	<td align=right>0</td>
	<td align=right></td>
				<td align=right>0</td>
		
				<td align=right><span style="color:#BD4D18;"></span></td>
		
		<td width=16></td>
		<td class="dim" align=right>0</td>
		<td class="dim" align=right></td>
				<td class="dim" align=right width=100>0</td>
		
		<td></td>
	</tr>
		<tr id=extended_PeriodSoundtrackUnits style="display: none;">
	
	<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://partner.steampowered.com/package/music/">Soundtrack units</a> 		</td>
	<td></td>
	<td align=right>0</td>
	<td align=right></td>
				<td align=right>0</td>
		
				<td align=right><span style="color:#BD4D18;"></span></td>
		
		<td width=16></td>
		<td class="dim" align=right>0</td>
		<td class="dim" align=right></td>
				<td class="dim" align=right width=100>0</td>
		
		<td></td>
	</tr>
		<tr id=extended_PeriodSoftwareUnits style="display: none;">
	
	<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://partner.steampowered.com/package/software/">Software units</a> 		</td>
	<td></td>
	<td align=right>0</td>
	<td align=right></td>
				<td align=right>0</td>
		
				<td align=right><span style="color:#BD4D18;"></span></td>
		
		<td width=16></td>
		<td class="dim" align=right>0</td>
		<td class="dim" align=right></td>
				<td class="dim" align=right width=100>0</td>
		
		<td></td>
	</tr>
		<tr id=extended_PeriodVideoUnits style="display: none;">
	
	<td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://partner.steampowered.com/package/video/">Video units</a> 		</td>
	<td></td>
	<td align=right>0</td>
	<td align=right></td>
				<td align=right>0</td>
		
				<td align=right><span style="color:#BD4D18;"></span></td>
		
		<td width=16></td>
		<td class="dim" align=right>0</td>
		<td class="dim" align=right></td>
				<td class="dim" align=right width=100>0</td>
		
		<td></td>
	</tr>
		<tr style="border-bottom-style:solid; border-bottom-width:1px;" >
	
	<td>&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://partner.steampowered.com/package/retail/">Retail activations</a> 		</td>
	<td></td>
	<td align=right>275</td>
	<td align=right></td>
			<td></td>
		
				<td align=right><span style="color:#B5DB42;">+12%</span></td>
		
		<td width=16></td>
		<td class="dim" align=right>245</td>
		<td class="dim" align=right></td>
				<td></td>
		
		<td></td>
	</tr>
</table>
							<br><br>
							<div style="text-align:left;">
										<b>
			SUPERHOT			 units sold						 ( <form action="https://partner.steampowered.com/report_csv.php" method="post"><input type="hidden" name="file" value="SUPERHOT"><input type="hidden" name="params" value="query=QueryPackageSalesForCSV^pkgID=50666^dateStart=2021-11-01^dateEnd=2021-11-30^interpreter=PartnerSalesReportInterpreter"><input style="background: none!important;border: none;padding: 0!important; text-decoration: underline; cursor: pointer;" type="submit" value="view as .csv"/></form> )		</b>
		<br>
		<br>
		<div style=\"float: left;\" >
				<div class="graphControls">
			<a href="javascript:jsChartUnitsHistoryunits.getOptions().shiftRange(100, true)">&lt;&lt;</a>&nbsp;
			<a href="javascript:jsChartUnitsHistoryunits.getOptions().shiftRange(33, true)">&lt;</a>
			&nbsp;&nbsp;
			<a href="javascript:jsChartUnitsHistoryunits.getOptions().shiftRange(33, false)">&gt;</a>&nbsp;
			<a href="javascript:jsChartUnitsHistoryunits.getOptions().shiftRange(100, false)">&gt;&gt;</a>&nbsp;
			<a href="javascript:jsChartUnitsHistoryunits.getOptions().updateRange(0,'now')">&gt;|</a>
			&nbsp;&nbsp;&nbsp;&nbsp;
			<a href="javascript:jsChartUnitsHistoryunits.getOptions().zoom(50);">[ + ]</a>
			<a href="javascript:jsChartUnitsHistoryunits.getOptions().zoom(200);">[ - ]</a>
			&nbsp;&nbsp;
		<span id="ChartUnitsHistoryunits_ranges" style="">
					<a href="javascript:jsChartUnitsHistoryunits.getOptions().updateRange(1,'year')">1y</a>
			<a href="javascript:jsChartUnitsHistoryunits.getOptions().updateRange(3,'month')">3m</a>
			<a href="javascript:jsChartUnitsHistoryunits.getOptions().updateRange(1,'month')">1m</a>
			<a href="javascript:jsChartUnitsHistoryunits.getOptions().updateRange(2,'week')">2w</a>
			<a href="javascript:jsChartUnitsHistoryunits.getOptions().updateRange(1,'week')">1w</a>
			<a href="javascript:jsChartUnitsHistoryunits.getOptions().updateRange(3,'day')">3d</a>
			&nbsp;&nbsp;&nbsp;
		</span>
			<a href="#" id="ChartUnitsHistoryunits_embiggen"
			   onclick="EnlargeFlotChart( 'ChartUnitsHistoryunits', jsChartUnitsHistoryunits, 1100, 312 ); return false">enhance</a>
			<a href="#" id="ChartUnitsHistoryunits_restore" style="display:none;"
			   onclick="RestoreFlotChart( 'ChartUnitsHistoryunits', jsChartUnitsHistoryunits, 700, 160 );;return false">unenhance</a>
			<div style="clear: both;"></div>
		</div>
		<div><div id="ChartUnitsHistoryunits" class="JSGraph" style="width: 700px;height: 160px; margin-bottom:40px;" align="center"; >
		</div><div class="LoadingBox" >Loading ...</div></div><script type="text/javascript">
			var jsChartUnitsHistoryunits;
			jsonpFetch( "https://partner.steampowered.com//report_xml.php?query=QuerySteamHistory&pkgID=50666&type=units&dateStart=2021-11-01&dateEnd=2021-11-30&json=1&jsid=jsChartUnitsHistoryunits&full_timestamps=1&width=700&height=160&func=onJsonData0", "onJsonData0", onJsonData0 );
			//console.log( "https://partner.steampowered.com//report_xml.php?query=QuerySteamHistory&pkgID=50666&type=units&dateStart=2021-11-01&dateEnd=2021-11-30&json=1&jsid=jsChartUnitsHistoryunits&full_timestamps=1&width=700&height=160&func=onJsonData0" );
		function onJsonData0( data )
		{
			var font_desc =  {
					size: 11,
					lineHeight: 13,
					style: "normal",
					weight: "normal",
					family: "Verdana, Arial, Helvetica, sans-serif",
					variant: "normal",
					color: "#a3a3a3"
				};


		jsChartUnitsHistoryunits = jQuery.plot( jQuery( "#ChartUnitsHistoryunits" ), data, {
			series: {
				
				lines: {
					show: true,
					
				},
				bars: {
					show: false,
				},
				pie: {
					show: false,
					stroke: { 
					    color: "#424242",
					    width: 0,
                    }
				},
			},
			legend: {
				show: false,
				margin: 0,
				labelBoxBorderColor: "none",
				backgroundColor: "#545454",
				position:"ne",

			},
			yaxis: {
				show: true,
				tickFormatter: (function (d) {
					return d.toLocaleString();
				}),
				tickColor: "#424242",
				autoscaleMargin: 0.15,
				font: font_desc,
				
			},
			xaxis: {
				font: font_desc,
				labelWidth: 100,
				reserveSpace: 80,
				show: true,
				
				
			mode: "time",
			minTickSize: [1, "hour"], 
			tickFormatter:
			(function (val, axis) {
				var currentRange = axis.max - axis.min;
				var	axisVal = new Date(val);
				if ( currentRange > 1000*60*60*24*365 )
					return axisVal.toLocaleDateString("en-US", { year: '2-digit', month: 'short' });
				else if ( currentRange > 1000*60*60*24*2 )
					return axisVal.toLocaleDateString("en-US", {month: 'short', day:'2-digit' });
				else
					return axisVal.toLocaleDateString("en-US", {month: 'short', day:'2-digit' }) + '<br>'
						   + axisVal.toLocaleTimeString("en-US", { hour: '2-digit', minute:'2-digit'});
				}),
			
			},
			grid: {
				hoverable: true,
				clickable: false,
				margin: 0,
				borderWidth: 0,
				axisMargin: 0,
				minBorderMargin: 0,
				mouseActiveRadius: 1000,
				backgroundColor: '#222222'
			},
			minDate:'2016-02-01',
dateStart:1635750000000,
dateEnd:1638259200000,
id:'ChartUnitsHistoryunits',
query_params:{"query":"QuerySteamHistory","pkgID":50666,"type":"units","dateStart":"2021-11-01","dateEnd":"2021-11-30","json":1,"jsid":"jsChartUnitsHistoryunits","full_timestamps":true,"width":700,"height":160,"func":"onJsonData0"},

		});

		jsChartUnitsHistoryunits.getOptions().query_params["func"] = "onJsonDataGraphReload0";

		jQuery( "#ChartUnitsHistoryunits" ).addClass( "line" );
		AddFunctionsToPlot( jsChartUnitsHistoryunits, "https://partner.steampowered.com//report_xml.php?", "ChartUnitsHistoryunits", onJsonDataGraphReload0, false, false );
	}

	function onJsonDataGraphReload0( data )
	{
		jsChartUnitsHistoryunits.setData( data );
		jsChartUnitsHistoryunits.getOptions().updatePlotAxis( jsChartUnitsHistoryunits );

		jQuery( "#ChartUnitsHistoryunits" ).parent().removeClass( "loading" );
	}
</script>
				</div>
		<div style="clear:both;"></div>
									</div>
							<br clear="all">
									<div style="width:1000px" class="regionCtn">
			<div style="float:left;">
				<b class="regionTitle"><a href="https://partner.steampowered.com/region/?subID=50666" class="graph_header">Sales & activations by region</a>, 11/1/2021 to 11/30/2021</b> 				<div><div id="chartdiv1" class="JSGraph" style="width: 400px;height: 280px; margin-bottom:40px;" align="center"; >
		</div><div class="LoadingBox" >Loading ...</div></div><script type="text/javascript">
			var jsgraph1;
			jsonpFetch( "https://partner.steampowered.com//report_xml.php?query=QuerySortedRegionalBreakdownForPeriod&pkgID=50666&type=allunits&field=region&piechart=1&width=400&height=280&json=1&func=onJsonData1", "onJsonData1", onJsonData1 );
			//console.log( "https://partner.steampowered.com//report_xml.php?query=QuerySortedRegionalBreakdownForPeriod&pkgID=50666&type=allunits&field=region&piechart=1&width=400&height=280&json=1&func=onJsonData1" );
		function onJsonData1( data )
		{
			var font_desc =  {
					size: 11,
					lineHeight: 13,
					style: "normal",
					weight: "normal",
					family: "Verdana, Arial, Helvetica, sans-serif",
					variant: "normal",
					color: "#a3a3a3"
				};


		jsgraph1 = jQuery.plot( jQuery( "#chartdiv1" ), data, {
			series: {
				
				lines: {
					show: false,
					
				},
				bars: {
					show: false,
				},
				pie: {
					show: true,
					stroke: { 
					    color: "#424242",
					    width: 0,
                    }
				},
			},
			legend: {
				show: true,
				margin: 0,
				labelBoxBorderColor: "none",
				backgroundColor: "#545454",
				position:"ne",

			},
			yaxis: {
				show: true,
				tickFormatter: (function (d) {
					return d.toLocaleString();
				}),
				tickColor: "#424242",
				autoscaleMargin: 0.15,
				font: font_desc,
				
			},
			xaxis: {
				font: font_desc,
				labelWidth: 100,
				reserveSpace: 80,
				show: true,
				ticks: data[0]['ticks'],
				
			},
			grid: {
				hoverable: true,
				clickable: false,
				margin: 0,
				borderWidth: 0,
				axisMargin: 0,
				minBorderMargin: 0,
				mouseActiveRadius: 1000,
				backgroundColor: '#222222'
			},
			query_params:{"query":"QuerySortedRegionalBreakdownForPeriod","pkgID":50666,"type":"allunits","field":"region","piechart":1,"width":400,"height":280,"json":1,"func":"onJsonData1"},

		});

		jsgraph1.getOptions().query_params["func"] = "onJsonDataGraphReload1";

		jQuery( "#chartdiv1" ).addClass( "pie" );
		AddFunctionsToPlot( jsgraph1, "https://partner.steampowered.com//report_xml.php?", "chartdiv1", onJsonDataGraphReload1, false, true );
	}

	function onJsonDataGraphReload1( data )
	{
		jsgraph1.setData( data );
		jsgraph1.getOptions().updatePlotAxis( jsgraph1 );

		jQuery( "#chartdiv1" ).parent().removeClass( "loading" );
	}
</script>
					</div>
						<div style="float:right;">
				<table>
					<thead>
						<th align="left">Country</th>
						<th width=100px align=right>Units</th>
						<th></th>
						<th width=100px align=right>Percent of Total</th>
					</thead>
					<tbody>
															<tr>
										<td><a href="https://partner.steampowered.com/country.php?countryCode=US&amp;dateStart=2021-11-01&amp;dateEnd=2021-11-30">United States</a></td>
										<td align=right>111</td>
										<td align=right></td>
										<td align=right>40%</td>
									</tr>
																		<tr>
										<td><a href="https://partner.steampowered.com/country.php?countryCode=DE&amp;dateStart=2021-11-01&amp;dateEnd=2021-11-30">Germany</a></td>
										<td align=right>23</td>
										<td align=right></td>
										<td align=right>8%</td>
									</tr>
																		<tr>
										<td><a href="https://partner.steampowered.com/country.php?countryCode=AU&amp;dateStart=2021-11-01&amp;dateEnd=2021-11-30">Australia</a></td>
										<td align=right>17</td>
										<td align=right></td>
										<td align=right>6%</td>
									</tr>
																		<tr>
										<td><a href="https://partner.steampowered.com/country.php?countryCode=GB&amp;dateStart=2021-11-01&amp;dateEnd=2021-11-30">United Kingdom</a></td>
										<td align=right>13</td>
										<td align=right></td>
										<td align=right>5%</td>
									</tr>
																		<tr>
										<td><a href="https://partner.steampowered.com/country.php?countryCode=CA&amp;dateStart=2021-11-01&amp;dateEnd=2021-11-30">Canada</a></td>
										<td align=right>11</td>
										<td align=right></td>
										<td align=right>4%</td>
									</tr>
																		<tr>
										<td><a href="https://partner.steampowered.com/country.php?countryCode=PL&amp;dateStart=2021-11-01&amp;dateEnd=2021-11-30">Poland</a></td>
										<td align=right>8</td>
										<td align=right></td>
										<td align=right>3%</td>
									</tr>
																		<tr>
										<td><a href="https://partner.steampowered.com/country.php?countryCode=NL&amp;dateStart=2021-11-01&amp;dateEnd=2021-11-30">Netherlands</a></td>
										<td align=right>8</td>
										<td align=right></td>
										<td align=right>3%</td>
									</tr>
																		<tr>
										<td><a href="https://partner.steampowered.com/country.php?countryCode=FR&amp;dateStart=2021-11-01&amp;dateEnd=2021-11-30">France</a></td>
										<td align=right>7</td>
										<td align=right></td>
										<td align=right>3%</td>
									</tr>
																		<tr>
										<td><a href="https://partner.steampowered.com/country.php?countryCode=SG&amp;dateStart=2021-11-01&amp;dateEnd=2021-11-30">Singapore</a></td>
										<td align=right>6</td>
										<td align=right></td>
										<td align=right>2%</td>
									</tr>
																		<tr>
										<td><a href="https://partner.steampowered.com/country.php?countryCode=HU&amp;dateStart=2021-11-01&amp;dateEnd=2021-11-30">Hungary</a></td>
										<td align=right>6</td>
										<td align=right></td>
										<td align=right>2%</td>
									</tr>
																	<tr>
									<td>Other</td>
									<td align=right>65</td>
									<td align=right></td>
									<td align=right>24%</td>
								</tr>
														<tr style="border-top-style:solid; border-top-width:1px border-color: white;">
							<td>Total</td>
							<td align="right">275</td>
															<td></td>
														<td></td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
										<br clear="all">

												<br clear="all">
				</div>
			</div>
		</center>
	</body>
</html>
