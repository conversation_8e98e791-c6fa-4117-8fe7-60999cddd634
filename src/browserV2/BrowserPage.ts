import axios, {AxiosRequestConfig} from 'axios';
import {StatusCodes} from 'http-status-codes';
import * as _ from 'lodash';
import {TimeoutError} from 'puppeteer';
import {SelectorMatch, WaitForAnyOptions, waitForAnySelector} from '../browser/browserMethods';
import {downloadFileUsingElementHandle} from '../browser/download';
import {CustomException} from '../error/CustomException';
import {errorTypes} from '../error/errorTypes';
import {DownloadFailedDueToExternalServiceIssuesException, RequestFailedException} from '../error/exceptions';
import {UnableToLoadUrlException} from '../error/exceptions/UnableToLoadUrlException';
import {CDPSession, PuppeteerPage, Response} from '../puppeteer';
import {Session} from '../scrapersV2/Scraper';
import {HttpMethod} from '../utils/http';
import {log} from '../utils/logger';
import {isValidBrowserSession} from './sessionValidators';
import {BrowserSession} from './types';

const defaultSelectorTimeout = 60000;
const goToDefaultTimeout = 300000;
const gotoMaxRetriesOnError = 5;
const defaultDownloadTimeout = 900000;

interface SelectorTestOptions {
    successSelector?: string;
    failureSelector?: string;
    successXPath?: string;
    failureXPath?: string;
    timeout?: number;
    visible?: boolean;
}

interface SelectorTestMultiOptions {
    successSelectors?: string[];
    failureSelectors?: string[];
    successXPaths?: string[];
    failureXPaths?: string[];
    timeout?: number;
    visible?: boolean;
}

interface BrowserPageRequestOptions {
    data?: unknown;
    options?: AxiosRequestConfig;
    responseValidator?(response: unknown): void;
}

export class BrowserPage {
    readonly page: PuppeteerPage;
    readonly client: CDPSession;

    constructor(page: PuppeteerPage, client: CDPSession) {
        this.page = page;
        this.client = client;
    }

    public async waitForAnySelector({cssSelectors = [], xPaths = [], timeout = defaultSelectorTimeout, visible = true}: WaitForAnyOptions): Promise<SelectorMatch> {
        return waitForAnySelector({cssSelectors, xPaths, timeout, visible}, this.page);
    }

    public async getSession(): Promise<BrowserSession> {
        return (await this.client.send('Network.getAllCookies')) as BrowserSession;
    }

    public async setSession(session: Session): Promise<void> {
        if (isValidBrowserSession(session)) {
            await this.page.setCookie(...(session.cookies as any));
        }
    }

    public async goto(url: string, isOnErrorPageValidator?: (page: BrowserPage) => Promise<boolean>, retryCounter = 0): Promise<Response | null> {
        const response = await this.page.goto(url, {timeout: goToDefaultTimeout});
        if (!isOnErrorPageValidator || !(await isOnErrorPageValidator(this))) {
            return response;
        }
        log.debug('Error page detected', {url, retryCounter});
        if (retryCounter < gotoMaxRetriesOnError) {
            log.debug('Retrying', {url, retryCounter});
            return this.goto(url, isOnErrorPageValidator, retryCounter + 1);
        }
        throw new UnableToLoadUrlException(url);
    }

    /**
     * If none of the specified selectors will be found then a timeout error will be thrown.
     */
    public async selectorTest({successSelector, failureSelector, successXPath, failureXPath, timeout, visible}: SelectorTestOptions): Promise<boolean> {
        return this.selectorTestMulti({
            successSelectors: successSelector ? [successSelector] : undefined,
            successXPaths: successXPath ? [successXPath] : undefined,
            failureSelectors: failureSelector ? [failureSelector] : undefined,
            failureXPaths: failureXPath ? [failureXPath] : undefined,
            timeout,
            visible
        });
    }

    public async selectorTestMulti({
        successSelectors = [],
        failureSelectors = [],
        successXPaths = [],
        failureXPaths = [],
        timeout,
        visible
    }: SelectorTestMultiOptions): Promise<boolean> {
        try {
            const selectors = [...successSelectors, ...failureSelectors];
            const xPaths = [...successXPaths, ...failureXPaths];
            const [selector] = await this.waitForAny({cssSelectors: selectors, xPaths, timeout, visible});

            return successSelectors.includes(selector) || successXPaths.includes(selector);
        } catch (error) {
            throw new CustomException({message: 'Timeout - none of expected elements has been found', error, errorType: errorTypes.TEMPORARY_PORTAL_ISSUE});
        }
    }

    public async click(selector: string): Promise<void> {
        await this.page.waitForSelector(selector, {timeout: defaultSelectorTimeout, visible: true});
        await this.page.click(selector);
    }

    public async clickByXPath(xPath: string): Promise<void> {
        const element = await this.page.waitForSelector(`::-p-xpath(${xPath})`, {timeout: defaultSelectorTimeout, visible: true});
        await element?.click();
    }

    public async downloadByXPathSelector(xPath: string, fileName): Promise<void> {
        const element = await this.page.waitForSelector(`::-p-xpath(${xPath})`, {timeout: defaultSelectorTimeout, visible: true});
        await downloadFileUsingElementHandle(this.page, element!, fileName);
    }

    public async clickIfExists(selector: string): Promise<void> {
        if ((await this.page.$(selector)) !== null) {
            await this.page.click(selector);
        }
    }

    public async input(selector: string, text: string): Promise<void> {
        await this.page.waitForSelector(selector, {timeout: defaultSelectorTimeout, visible: true});
        // clicking three times in a text fields acts as a "select all" command
        await this.page.click(selector, {clickCount: 3});
        await this.page.type(selector, text);
    }

    public async text(selector: string, visible = true): Promise<string> {
        return this.getElementProperty(selector, 'textContent', visible);
    }

    public async getElementProperty(selector: string, property: string, visible = true): Promise<string> {
        const element = await this.page.waitForSelector(selector, {timeout: defaultSelectorTimeout, visible});
        if (!element) {
            throw new TimeoutError(`Selector '${selector}' not found.`);
        }
        const text = await (await element.getProperty(property)).jsonValue();
        return text as string;
    }

    public async checkSelectorExists(selector: string): Promise<boolean> {
        return (await this.page.$(selector)) !== null;
    }

    public async waitForAny({cssSelectors = [], xPaths = [], timeout = defaultSelectorTimeout, visible = true}: WaitForAnyOptions): Promise<SelectorMatch> {
        return waitForAnySelector({cssSelectors: cssSelectors, xPaths, timeout, visible}, this.page);
    }

    public async clickAndOpenPopup(selector: string): Promise<BrowserPage> {
        const popupPending: Promise<PuppeteerPage> = new Promise((resolve) => this.page.once('popup', () => resolve));
        await this.click(selector);
        const popup = await popupPending;
        return new BrowserPage(popup, this.client);
    }

    public url(): string {
        return this.page.url();
    }

    private async getCookieString(): Promise<string> {
        const cookies = await this.page.cookies();
        return cookies.map((cookie): string => `${cookie.name}=${cookie.value}; `).join('');
    }

    public async request<T = any>(method: HttpMethod, url: string, {data, options, responseValidator}: BrowserPageRequestOptions = {}): Promise<T> {
        const cookieString = await this.getCookieString();

        try {
            const response = await axios({
                url,
                method,
                data,
                headers: {Cookie: cookieString},
                timeout: defaultDownloadTimeout,
                ...options
            });
            responseValidator?.(response);
            return response.data as T;
        } catch (error) {
            const status = _.get(error, 'response.status', null);
            if (status >= StatusCodes.INTERNAL_SERVER_ERROR) {
                throw new DownloadFailedDueToExternalServiceIssuesException({reportId: url, status, data: error.response?.data});
            }
            throw new RequestFailedException(error);
        }
    }
}
