import * as AdmZip from 'adm-zip';
import {keyBy, mapValues} from 'lodash';
import {Moment} from 'moment/moment';
import * as asyncPool from 'tiny-async-pool';
import {BrowserSession} from '../../../../browserV2';
import {Source} from '../../../../dataTypes';
import {Report} from '../../../../scrapersV1/Report';
import {FileExtension} from '../../../../utils/files/FileExtension';
import {buildFilePath, generateFileName} from '../../../../utils/files/fileUtils';
import {HTTPClient} from '../../../../utils/http';
import {log} from '../../../../utils/logger';
import {ProgressCallback} from '../../../ProgressCallback';
import {IReportManifest} from '../../../ReportManifest';
import {ScrapeFunction} from '../../../Scraper';
import {FileMetaData} from '../../common/FileMetaData';
import {SteamParams, getLoginFunction} from '../../common/login';
import {SteamAdditionalData, steamDateFormat} from '../../common/requests';
import {steamScraperWrapper} from '../../common/scraperWrapper';
import {checkSteamGamesLogin, checkSteamGamesSession} from '../checkSession';
import {getSourceSideOrganizations} from '../getSourceSideOrganizations';
import getFullSetOfDataToScrape from '../impressionScrapingMetadata';
import {SteamAppData} from '../SteamAppData';
import {downloadSteamGamesFile, steamGamesCheerioRequest, steamGamesUrl} from '../steamGamesRequest';
import {extractChecksum} from '../steamImpressionsChecksum';

const workerPoolSize = 2;

interface appDownloadScrapeContext {
    appsWithDates: SteamAppData[];
    progress: ProgressCallback;
    httpClient: HTTPClient;
    zip: AdmZip;
}

export interface DownloadData {
    fileName: string;
    fileMetaData: FileMetaData;
}

const getAppDownloadUrl = (appId: string, date: Moment): string => {
    const slash = '%2F';
    const dateString = `${date.month() + 1}${slash}${date.date()}${slash}${date.year()}`;
    const dateQuery = `preset_date_range=custom&start_date=${dateString}&end_date=${dateString}`;
    return `/apps/navtrafficstats/${appId}?attribution_filter=all&${dateQuery}&format=csv`;
};

function impressionsSiteUrl(packageId: string, startDate: Moment, endDate: Moment): string {
    return `/apps/navtrafficstats/${packageId}?attribution_filter=all&preset_date_range=custom&start_date=${startDate.format(steamDateFormat)}&end_date=${endDate.format(
        steamDateFormat
    )}`;
}

async function downloadReportsForApps({
    appsWithDates,
    httpClient,
    zip,
    progress
}: appDownloadScrapeContext): Promise<{fileMetaData: {[p: string]: FileMetaData}; fileNames: string[]}> {
    let fileCounter = 0;
    const requestFun = async (appData: SteamAppData): Promise<DownloadData> => {
        progress(`Downloading: ${JSON.stringify(appData)}`);
        const fileName = generateFileName(Source.STEAM_IMPRESSIONS, appData.date, appData.date, FileExtension.CSV, appData.index);
        const downloadLink = getAppDownloadUrl(appData.id, appData.date);
        const checksumSiteUrl = impressionsSiteUrl(appData.id, appData.date, appData.date);

        // we want to keep these download calls as close to being simultaneous as possible to reduce the risk of live data
        // update causing checksum vs report differences
        const [downloadResponse, checksumPage] = await Promise.all([
            downloadSteamGamesFile(httpClient, downloadLink, appData.name),
            steamGamesCheerioRequest(checksumSiteUrl, {httpClient})
        ]);

        zip.addFile(fileName, Buffer.from(downloadResponse.data, 'utf8'));

        fileCounter++;
        informUserWithProgress(fileCounter, appsWithDates.length);
        const checksum = extractChecksum(checksumPage);
        if (!checksum) {
            log.debug("[STEAM_IMPRESSIONS] Couldn't find total visits or games", {...appData});
        }
        return {
            fileName,
            fileMetaData: {
                productId: appData.id,
                productName: appData.name,
                date: appData.date.startOf('day').format(),
                checksum: checksum ? checksum : undefined
            }
        };
    };

    const files = await asyncPool<SteamAppData, DownloadData>(workerPoolSize, appsWithDates, requestFun); //TODO this can lie and swallow exceptions

    return {
        fileMetaData: mapValues(keyBy(files, 'fileName'), 'fileMetaData'),
        fileNames: files.map((item) => item.fileName)
    };
}

function informUserWithProgress(done: number, goal: number, granularity = 1): void {
    const percent = (100 * done) / goal;
    const percentStr = percent.toFixed(2);
    if (done % granularity === 0 || done === goal) {
        log.info(`${percentStr}% done`);
    }
}

const scrape: ScrapeFunction<SteamParams, BrowserSession> = async ({progress, getHttpClient, params}, startDate, endDate) => {
    const httpClient = getHttpClient();
    const appsWithDates = await getFullSetOfDataToScrape({httpClient, progress, startDate, endDate, ignoredProducts: params.ignoredProducts || []});
    const zip = new AdmZip();
    const localScrapeContext: appDownloadScrapeContext = {
        progress,
        httpClient,
        appsWithDates,
        zip
    };
    const {fileMetaData, fileNames} = await downloadReportsForApps(localScrapeContext);
    addBackwardCompatibleManifestToZip(zip, startDate, endDate, fileMetaData);

    const zipFileName = generateFileName(Source.STEAM_IMPRESSIONS, startDate, endDate, FileExtension.ZIP);
    const zipFilePath = buildFilePath(zipFileName);
    zip.writeZip(zipFilePath);

    return [new Report(Source.STEAM_IMPRESSIONS, zipFileName, startDate, endDate, fileNames.length === 0)];
};

function addBackwardCompatibleManifestToZip(zip: AdmZip, startDate: Moment, endDate: Moment, fileMetaData: {[p: string]: FileMetaData}): void {
    const manifestFileDateFormat = 'YYYY-MM-DD';
    const manifest: IReportManifest & SteamAdditionalData = {
        dateFrom: startDate,
        dateTo: endDate,
        scraper: Source.STEAM_IMPRESSIONS,
        //@deprecated
        fileMetaData,
        startDate,
        endDate,
        metadata: fileMetaData
    };
    const manifestFileName = `additionalData_${manifest.scraper}_${startDate.format(manifestFileDateFormat)}_${endDate.format(manifestFileDateFormat)}.${
        FileExtension.JSON
    }`;

    zip.addFile(manifestFileName, Buffer.from(JSON.stringify(manifest), 'utf8'));
}

export const steamImpressionsScraper = steamScraperWrapper({
    login: getLoginFunction(Source.STEAM_IMPRESSIONS, checkSteamGamesLogin),
    scrape,
    getSourceSideOrganizations,
    checkSession: checkSteamGamesSession,
    manualLoginDetails: {
        url: `${steamGamesUrl}/dashboard`,
        successSelector: {value: '#landingWelcome', type: 'css'}
    }
});
