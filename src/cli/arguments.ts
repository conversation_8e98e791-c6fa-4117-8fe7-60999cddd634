import {ClassType, transformAndValidateSync} from 'class-transformer-validator';
import {IsBoolean, IsOptional, IsString, ValidationError} from 'class-validator';
import * as moment from 'moment';
import * as yargs from 'yargs';
import {Options} from 'yargs';
import {hideBin} from 'yargs/helpers';

export class CliSentryParams {
    @IsBoolean()
    readonly enabled: boolean;
    @IsString()
    readonly dsn: string;
    @IsOptional()
    readonly environment: string;
}

export enum Command {
    LOGIN = 'login',
    CHECK_SESSION = 'check-session',
    GET_SOURCE_SIDE_ORGANIZATIONS = 'get-source-side-organizations',
    SCRAPE = 'scrape',
    GET_MANUAL_LOGIN_DETAILS = 'get-manual-login-details',
    EXAMPLE_OUTPUT = 'example-output'
}

export interface CLIArguments extends Record<string, unknown> {
    source: string;
    sessionFile?: string;
    outputSessionFile?: string;
    credentials: Record<string, string>;
    excludedSkus?: string[];
    excludedOrgs?: string[];
    featureFlags?: string[];
    encrypt: boolean;
    encryptionToken: string;
    output?: string;
    apiUrl?: string;
    apiToken?: string;
    headless: boolean;
    chromePath?: string;
    from?: moment.Moment;
    to?: moment.Moment;
    reportPath?: string;
    command: Command;
    printTelemetry: boolean;
    proxyUrl?: string;
    dumpDir?: string;
    sentryParams?: CliSentryParams;
}

export function parseArguments(argv: string[], scriptName: string): CLIArguments {
    // the command line arguments definition configured here
    // should closely follow the launch interface defined in
    // docs/design/launchInterface.md.
    // when modifying the config below, make sure the spec is updated as well.

    const sessionFileInputDeclaration = (require: boolean): Options => ({
        type: 'string',
        demandOption: require,
        describe: 'File in which to store/load session data (cookies).'
    });

    const sourceInputDeclaration = (require: boolean): Options => ({
        type: 'string',
        demandOption: require,
        describe: 'The data source to scrape'
    });

    const scrapeOptionsDeclarations = (require: boolean): Record<string, Options> => ({
        source: sourceInputDeclaration(require),
        from: {
            type: 'string',
            demandOption: require,
            coerce: dateParse('from'),
            describe: 'Date from which to start scraping. (YYYY-MM-DD)'
        },
        to: {
            type: 'string',
            demandOption: require,
            coerce: dateParse('to'),
            describe: 'Date on which to stop scraping. (YYYY-MM-DD)'
        },
        reportPath: {
            type: 'string',
            demandOption: require,
            describe: 'The directory to which the report file should be downloaded.'
        }
    });

    const args = yargs(hideBin(argv))
        .help()
        .env('NDBI')
        .scriptName(scriptName)
        .options({
            source: sourceInputDeclaration(false),
            sessionFile: sessionFileInputDeclaration(false),
            outputSessionFile: {
                type: 'string',
                demandOption: false,
                describe: 'If provided, store updates to session data here rather than in sessionFile.'
            },
            credentials: {
                type: 'string',
                demandOption: false,
                coerce: credentialsParse,
                describe: 'A JSON-encoded dictionary of portal-specific credentials, used to create a session.'
            },
            encrypt: {
                type: 'boolean',
                demandOption: false,
                default: true,
                describe: 'If set, encrypt the session file with the encryption token.'
            },
            encryptionToken: {
                type: 'string',
                demandOption: false,
                describe: 'A secret used to encrypt/decrypt the session file. Ideally, should be machine-dependent, like cookie encryption in Google Chrome.'
            },
            output: {
                type: 'string',
                demandOption: false,
                default: 'json',
                describe: 'If `json`, formats log messages in JSON. If `text`, logs using plain text.',
                choices: ['json']
            },
            apiUrl: {type: 'string', demandOption: false, describe: 'URL used to query IndieBI Scraper API.'},
            apiToken: {type: 'string', demandOption: false, describe: 'JWT token used to query IndieBI Scraper API.'},
            headless: {
                type: 'boolean',
                demandOption: false,
                default: true,
                describe: 'If true, browser windows would not be shown.'
            },
            chromePath: {type: 'string', demandOption: false, describe: 'Path to the Chromium binary to use for Puppeteer.'},
            featureFlags: {
                type: 'string',
                demandOption: false,
                coerce: stringsArrayParse('featureFlags'),
                describe: 'JSON-encoded list of feature keys that should be enabled.'
            },
            dumpDir: {
                type: 'string',
                demandOption: false,
                describe: 'If set, create HTML/JSON dumps and screenshots in the given directory after a scraping error.'
            },
            printTelemetry: {
                type: 'boolean',
                demandOption: false,
                describe: 'If true, show telemetry data on standard output.'
            },
            proxyUrl: {
                type: 'string',
                demandOption: false,
                describe: 'URL of a scraper proxy to use.'
            },
            sentryParams: {
                type: 'string',
                demandOption: false,
                describe: 'JSON-encoded dictionary Sentry integration params',
                coerce: sentryParamsParse
            },
            ...scrapeOptionsDeclarations(false)
        })
        .command(Command.LOGIN, 'Using given credentials, log in to the portal and create a session file at the specified path.', {
            credentials: {
                type: 'string',
                demandOption: true,
                coerce: credentialsParse,
                describe: 'A JSON-encoded dictionary of portal-specific credentials, used to create a session.'
            },
            source: sourceInputDeclaration(true),
            sessionFile: sessionFileInputDeclaration(true)
        })
        .command(Command.CHECK_SESSION, 'Using cookies from the session file, check against the portal whether the session is valid.', {
            source: sourceInputDeclaration(true),
            sessionFile: sessionFileInputDeclaration(true)
        })
        .command(
            Command.GET_SOURCE_SIDE_ORGANIZATIONS,
            'Using cookies from the session file, scrape all source side organization names. If no organization is found (or multi orgs are not supported) then a default organization will be returned.',
            {
                source: sourceInputDeclaration(true),
                sessionFile: sessionFileInputDeclaration(true)
            }
        )
        .command(Command.GET_MANUAL_LOGIN_DETAILS, 'Get a JSON definition of manual login details for given source', {
            source: sourceInputDeclaration(true)
        })
        .command(Command.EXAMPLE_OUTPUT, 'Generate example of all possible outputs formats supported by the scraper.')
        .command(Command.SCRAPE, "Download a report using the given session file and a specified date range. Fail if not logged in, don't try to log in again.", {
            ...scrapeOptionsDeclarations(true),
            source: sourceInputDeclaration(true),
            sessionFile: sessionFileInputDeclaration(true),
            excludedSkus: {
                type: 'string',
                demandOption: false,
                coerce: stringsArrayParse('excludedSkus'),
                describe: 'A JSON-encoded array of Ids of the products that should NOT be downloaded by the scraper.'
            },
            excludedOrgs: {
                type: 'string',
                demandOption: false,
                coerce: stringsArrayParse('excludedOrgs'),
                describe: 'A JSON-encoded array of Ids of organizations that should NOT be included in the reports.'
            }
        })
        .demandCommand(1, 1)
        .wrap(yargs.terminalWidth())
        .parseSync();

    args.command = args._[0];
    /**
     * yargs requires the 'json' types that we use to be string types as well (because at parsing time they are strings)
     * in order to keep this necessary type pollution isolated this casting was introduced.
     */
    return args as unknown as CLIArguments;
}

function dateParse(option: string): (dateStr: string) => moment.Moment {
    return (dateStr: string) => {
        const format = 'YYYY-MM-DD';
        const date = moment.utc(dateStr, format, true);
        if (!date.isValid()) {
            throw new Error(`${option}: ${dateStr} could not be parsed as a date in a ${format} format.`);
        }
        return date;
    };
}

// eslint-disable-next-line @typescript-eslint/ban-types
function parseJsonWithClassValidation<T extends object>(errorPrefix: string, classType: ClassType<T>): (json: string) => T {
    return (json: string) => {
        // this check is required because yargs required:false parameters still run this function and without it cause a JSON parse error
        if (!json) {
            return null;
        }

        let transformedResult;
        try {
            transformedResult = transformAndValidateSync(classType, json);
        } catch (validationError) {
            if (Array.isArray(validationError)) {
                const msg = validationError.reduce((result: string, e: ValidationError) => `${result} ${e.property} has invalid value, `, '');
                throw new Error(`${errorPrefix}: ${msg}`);
            }
            throw validationError;
        }
        return Array.isArray(transformedResult) ? transformedResult[0] : transformedResult;
    };
}

const sentryParamsParse = parseJsonWithClassValidation('sentry-params', CliSentryParams);

function parseJsonWithErrorPrefix<T>(errorPrefix: string): (json: string) => T {
    return (json: string) => {
        // this check is required because yargs required:false parameters still run this function and without it cause a JSON parse error
        if (!json) {
            return null;
        }
        try {
            return JSON.parse(json);
        } catch (error) {
            throw new Error(`${errorPrefix}: ${error.message}`);
        }
    };
}

const credentialsParse = parseJsonWithErrorPrefix<Record<string, string>>('credentials');
const stringsArrayParse = (errorPrefix: string) => parseJsonWithErrorPrefix<string[]>(errorPrefix);
