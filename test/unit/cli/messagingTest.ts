import {deepStrictEqual} from 'assert';
import {AxiosError} from 'axios';
import {afterEach} from 'mocha';
import * as td from 'testdouble';
import {TestDouble} from 'testdouble';
import {
    _resetBasicPrintingParams,
    printDualAuth,
    printError,
    printOutput,
    printResult,
    printTrace,
    printTraceWithError,
    setBasicPrintingParams
} from '../../../src/cli/messaging';
import {Portal} from '../../../src/dataTypes';
import {CustomException} from '../../../src/error/CustomException';
import {errorTypes} from '../../../src/error/errorTypes';
import {maxSteamEmailCodeAttempts} from '../../../src/scrapersV2/steam/common/login';
import {LogLevel} from '../../../src/utils/logger';

describe('messaging should:', () => {
    const basicParams = {originId: 'testOrigin', source: 'testSource'};
    let consoleLogStub: TestDouble<Console['log']>;
    beforeEach(() => {
        setBasicPrintingParams(basicParams);
        consoleLogStub = td.replace(console, 'log');
    });
    afterEach(() => {
        td.reset();
        _resetBasicPrintingParams();
    });

    function verifyPrint(type: 'output' | 'event' | 'trace' | 'result' | 'error' | 'dualAuth', message?: string, logLevel: LogLevel = LogLevel.INFO, data?: object) {
        // Create a custom matcher that parses the JSON string and checks if it contains the expected properties
        td.verify(
            consoleLogStub(
                td.matchers.argThat((logArg: string) => {
                    try {
                        const parsedArg = JSON.parse(logArg);

                        // Check type
                        if (parsedArg.type !== type) {
                            return false;
                        }

                        // Check message if not result type
                        if (message && parsedArg.message !== message) {
                            return false;
                        }

                        // Check logLevel
                        if (parsedArg.logLevel !== logLevel) {
                            return false;
                        }

                        // Check timestamp exists
                        if (!parsedArg.timestamp) {
                            return false;
                        }

                        // Check basic params
                        for (const [key, value] of Object.entries(basicParams)) {
                            if (parsedArg[key] !== value) {
                                return false;
                            }
                        }

                        // Check data if provided
                        if (data) {
                            deepStrictEqual(parsedArg.data, data);
                        }

                        return true;
                    } catch {
                        return false;
                    }
                })
            )
        );
    }

    it('printOutput should contain all expected fields', () => {
        const message = 'test message';

        printOutput(message, LogLevel.WARN);

        verifyPrint('output', message, LogLevel.WARN);
    });

    it('printTrace should contain all expected fields', () => {
        const message = 'test message';

        const data = {test: 'data'};
        printTrace(message, data);

        verifyPrint('trace', message, LogLevel.INFO, data);
    });

    it('printTraceWithError should contain all expected fields', () => {
        const error = new Error('test error');

        printTraceWithError(error);

        verifyPrint('trace', error.message, LogLevel.INFO);
    });

    it('printTraceWithError should contain all expected fields for custom exception', () => {
        const error = new CustomException({
            message: 'test error',
            errorType: errorTypes.TEMPORARY_PORTAL_ISSUE,
            telemetryLogLevel: LogLevel.WARN,
            additionalErrorData: {test: 'test'}
        });

        printTraceWithError(error);

        verifyPrint('trace', error.message, LogLevel.INFO, {
            errorType: errorTypes.TEMPORARY_PORTAL_ISSUE,
            logLevel: LogLevel.WARN,
            additionalErrorData: {test: 'test'}
        });
    });

    it('printTraceWithError should contain all expected fields for axios error', () => {
        const error = new AxiosError('test error', 'ERR_NETWORK', undefined, undefined, undefined);
        printTraceWithError(error);
        verifyPrint('trace', error.message, LogLevel.INFO, {
            errorType: errorTypes.UNEXPECTED_ERROR,
            logLevel: LogLevel.ERROR,
            additionalErrorData: {request: {}, response: {}}
        });
    });

    it('printDualAuth should contain all expected fields', () => {
        const data = {portal: Portal.STEAM, attempt: 1, maxAttempts: maxSteamEmailCodeAttempts};
        printDualAuth(data);

        verifyPrint('dualAuth', undefined, LogLevel.INFO);
    });

    it('printError should contain all expected fields', () => {
        const error = new Error('test error');

        printError(error);

        verifyPrint('error', undefined, LogLevel.ERROR, {
            message: 'Unhandled exception occurred: test error',
            stack: error.stack
        });
    });

    it('printError should contain additional data when a custom exception with additional error data is passed', () => {
        const additionalErrorData = {asd: [1, 2, 4]};
        const exception = new CustomException({
            message: 'test',
            additionalErrorData,
            errorType: errorTypes.TEMPORARY_PORTAL_ISSUE
        });

        printError(exception);

        td.verify(
            consoleLogStub(
                td.matchers.argThat((logArg: string) => {
                    try {
                        const parsedArg = JSON.parse(logArg);
                        deepStrictEqual(parsedArg.data.additionalErrorData, additionalErrorData);
                        return true;
                    } catch {
                        return false;
                    }
                })
            )
        );
    });

    it('printResult should contain all expected fields', () => {
        const data = {result: 'kopytko'};

        printResult(data);

        verifyPrint('result', undefined, LogLevel.INFO, data);
    });
});
