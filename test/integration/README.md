# Integration tests

Integration tests will fire automatically on Merge Requests.

To speed them up try putting the long-running tests in separate files and adding proper prefixes in the gitlab run integration tests job description.
We tried adding the --parallel parameter to mocha, but it's in direct conflict with the file parameter that we specify 

## How to run locally

Execute from project's base directory (replace `{portal}/{case}` with desired test case for which you want to run tests for).  You can take the list of cases by taking the look at test files with following pattern `{portal}/{case}Test.ts`

```shell
./ci-cd/debug-integration-test.sh {portal}/{case}
```

Example usage:

```shell
cd ../.. # changing dir since this needs to be run from project root directory
./ci-cd/debug-integration-test.sh steam/scraper
```
OR
```shell
cd ../.. # changing dir since this needs to be run from project root directory
./ci-cd/debug-integration-test.sh steam/salesLowPermissionScraper
```

it is possible to run multiple tests with a wildcard:

```shell
./ci-cd/debug-integration-test.sh steam/*
./ci-cd/debug-integration-test.sh */checkSession
```

## Updating sessions or credentials
Sessions and credentials are stored under version control in an encrypted form. 
When integration tests start, they will
1) decrypt the file with credentials and load its content into memory
2) decrypt the file with sessions, split its contents to sessions files and store all sessions as separate files in `.private/integration_tests_sessions`.
> **_WARNING!_** Anything you (or the test run) update in `.private/integration_tests_sessions` will get overwritten with the decrypted contents of `encrypted_sessions.blob` next time you run any of the integration tests.

To make the change persistent, follow the section below.

### Updating sessions
1) Run the test you want using `./ci-cd/run-integration-test.sh {portal}/{case}` or `./ci-cd/debug-integration-test.sh {portal}/{case}`. 
2) Check the name of the session file that is used for your test in the code. It will be a login argument. Update the contents of `./private/integration_tests_session/<your_session_file>.json` to the new session file you want. 
   
   > **_TIP:_** One way to update the session is to run the test in debug, and manually provide it with 2fa. If you can get it to login sucesfully, the run itself will update the session file that you need, so you can proceed to the next step without manually touching the file.
3) Encrypt sessions by executing appropriate target in the `scrapers` main directory:
    ```
    make encryptSessions
    ```
4) Commit `encrypted_sessions.blob` to version control.  
   
### Updating credentials
Please remember that the credentials file is shared with VCR tests. Any changes you make may affect VCR tests too. 

1) Decrypt the credentials by executing the target in the `scrapers` main directory:
    ```
    make decryptCredentials
    ```
2) Update/add the key you want in `credentials.json`
3) Encrypt credentials by executing the target in the `scrapers` main directory:
    ```
    make encryptCredentials
    ```
4) Commit `credentials_encrypted.json` to version control.
