import {assert} from 'chai';
import {CustomException} from '../../src/error/CustomException';

/**
    @deprecated
    use await expect (call to async function).to.be.rejectedWith(Error | CustomException)
    Remember to import chai-as-promised and use it in your test file.
    ```
        import * as chai from 'chai';
        import * as chaiAsPromised from 'chai-as-promised';

        chai.use(chaiAsPromised);
        const {expect} = chai;
    ```
*/
export async function shouldThrowAsync<T>(asyncFunction: () => Promise<T>, expectedError: Error | CustomException, checkOnlyErrorMessage?: boolean): Promise<void> {
    try {
        await asyncFunction();
        assert.fail();
    } catch (error) {
        if (checkOnlyErrorMessage) {
            assert.equal(error.message, expectedError.message);
        } else {
            assert.equal(error.toString(), expectedError.toString());
        }
    }
}

export async function shouldNotThrowAsync<T>(asyncFunction: () => Promise<T>): Promise<void> {
    try {
        await asyncFunction();
    } catch (error) {
        assert.fail(`${asyncFunction.toString()} should not throw error.\nError thrown: ${error.toString()}`);
    }
}
