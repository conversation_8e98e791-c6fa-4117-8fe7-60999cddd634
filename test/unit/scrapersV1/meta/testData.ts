import * as moment from 'moment';
import * as td from 'testdouble';
import {<PERSON>rowser} from '../../../../src/browser/Browser';
import {Source} from '../../../../src/dataTypes';
import {GraphQLPersistedQuery} from '../../../../src/scrapersV1/httpDownloaders/meta/MetaConstants';

export const source = Source.META_RIFT_SALES;
export const startDate = moment('2025-01-10T10:00:00.000Z').utc();
export const endDate = moment('2025-01-11T10:00:00.000Z').utc();
export const validLogin = '<EMAIL>';
export const validPassword = 'valid';
export const gameId = '123123';
export const gameName = 'sampleRiftGame';

export const graphqlRequestMatcher = (query_id: GraphQLPersistedQuery) =>
    td.matchers.contains({
        doc_id: query_id
    });

export const prepareFakeCookiesAndMetaOrganizations = (fakeBrowser: Browser, organizations?: unknown[], isVerified = true) => {
    const fakeOrganizations = [{name: 'Supertest', id: '123'}];

    td.when(fakeBrowser.cookies()).thenResolve([{value: 'asda', name: 'oc_ac_at'} as any]);

    // get user metadata
    td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_USER_METADATA))).thenResolve({
        data: {viewer: {user: {is_verified: isVerified}}}
    });

    // get organizations
    td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_ORGANIZATIONS))).thenResolve({
        data: {viewer: {user: {organizations: {nodes: organizations ? organizations : fakeOrganizations}}}}
    });

    // change organization
    td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.CHANGE_ORGANIZATION))).thenResolve({
        data: {viewer: {user: {organizations: {nodes: fakeOrganizations}}}}
    });

    // get available apps
    td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_AVAILABLE_APPS))).thenResolve({
        data: {
            organization: {
                applications: {
                    edges: [{node: {id: gameId, display_name: gameName, is_test: false, release_date: new Date().getTime()}}],
                    page_info: {
                        end_cursor: 'some-random-very-long-string',
                        has_next_page: false
                    }
                }
            }
        }
    });
};

export const noSalesValidResponse = {
    data: {
        node: {
            __typename: 'Application',
            sales_aggregated: {
                nodes: []
            },
            entitlements_aggregated: {
                nodes: []
            },
            ocanalytics_revenue_sales_time_over_time: {
                mom_value: null,
                wow_value: null
            },
            ocanalytics_revenue_app_entitlements_time_over_time: {
                mom_value: null,
                wow_value: null
            },
            sales: {
                nodes: []
            },
            entitlements: {
                nodes: []
            }
        }
    }
};

export const noSalesLegacyDashboardValidResponse = {
    data: {
        app_store_item: {
            revenue_analytics_data: {
                app_revenue: {
                    total_revenue: 0,
                    time_series_data: []
                }
            }
        }
    }
};

export const commaInCountryNameValidResponse = {
    data: {
        node: {
            __typename: 'Application',
            sales_aggregated: {
                nodes: [
                    {
                        metrics: {
                            revenue_usd: 9.99
                        }
                    }
                ]
            },
            entitlements_aggregated: {
                nodes: [
                    {
                        metrics: {
                            entitlement_grant_count: 1
                        }
                    }
                ]
            },
            ocanalytics_revenue_sales_time_over_time: {
                mom_value: null,
                wow_value: null
            },
            ocanalytics_revenue_app_entitlements_time_over_time: {
                mom_value: null,
                wow_value: null
            },
            sales: {
                nodes: [
                    {
                        dimensions: {
                            end_time: 1735689600,
                            country: 'Congo, Democratic Republic of the'
                        },
                        metrics: {
                            revenue_usd: 9.99
                        }
                    }
                ]
            },
            entitlements: {
                nodes: [
                    {
                        dimensions: {
                            end_time: 1735689600,
                            country: 'Congo, Democratic Republic of the'
                        },
                        metrics: {
                            entitlement_grant_count: 1
                        }
                    }
                ]
            }
        }
    }
};

export const salesLegacyDashboardValidResponse = {
    data: {
        app_store_item: {
            __typename: 'Application',
            display_name: 'SUPERHOT VR',
            revenue_analytics_data: {
                app_revenue: {
                    total_revenue: 1230.246,
                    wow_revenue: null,
                    mom_revenue: 0.029142765138612,
                    time_series_data: [
                        {
                            timestamp: 1704888000,
                            country: 'US',
                            revenue: 1000.123,
                            active_users: 1,
                            copies_sold: 100
                        },
                        {
                            timestamp: 1704974400,
                            country: 'ZA',
                            revenue: 230.123,
                            active_users: 1,
                            copies_sold: 23
                        }
                    ]
                }
            },
            id: 1921533091289407
        }
    },
    extensions: {
        is_final: true
    }
};
