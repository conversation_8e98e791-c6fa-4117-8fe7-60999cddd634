import {Source, sourceToPortal} from '../../dataTypes';
import {LogLevel} from '../../utils/logger';
import {CustomException} from '../CustomException';
import {errorTypes} from '../errorTypes';

export class NotFoundAnyOrganizationsException extends CustomException {
    constructor(source: Source) {
        super({
            message: `Not found any organizations. The ${sourceToPortal(source)} scraper has nothing to scrape.`,
            suggestedAction: 'Please setup your scraper using an account with access to downloadable products.',
            telemetryLogLevel: LogLevel.WARN,
            errorType: errorTypes.CONFIGURATION_ISSUE
        });
    }
}
