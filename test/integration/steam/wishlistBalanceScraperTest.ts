import {expect} from 'chai';
import * as moment from 'moment';
import {Source} from '../../../src/dataTypes';
import {FileExtension} from '../../../src/utils/files/FileExtension';
import {getLoginArgs, getScrapeArgs} from '../utils/ArgsProvider';
import {listZipContentNames, removeIfFilesExist, runCliMainWithConvertedArgs} from '../utils/Utils';

const numberOfProducts = 7;

describe('Steam Country Wishlists runner should:', () => {
    const defaultStartDateString = '2020-01-01';
    const defaultEndDateString = '2020-01-07';
    const recentStartDate = moment.utc().subtract(7, 'days').format('YYYY-MM-DD');
    const recentEndDate = moment.utc().subtract(2, 'days').format('YYYY-MM-DD');
    const loginArgs = getLoginArgs(Source.STEAM_WISHLIST_BALANCE, 'steam_sales_superhot_fullpermissions', 'steam_fullpermissions.json');
    const scrapeArgs = getScrapeArgs(loginArgs, defaultStartDateString, defaultEndDateString, FileExtension.ZIP);
    const recentScrapeArgs = getScrapeArgs(loginArgs, recentStartDate, recentEndDate, FileExtension.ZIP);

    before(async () => {
        await removeIfFilesExist(scrapeArgs.testedFilePath!);
        await runCliMainWithConvertedArgs(loginArgs);
    });

    it('should download STEAM_WISHLIST_BALANCE zipped data and check number of files', async () => {
        await runCliMainWithConvertedArgs(scrapeArgs);
        const fileNames = listZipContentNames(scrapeArgs.testedFilePath!);
        verifyZipContent(fileNames);
    });

    it('should download recent STEAM_WISHLIST_BALANCE zipped data and check number of files', async () => {
        await runCliMainWithConvertedArgs(recentScrapeArgs);
        const fileNames = listZipContentNames(scrapeArgs.testedFilePath!);
        verifyZipContent(fileNames);
    });
});

function verifyZipContent(fileNames: string[]) {
    expect(fileNames).to.have.length(numberOfProducts + 1); // +1 for manifest.json
    expect(fileNames).to.include.members(['manifest.json']);
    fileNames.forEach((name) => {
        if (name !== 'manifest.json') {
            // eslint-disable-next-line security/detect-unsafe-regex
            expect(name).to.match(/^steam_wishlist_balance-\d{4}-\d{2}-\d{2}_\d{4}-\d{2}-\d{2}-SUPERHOT-Sp-z-oo-\w+(-\w+)*-\d+\.csv$/);
        }
    });
}
