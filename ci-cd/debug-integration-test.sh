#!/usr/bin/env bash

if [ $# -eq 0 ]; then
  echo "Usage: ./debug-integration-test.sh <portal/case>"
  echo "You can check available cases in test/integration directory. Portal is the name of the directory, case is the name of the file without \"Test.js\" suffix."
  echo "Example: ./debug-integration-test.sh gog/scraper"
  exit 1
fi

CASE=$1
./node_modules/.bin/mocha --config 'test/integration/.mocharc.yaml' --inspect "test/integration/${CASE}Test.ts"
