import {expect} from 'chai';
import {CustomException, defaultSuggestedAction} from '../../../src/error/CustomException';
import {errorTypes} from '../../../src/error/errorTypes';
import {LogLevel} from '../../../src/utils/logger';

describe('CustomException should:', () => {
    [
        {message: 'asd', errorType: errorTypes.TIMEOUT_2FA},
        {message: 'asd', error: new Error(), errorType: errorTypes.TIMEOUT_2FA},
        {message: 'asd', telemetryLogLevel: LogLevel.WARN, errorType: errorTypes.TIMEOUT_2FA}
    ].forEach((testData) => {
        it('create custom exception via constructor', () => {
            const customException = new CustomException(testData);
            expect(customException.message).to.be.equal(testData.message);
            expect(customException.originalError).to.be.equal(testData.error);
            expect(customException.telemetryLogLevel).to.be.equal(testData.telemetryLogLevel || LogLevel.ERROR);
        });
    });

    it('create custom exception with suggested action', () => {
        const suggestedAction = 'suggestedAction';
        const customException = new CustomException({message: 'asd', suggestedAction, errorType: errorTypes.BROWSER_ISSUE});
        expect(customException.suggestedAction).to.be.equal(suggestedAction);
    });

    it('create custom exception with a default suggested action', () => {
        const message = 'asd';
        const customException = new CustomException({message, errorType: errorTypes.BROWSER_ISSUE});
        expect(customException.suggestedAction).to.be.equal(`${message} - ${defaultSuggestedAction}`);
    });
});
