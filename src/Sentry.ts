import * as Sentry from '@sentry/node';
import {nodeProfilingIntegration} from '@sentry/profiling-node';

interface SentryInitOptions {
    dsn: string;
    version: string;
    enabled: boolean;
    environment: string;
}

export default function init(sentryInitOptions: SentryInitOptions): void {
    Sentry.init({
        dsn: sentryInitOptions.dsn,
        integrations: [nodeProfilingIntegration()],
        sampleRate: 1.0,
        tracesSampleRate: 1.0,
        profilesSampleRate: 1.0,
        release: sentryInitOptions.version,
        enabled: sentryInitOptions.enabled,
        environment: sentryInitOptions.environment
    });
}

/**
 * Not a big fan of this solution but pushing this through scraperLib seems like unnecessary coupling.
 * Leaving for now for review and further investigation.
 */
export function prepareDefaultSentryInitOptions(version: string): SentryInitOptions {
    const localRunVersionNumber = '1.0.0';
    const isProductionEnv = version !== localRunVersionNumber;
    return {
        version,
        dsn: 'https://<EMAIL>/4505250428485632',
        enabled: isProductionEnv,
        environment: isProductionEnv ? 'production' : 'development'
    };
}
