{"name": "scrapers", "version": "1.0.0", "description": "Client side report scrapers for IndieBI", "main": "dist/src/scrape.js", "engines": {"node": "20.10.0", "npm": "10.2.x"}, "scripts": {"build": "npm i; npm run installHusky", "debug:login": "./scripts/run_login.sh $1", "debug:scrape": "./scripts/run_scrape.sh $1", "debug:get-source-side-organizations": "./scripts/run_get-source-side-organizations.sh $1", "debug:example-output": "node -r ts-node/register --inspect ./src/scrape.ts example-output", "eslint": "eslint \"src/**/*.ts\" \"test/**/*.ts\" --cache --max-warnings 0 --ext .js,.ts", "lint": "npm run eslint", "installHusky": "echo \"This was previously prepare step but since husky is a dev dependency it got in the way of npm ci --production\"; husky install", "test": "npm run test-unit", "test-unit": "cross-env NODE_ENV=test ./node_modules/.bin/nyc ./node_modules/.bin/mocha 'test/unit/**/*Test.ts'", "test-unit-watch": "cross-env NODE_ENV=test ./node_modules/.bin/mocha 'test/unit/**/*Test.ts' --watch --reporter min", "test-unit-js": "cross-env NODE_ENV=test ./node_modules/.bin/mocha 'dist/test/unit/**/*Test.js'", "test-unit-js-watch": "cross-env NODE_ENV=test ./node_modules/.bin/mocha 'dist/test/unit/**/*Test.js' --watch --reporter min", "test-integration": "./node_modules/.bin/mocha --config 'test/integration/.mocharc.yaml' 'test/integration/**/*Test.ts'", "test-integration-encryptSessions": "./node_modules/typescript/bin/tsc; node dist/test/integration/utils/scripts/encryptSessions.js", "tscwatch": "tsc --watch", "cli": "node -r ts-node/register --inspect src/scrape.ts", "build-cli-all": "./node_modules/typescript/bin/tsc; ./package/package-cli.sh ./builds node20-linux-x64,node20-macos-x64,node20-win-x64", "build-cli-linux": "./node_modules/typescript/bin/tsc; ./package/package-cli.sh ./builds node20-linux-x64", "build-cli-macos": "./node_modules/typescript/bin/tsc; ./package/package-cli.sh ./builds node20-macos-x64", "build-cli-win": "./node_modules/typescript/bin/tsc; ./package/package-cli.sh ./builds node20-win-x64"}, "keywords": ["IndieBI", "<PERSON><PERSON><PERSON>"], "author": "IndieBI", "dependencies": {"@sentry/node": "8.13.0", "@sentry/profiling-node": "8.13.0", "adm-zip": "0.5.10", "archiver": "6.0.1", "axios": "1.7.7", "axios-har-tracker-safe-stringify": "0.7.3", "case-anything": "2.1.13", "cheerio": "1.0.0-rc.12", "class-transformer": "0.5.1", "class-transformer-validator": "0.9.1", "class-validator": "0.14.0", "cryptr": "6.3.0", "fs-extra": "11.2.0", "http-status-codes": "2.3.0", "json-stringify-safe": "5.0.1", "jszip": "3.10.1", "lodash": "4.17.21", "moment": "2.29.4", "nconf": "0.12.1", "otpauth": "9.2.1", "papaparse": "5.4.1", "puppeteer": "23.5.0", "puppeteer-har": "1.1.2", "slugify": "1.6.6", "steam-session": "1.9.3", "tiny-async-pool": "1.3.0", "uuid": "9.0.1", "yargs": "17.7.2"}, "devDependencies": {"@bluebrick/eslint-config-scp": "2.0.1", "@types/adm-zip": "0.5.5", "@types/archiver": "6.0.2", "@types/chai": "4.3.11", "@types/chai-as-promised": "7.1.8", "@types/fs-extra": "11.0.4", "@types/json-stringify-safe": "5.0.3", "@types/jszip": "3.4.1", "@types/lodash": "4.14.202", "@types/mocha": "10.0.6", "@types/mock-fs": "4.13.4", "@types/nconf": "0.10.6", "@types/node": "20.10.3", "@types/papaparse": "5.3.14", "@types/puppeteer-har": "1.1.0", "@types/rewire": "2.5.30", "@types/sinon": "17.0.2", "@types/sinon-chai": "3.2.12", "@types/uuid": "9.0.7", "@types/yargs": "17.0.32", "@typescript-eslint/eslint-plugin": "6.13.2", "@typescript-eslint/eslint-plugin-tslint": "6.13.2", "@typescript-eslint/parser": "6.13.2", "@yao-pkg/pkg": "5.11.5", "chai": "4.3.10", "chai-as-promised": "7.1.1", "cross-env": "7.0.3", "eslint": "8.13.0", "fernet-nodejs": "1.0.6", "husky": "9.0.11", "lint-staged": "^15.5.2", "madge": "7.0.0", "mocha": "10.2.0", "mocha-junit-reporter": "2.2.1", "mocha-multi-reporters": "1.5.1", "mock-fs": "5.2.0", "nyc": "17.1.0", "prettier": "2.6.2", "rewire": "7.0.0", "sinon": "17.0.1", "sinon-chai": "3.7.0", "testdouble": "3.20.1", "ts-node": "10.9.1", "typescript": "5.3.3", "webpack-cli": "5.1.4"}, "lint-staged": {"{test,src}/**/*.{js,ts,json}": "eslint --cache --fix"}}