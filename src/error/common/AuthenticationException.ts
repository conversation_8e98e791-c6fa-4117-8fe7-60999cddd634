import {LogLevel} from '../../utils/logger';
import {CustomException} from '../CustomException';
import {errorTypes} from '../errorTypes';

export class AuthenticationException extends CustomException {
    constructor(authenticationErrorString?: string) {
        super({
            message: authenticationErrorString || 'An authentication error occurred.',
            suggestedAction: 'Please double check the provided credentials and try again.',
            telemetryLogLevel: LogLevel.WARN,
            canBeFixedByRetry: false,
            errorType: errorTypes.INCORRECT_CREDENTIALS
        });
    }
}
