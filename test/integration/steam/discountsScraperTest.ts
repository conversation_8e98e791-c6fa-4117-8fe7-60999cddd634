import {expect} from 'chai';
import * as moment from 'moment';
import {Source} from '../../../src/dataTypes';
import {FileExtension} from '../../../src/utils/files/FileExtension';
import {steamSuperhotOrgs} from '../expectedData';
import {createGetSourceSideOrgTest} from '../getSourceSideOrganizationTestGenerator';
import {getLoginArgs, getScrapeArgs} from '../utils/ArgsProvider';
import {listZipContentNames, removeIfFilesExist, runCliMainWithConvertedArgs} from '../utils/Utils';

describe('Steam Discounts runner should', () => {
    const startDate = moment.utc().subtract(4, 'days');
    const startDateAsFormattedString = startDate.format('YYYY-MM-DD');
    const endDate = moment.utc().subtract(2, 'days');
    const endDateAsFormattedString = endDate.format('YYYY-MM-DD');
    const loginArgs = getLoginArgs(Source.STEAM_DISCOUNTS, 'steam_sales_superhot_multiorg', 'steam_multiorg.json');
    const scrapeArgs = getScrapeArgs(loginArgs, startDateAsFormattedString, endDateAsFormattedString, FileExtension.ZIP);

    before(async function () {
        await removeIfFilesExist(scrapeArgs.testedFilePath!);
        await runCliMainWithConvertedArgs(loginArgs);
    });

    createGetSourceSideOrgTest(loginArgs, steamSuperhotOrgs, true);

    it('download recent zipped data and verify number of files and filenames', async function () {
        await runCliMainWithConvertedArgs(scrapeArgs);
        expect(listZipContentNames(scrapeArgs.testedFilePath!)).to.have.members([
            'discounts_all_3204.csv',
            'discountManagementData.json',
            'discountHistory.json',
            'basePrices.csv',
            'discountEvents.csv',
            'packageDiscounts.csv',
            'packageIds.csv',
            'priceIncreaseTimes.csv',
            'userinfo.csv',
            'discountHistory.csv',
            'manifest.json'
        ]);
    });
});
