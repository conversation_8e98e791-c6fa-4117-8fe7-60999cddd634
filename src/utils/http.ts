import axios, {AxiosBasicCredentials, AxiosError, AxiosInstance, AxiosProxyConfig, AxiosRequestConfig, AxiosResponse} from 'axios';
import {AxiosHarTracker} from 'axios-har-tracker-safe-stringify';
import {BrowserSession} from '../browserV2';
import * as config from '../config/ConfigService';
import {<PERSON>ie} from '../puppeteer';
import {log} from './logger';

/**
 * This interface is a subset of the AxiosResponse interface.
 */
export interface HTTPResponse {
    data: any;
    status: number;
    headers: Record<string, string>;
    config: AxiosRequestConfig;
}

export type HttpMethod = 'GET' | 'POST' | 'PATCH' | 'PUT' | 'DELETE' | 'OPTIONS';

interface HTTPRequest {
    url: string;
    followRedirects?: boolean;
    headers?: Record<string, string>;
    stream?: boolean;
    extraCookies?: Record<string, string>;
    method?: HttpMethod;
    data?: string;
    formData?: Record<string, string>;
    auth?: Record<any, any>;
}

export const proxyUrlToAxiosProxyConfig = (proxyUrl?: string): AxiosProxyConfig | undefined => {
    if (proxyUrl) {
        const url = new URL(proxyUrl);
        return {
            host: `${url.hostname}`,
            port: parseInt(url.port)
        };
    }
};

export function rewriteUrlAndCreateVCRHeaders(usesProxy: boolean, url: string, followRedirects: boolean) {
    const vcrHeaders = {};
    let proxyFriendlyUrl: string = url;
    if (usesProxy && url.startsWith('https://')) {
        proxyFriendlyUrl = 'http://' + url.slice('https://'.length);
        vcrHeaders['x-ndbi-proxy-protocol'] = 'https';
        if (followRedirects) {
            vcrHeaders['x-ndbi-proxy-rewrite-redirects'] = 'true';
        }
    }

    return {vcrHeaders, proxyFriendlyUrl};
}

export class HTTPClient {
    /* HTTP client to be used by scrapers when not using Puppeteer, with useful utilities packed in. */

    private readonly session: BrowserSession;

    private readonly axiosInstance: AxiosInstance;

    private axiosTracker?: AxiosHarTracker;

    private readonly usesProxy: boolean;

    private debugCache?: AxiosResponse;

    constructor(session?: BrowserSession, proxyUrl?: string) {
        this.session = session ?? {cookies: []};

        const proxy = proxyUrlToAxiosProxyConfig(proxyUrl);

        this.usesProxy = !!proxy;
        this.axiosInstance = axios.create({
            timeout: 100000,
            proxy
        });
        if (config.isFeatureFlagEnabled('full-history-dump')) {
            log.info('Full history dump enabled, creating HAR tracker');
            this.axiosTracker = new AxiosHarTracker(this.axiosInstance);
        }
    }

    private telemetryForResponse({status, config: {url}}: AxiosResponse) {
        log.debug(`Request for: ${url} returned ${status}`, {url, status});
    }

    public overrideCookies(cookies: Cookie[]): void {
        this.session.cookies = cookies;
    }

    public getDebugCache(): AxiosResponse | undefined {
        return this.debugCache;
    }

    public getHAR(): any | undefined {
        return this.axiosTracker?.getGeneratedHar();
    }

    public async request<T>(request: HTTPRequest): Promise<AxiosResponse<T>> {
        const {auth, method, followRedirects = true, stream = false, extraCookies = {}, formData} = request;
        let {url, data} = request;
        const cookieHeader = this.session?.cookies ? cookieHeaderFromSession(this.session, extraCookies) : '';

        const {proxyFriendlyUrl, vcrHeaders} = rewriteUrlAndCreateVCRHeaders(this.usesProxy, url, followRedirects);
        const headers = {
            cookie: cookieHeader,
            ...request.headers,
            ...vcrHeaders
        };

        url = proxyFriendlyUrl;

        if (formData) {
            const params = new URLSearchParams(formData);
            data = params.toString();
            headers['content-type'] = 'application/x-www-form-urlencoded; charset=UTF-8';
            headers['cache-control'] = 'no-cache';
            headers['pragma'] = 'no-cache';
        }

        try {
            const response = await this.axiosInstance.request<T>({
                url,
                method,
                data,
                responseType: stream ? 'stream' : 'json',
                headers,
                maxRedirects: 0, // we have custom redirects logic with sync cookies with session,
                auth: auth as AxiosBasicCredentials
            });

            if (isHtmlResponse(response)) {
                this.debugCache = response;
            }

            this.setCookiesFromHeaders(response);
            /*
            This is done because the response url is based on the request url which is malformed to be compatible with VCRproxy
            This needs to be done so that logic based on determining proper redirecting( https vs http) works properly.
            At the time of writing this is used for Steam v2 browserless login
            */
            if (response.config.url && response.config.url.startsWith('http://') && this.usesProxy) {
                response.config.url = 'https://' + url.slice('http://'.length);
            }

            return response;
        } catch (error) {
            if (error instanceof AxiosError && error.response) {
                this.debugCache = error.response;
                const status = error.response?.status || 0;
                if (status === 302 && followRedirects) {
                    this.setCookiesFromHeaders(error.response);
                    const redirectUrl = error.response.headers['location'];
                    return this.request({...request, url: redirectUrl});
                }
                if ((status >= 300 && status < 400) || status === 429) {
                    return error.response;
                }
            }
            throw error;
        }
    }

    public async get<T>(request: HTTPRequest): Promise<AxiosResponse<T>> {
        request['method'] = 'GET';
        return this.request<T>(request);
    }

    public async post<T>(request: HTTPRequest): Promise<AxiosResponse<T>> {
        request['method'] = 'POST';
        return this.request<T>(request);
    }

    private setCookiesFromHeaders(response: AxiosResponse) {
        this.telemetryForResponse(response);
        const {host} = new URL(response.config.url!);

        for (const cookieStr of response.headers['set-cookie'] || []) {
            const cookie = parseCookie(cookieStr);
            if (!cookie.domain) {
                cookie.domain = host;
            }
            this.session.cookies = this.session.cookies.filter((c) => !(c.name == cookie.name && c.domain == cookie.domain));
            this.session.cookies.push(cookie);
        }
    }
}

export function convertCookiesToString(cookies: Cookie[] | Record<string, string>): string {
    if (Array.isArray(cookies)) {
        return cookies.map((cookie) => `${cookie.name}=${cookie.value}`).join(';');
    }
    return Object.entries(cookies)
        .map(([k, v]): string => `${k}=${v}`)
        .join(';');
}

function cookieHeaderFromSession(session: BrowserSession, extraCookies = {}) {
    const cookies = {
        ...Object.fromEntries(session.cookies.map((cookie) => [cookie.name, cookie.value])),
        ...extraCookies
    };

    return convertCookiesToString(cookies);
}

export function isHtmlResponse({status, headers: {'content-type': contentType}}: AxiosResponse): boolean {
    return status >= 200 && status < 300 && contentType.includes('text/html');
}

export function parseCookie(cookieHeader: string): Cookie {
    const parts = cookieHeader.split(';');
    const nameValue = parts.shift()!.split('=');

    const cookie: any = {
        name: nameValue.shift(),
        value: nameValue.join('=').trim()
    };

    for (const part of parts) {
        const sides = part.split('=');
        const key = sides.shift()!.trim().toLowerCase();
        const value = sides.join('=');
        if (key === 'expires') {
            cookie.expires = new Date(value).getTime() / 1000.0;
        } else if (key === 'max-age') {
            cookie.maxAge = parseInt(value, 10);
        } else if (key === 'secure') {
            cookie.secure = true;
        } else if (key === 'httponly') {
            cookie.httpOnly = true;
        } else if (key === 'samesite') {
            cookie.sameSite = value;
        } else if (key === 'domain') {
            cookie.domain = value;
        } else {
            cookie[key] = value;
        }
    }

    return cookie;
}
