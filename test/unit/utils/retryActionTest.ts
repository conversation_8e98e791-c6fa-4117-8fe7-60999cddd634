import * as chai from 'chai';
import * as chaiAsPromised from 'chai-as-promised';
import * as sinon from 'sinon';
import * as sinonChai from 'sinon-chai';
import * as messaging from '../../../src/cli/messaging';
import {retryAction} from '../../../src/utils/retryAction';
import * as sleepUtil from '../../../src/utils/sleep';

chai.use(chaiAsPromised).use(sinonChai);
const expect = chai.expect;

describe('retryAction', () => {
    beforeEach(() => {
        sinon.stub(sleepUtil, 'sleep').resolves();
    });
    afterEach(sinon.restore);

    it('should properly rethrow error when reached maxRetries', async () => {
        const expectedError = new Error('error');
        const fun = retryAction({
            target: async () => {
                throw expectedError;
            },
            maxAttempts: 0
        });
        await expect(fun).to.eventually.be.rejectedWith(expectedError);
    });

    it('should properly repeat until maxRetries reached', async () => {
        const messagingTraceSpy = sinon.spy(messaging, 'printTrace');
        const target = sinon.spy(async () => {
            throw new Error('error');
        });
        const fun = retryAction({target, maxAttempts: 2});

        await expect(fun).to.eventually.be.rejected;

        expect(target).to.have.callCount(2);
        expect(messagingTraceSpy).to.have.callCount(10);
    });

    it('should stop immediately once retryCondition returns false', async () => {
        const target = sinon.spy(async () => {
            throw new Error('error');
        });

        const retryCondition = sinon.stub();
        retryCondition.onCall(0).returns(true);
        retryCondition.onCall(1).returns(true);
        retryCondition.onCall(2).returns(false);

        const expectedAttempts = 3;
        const fun = retryAction({target, maxAttempts: expectedAttempts * 2, retryCondition});
        await expect(fun).to.eventually.be.rejected;
        expect(target).to.have.callCount(expectedAttempts);
    });

    it('should use label if provided', async () => {
        const logger = sinon.stub();
        const fun = retryAction({
            target: async () => {},
            logger,
            maxAttempts: 1,
            label: 'doing shady stuff'
        });
        await expect(fun).to.eventually.be.fulfilled;
        expect(logger).to.have.been.calledWith('Starting doing shady stuff, attempt: 1/1');
    });
});
