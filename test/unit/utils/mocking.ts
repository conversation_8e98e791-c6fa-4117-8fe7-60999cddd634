import {<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>} from 'puppeteer';

export function populateFakeElementHandle(handle: ElementHandle, content: Record<string, unknown>): any {
    return {
        ...handle,
        // eslint-disable-next-line security/detect-object-injection
        getProperty: (property: string): Partial<JSHandle> => ({jsonValue: async <T = unknown>(): Promise<T> => content[property] as T}),
        click: () => null
    };
}

export function mockHttpResponse(statusCode: number): Partial<HTTPResponse> {
    return {
        status: () => statusCode
    } as Partial<HTTPResponse>;
}
