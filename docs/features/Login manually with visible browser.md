### Intro:

Each scraper should have the ability to log in manually via a visible browser (same as with current captcha handling).

Implementation of [ELE-46]

### Why we set fake username/password:

Because we do not want to gather usernames/passwords during this process and our current setup requires a username/password
combination to designate a scraper as `ACTIVE` after scraping all cookies we set the mentioned values to `fake` values.

This should be without any concerns to users because the login process is based on cookies which will have a proper value.

### Known issues:

At the time of writing the Epic scraper captcha system is broken (for automated browsers) and browser login will not work properly.
