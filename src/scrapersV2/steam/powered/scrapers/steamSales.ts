import {getIgnoredProducts} from '../../../../config/ConfigService';
import {Source} from '../../../../dataTypes';
import {Report} from '../../../../scrapersV1/Report';
import {FileExtension} from '../../../../utils/files/FileExtension';
import {buildFilePath, generateFileName} from '../../../../utils/files/fileUtils';
import {ScrapeFunction} from '../../../Scraper';
import {SteamParams, getLoginFunction} from '../../common/login';
import {steamScraperWrapper} from '../../common/scraperWrapper';
import {SteamSession} from '../../common/SteamSession';
import {checkSteamPoweredLogin, checkSteamPoweredSession} from '../checkSession';
import {getSteamPoweredSourceSideOrganizations, refreshSourceOrgsInSession} from '../multiorg';
import {SteamSalesScrapingOptions, scrapeSalesFromOrgs} from '../scrapeSalesFromOrgs';
import {steamPoweredManualLoginDetails} from '../steamPoweredManualLoginDetails';
import {zipBuilder} from '../zipBuilder';

const scrape: ScrapeFunction<SteamParams, SteamSession> = async (context, startDate, endDate) => {
    const {progress, params} = context;
    const steamSalesScrapingOptions: SteamSalesScrapingOptions = {
        progress: progress,
        httpClient: context.getHttpClient(),
        startDate,
        endDate
    };

    const zipFileName = generateFileName(Source.STEAM_SALES, startDate, endDate, FileExtension.ZIP);
    const zipFilePath = buildFilePath(zipFileName);
    const ignoredPackages = [...getIgnoredProducts(Source.STEAM_SALES), ...(context.params.ignoredProducts || [])];
    progress(`${ignoredPackages.length} products to ignore.`);

    await zipBuilder(zipFilePath, async (appendToZip) => {
        const fileMetaData = {};

        let index = 0;
        const steamOrganizations = await refreshSourceOrgsInSession(steamSalesScrapingOptions.progress, context.session, steamSalesScrapingOptions.httpClient);
        for await (const report of scrapeSalesFromOrgs(steamSalesScrapingOptions, ignoredPackages, steamOrganizations, params.featureFlags ?? [])) {
            index++;
            const filename = generateFileName(Source.STEAM_SALES, startDate, endDate, FileExtension.CSV, index);
            progress('Appending csv stream to archive');
            await appendToZip(report.stream, filename);

            fileMetaData[filename] = {
                checksum: {unitsSold: report.totalUnitsSold},
                contentType: report.contentType,
                packageId: report.package?.id,
                packageName: report.package?.name
            };
        }
        progress('Creating manifest file');
        const additionalData = {
            scraper: 'steam_sales',
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
            fileMetaData: fileMetaData
        };
        progress('Appending manifest file to archive');
        await appendToZip(JSON.stringify(additionalData), `additionalData_steam_sales_${startDate.format('YYYY-MM-DD')}_${endDate.format('YYYY-MM-DD')}.json`);
    });

    progress(`Saving report to ${zipFilePath}`);
    return [new Report(Source.STEAM_SALES, zipFileName, startDate, endDate)];
};

export const steamSalesScraper = steamScraperWrapper({
    login: getLoginFunction(Source.STEAM_SALES, checkSteamPoweredLogin),
    scrape,
    getSourceSideOrganizations: getSteamPoweredSourceSideOrganizations,
    checkSession: checkSteamPoweredSession,
    manualLoginDetails: steamPoweredManualLoginDetails
});
