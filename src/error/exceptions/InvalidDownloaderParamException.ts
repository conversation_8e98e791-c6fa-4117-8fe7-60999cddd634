import {Source, sourceToPortal} from '../../dataTypes';
import {LogLevel} from '../../utils/logger';
import {CustomException} from '../CustomException';
import {errorTypes} from '../errorTypes';

export class InvalidDownloaderParamException extends CustomException {
    constructor(source: Source, param: string) {
        super({
            message: `Invalid downloader ${param} param for ${sourceToPortal(source)}`,
            suggestedAction: `We've detected a problem with your ${sourceToPortal(source)} configuration. Looks like you provided invalid ${param}.`,
            telemetryLogLevel: LogLevel.WARN,
            errorType: errorTypes.CONFIGURATION_ISSUE
        });
    }
}
