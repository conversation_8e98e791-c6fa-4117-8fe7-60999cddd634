import {Frame, Page, TimeoutError} from 'puppeteer';
import {activeWaitTimeout} from '../config/ConfigService';
import {PromiseState, waitSettled} from '../utils/promises';
import Timeout = NodeJS.Timeout;

const finalTimeoutOffset = 50;
const intervalTimeout = 250;
const options = {timeout: intervalTimeout, visible: true};
interface WaitOptions {
    timeout?: number;
    visible?: boolean;
    isXPath?: boolean;
}

export async function intervalSearchElementExists<T>(pageOrFrame: Page | Frame, selector: string, waitOptions: WaitOptions = {}): Promise<T> {
    const {timeout = activeWaitTimeout, visible = true, isXPath = false} = waitOptions;

    return new Promise((resolve: (result: T) => void, reject: (reason: Error) => void): void => {
        let intervalTimerId: Timeout;

        function endIntervalSearch(): void {
            clearTimeout(intervalTimerId);
            reject(new TimeoutError(`Selector '${selector}' not found.`));
        }

        const endIntervalSearchTimeout = setTimeout(endIntervalSearch, timeout + finalTimeoutOffset);

        async function searchForElementInterval(): Promise<void> {
            try {
                const element = isXPath
                    ? await pageOrFrame.waitForSelector(`::-p-xpath(${selector})`, {timeout: intervalTimeout, visible})
                    : await pageOrFrame.waitForSelector(selector, {timeout: intervalTimeout, visible});
                clearTimeout(endIntervalSearchTimeout);
                resolve(element as any);
            } catch (error) {
                if (!(error instanceof TimeoutError)) {
                    clearTimeout(endIntervalSearchTimeout);
                    return reject(error);
                }
                intervalTimerId = setTimeout(searchForElementInterval, intervalTimeout);
            }
        }

        intervalTimerId = setTimeout(searchForElementInterval, 0);
    });
}

export async function intervalSearchAnyElementExists<T>(pageOrFrame: Page | Frame, selectors: string[], waitOptions: WaitOptions = {}): Promise<T[]> {
    const promises = selectors.map(async (selector: string) => intervalSearchElementExists(pageOrFrame, selector, waitOptions));

    const result = await Promise.all(promises.map(waitSettled));

    const elements = <T[]>result.filter((res) => res.status === PromiseState.FULFILLED).map((res) => res.value);

    if (elements.length === 0) {
        throw new TimeoutError('Selectors not found');
    }

    return elements;
}

export async function elementDoesNotExist(pageOrFrame: Page | Frame, selector: string, timeout: number = activeWaitTimeout): Promise<void> {
    return new Promise((resolve: () => void, reject: (reason: Error) => void): void => {
        let intervalTimerId: Timeout;

        function endIntervalSearch(): void {
            clearTimeout(intervalTimerId);
            reject(new TimeoutError(`Selector '${selector}' is still visible.`));
        }

        const endIntervalSearchTimeout = setTimeout(endIntervalSearch, timeout + finalTimeoutOffset);

        async function elementDoesNotExistInterval(): Promise<void> {
            try {
                await pageOrFrame.waitForSelector(selector, options);
                intervalTimerId = setTimeout(elementDoesNotExistInterval, intervalTimeout);
            } catch (error) {
                if (!(error instanceof TimeoutError)) {
                    reject(error);
                    return;
                }
                clearTimeout(endIntervalSearchTimeout);
                resolve();
            }
        }

        intervalTimerId = setTimeout(elementDoesNotExistInterval, 0);
    });
}
