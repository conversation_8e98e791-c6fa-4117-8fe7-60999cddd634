### Tired of logging into EPIC manually every day?

### Tired of seeing this?

![](../images/epicHCaptcha.jpg)

A new scraper feature for EPIC allows you to automate EPIC login com-ple-te-ly!

![](../images/100auto.jpg)

# Intro & Rationale

-   New option for login for Epic: Login with Steam.
-   Epic does not require a captcha challenge if using another login provider, which is an elegant way for us to increase Epic scraper's level of automation
-   Epic session (cookie) timeout is very short (hours), so logging in manually every time is painful.
-   This feature also incorporates several new mechanisms:
    -   automated collection of [2FA Email Codes](./email2FA.md)
    -   automated generation of [2FA TOTP Codes](./totp2FA.md)

# How to test

## In a local environment:

### Setup

1. Get an EPIC account you want to test on (I'm using `supersuperdata`).
1. Make sure that this EPIC account is connected with a Steam account.
1. If the Steam account you're using has Steam Guard enabled, make sure Steam Guard is set to sending email codes.

    _Using Steam Guard Authenticator is also theoretically supported, although harder to setup and not covered here._

1. Get the 2FA shared secret for your EPIC account:
    1. On EPIC website, enable 2FA through Authenticator App. If it's already on, disable then reenable.
    1. When the QR code is displayed, save the code labeled as "Manual Entry Key":
       ![](../images/manualEntryKey.jpg)
    1. After you write the code down, you can (re-)configure any supported Authenticator App with the supplied QR code.
1. Run the stack locally and in a browser, look up your local account's scraper email (Automation -> Scrapers in the frontend).
1. Write down your _forwarding email_, which is the code from your scraper email with a different prefix and domain. If your scraper email is `<EMAIL>`, your _forwarding email_ for local development is `<EMAIL>`.

### Test

1. Run the CLI app.
1. Login using any IndieBI account.
1. Set up EPIC using the `Set up using Steam login` option.
1. Enter **Steam username** & password.
1. Enter the EPIC 2FA shared secret ("Manual Entry Key").
1. The scraper will now attempt to login. During login, it will stop at Email 2FA from Steam.
1. Look for the 2FA email in your inbox.
1. Use the script at `indiebi-scrapper-api/test/sendSteamAuthEmailWebhook.sh` to simulate receiving an email with the code in the local environment, like this:

    `./sendSteamAuthEmailWebhook.sh <<EMAIL>> <<EMAIL>> <auth_code>`

    `<<EMAIL>>` is the user email.
    `<<EMAIL>>` is your _forwarding email_ (see Setup above)

1. The scraper should now be able to log in. Additionally, a session file is created at `~/.local/share/indieBI/sessions/epic.json`, that contains all the cookies received during the login process, from both Steam and Epic domains.
1. If you remove the session file and try scraping, the scraper should relogin automatically using credentials saved in the config. You'll still need to inject the Steam 2FA code.
1. Logging out of the scraper should remove the session file.
1. Purging the scraper should also remove all session files.

## In an Integration/QA environment:

1. Perform similar setup as for local env (see above), making sure that the IndieBI account you're using has matching email to the Steam account you're logging in with.
1. Set up a forwarding rule for the inbox that receives emails for Steam with 2FA codes. The mail should be forwarded to:
    - `<EMAIL>` for Production environment,
    - `<EMAIL>` for QA environment,
    - and so on for other environments
      _(if you're configuring a new environment, some setup is required on Mandrill side - you need to add a new route in Mandrill Inbound API)_
