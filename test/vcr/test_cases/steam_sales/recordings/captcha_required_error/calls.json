{"calls": {"https://partner.steampowered.com/nav_games.php": [{"status": 302, "headers": {"content-type": "text/html; charset=UTF-8", "content-security-policy": "script-src 'self' 'unsafe-inline' 'unsafe-eval'; object-src 'none'; connect-src 'self'; child-src 'self'; report-uri /actions/cspreport", "location": "https://partner.steampowered.com/login/?goto=%2Fnav_games.php", "set-cookie": "dateStart=2022-07-14; expires=Thu, 14-Jul-2022 14:44:24 GMT; Max-Age=10800; path=/, dateEnd=2022-07-14; expires=Thu, 14-Jul-2022 14:44:24 GMT; Max-Age=10800; path=/, priorDateStart=2022-07-13; expires=Thu, 14-Jul-2022 14:44:24 GMT; Max-Age=10800; path=/, priorDateEnd=2022-07-13; expires=Thu, 14-Jul-2022 14:44:24 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/login/getrsakey/": [{"method": "POST", "req_headers": {"content-type": "application/x-www-form-urlencoded; charset=UTF-8"}, "req_body": "donotcache=1657799063379&username=supersuperdata", "body": "{\"success\":true,\"publickey_mod\":\"d0874ac7a21d5eff45c7d3c554b6f0707fed5b0855e04d5bfa716cbe581aa18943d3a239036886b1f4ef04e5f07e80e26cd537b1f7e453e41541bb073dbc6eb648f356381e01d9ea48e57472b932acaa3367b3e4d258c66c3182a3c666059ed684988cdaa80a5834f456a961b0bb9ad9e829f82253dd4d089ed0465b6c75b637133221a2299a206f05c72b058253778dd5257b968423e06161c2ca6072d2c0ce382313428e8b830caa0f63369325ba988be4bf2c92b74fee8771de7464cb9bdd16940ff6c92170bed6517dad86b81c8d3a0f5ca1c150a092ca6e06f12328463a9987d8ddd2642e1f2fb8bc3a78e47bb872c58189f24ee8500e2aec83b2d4e3b9\",\"publickey_exp\":\"010001\",\"timestamp\":\"46832700000\",\"token_gid\":\"497a0f8e70470ff3\"}", "headers": {}}], "https://partner.steampowered.com/login/dologin/": [{"method": "POST", "req_headers": {"content-type": "application/x-www-form-urlencoded; charset=UTF-8"}, "req_body": "donotcache=1657799063691&username=supersuperdata&password=O%2FKpC14fVGMLFqwxHXDl8HZgS25v%2BiiQpOu%2FMQYVpnbgewroyqZJR9e8iKMWb%2BVbtx%2Bl%2BaGU5jfVKIcG%2FvMvKwyBsPzF0XCeDIAi%2B5H6%2F6Qw9ord6izs0AdR4vZuEESq8HVfPN219mGs5i%2BYYBq7jsOql0kCCjYx00SYjCSW8%2Bra551O2nl0lXQUkFgDlQpcFBqBdY5jPPvr7ITmWQRawHAQ8biT%2BYAHBeaphEgnWw7DH141gfFX%2BIhhKPcgT9CIcc6MYQGlwDjK2rWsF5co%2BenC7so1oYiLovsvd936xqzpnjrqTNAml57QboiCejn3kXnMaT1a1fWS3j1s9ZyFXw%3D%3D&twofactorcode=&emailauth=&loginfriendlyname=&captchagid=-1&captcha_text=&emailsteamid=&rsatimestamp=46832700000&remember_login=true&tokentype=-1", "body": "{\"success\":false,\"message\":\"#Login_Error_CaptchaBad\",\"requires_twofactor\":false,\"captcha_needed\":true,\"captcha_gid\":\"4785654656953061541\"}", "headers": {}}]}, "defaults": {"request_headers": {"accept": "application/json, text/plain, */*", "cookie": "steamLoginSecure=76561198803773815%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Gz3uCgibh9efYBhpne5GYNz2b9Usfkq-vk29Z6UzYDsFqYBDwp5SyPnKexmYLBNLc0U_8QJficVNrHOC7hb7Ag;", "user-agent": "axios/0.27.2", "host": "partner.steampowered.com", "connection": "close"}, "headers": {"server": "nginx", "content-type": "application/json; charset=utf-8", "connection": "close"}}}