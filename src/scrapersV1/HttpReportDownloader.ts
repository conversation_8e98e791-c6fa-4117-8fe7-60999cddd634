import {SourceIdentifier, SourceSideOrganization} from '../dataTypes/SourceSideOrganization';
import {LoginResult} from './LoginResult';
import {ReportDownloader} from './ReportDownloader';

export abstract class HttpReportDownloader extends ReportDownloader {
    public abstract performLogin(): Promise<LoginResult>;
    public abstract getSourceSideOrganizations(): Promise<SourceSideOrganization[]>;
    public abstract checkSession(): Promise<SourceIdentifier>;
    public abstract fillSecondAuthenticationFactor(secondAuthenticationFactor: string): Promise<void>;
    public abstract get dualAuthMaxAttempts(): number;
}
