import {OK} from 'http-status-codes';
import {getIndieBIKey} from '../config/ConfigService';
import {Portal, Source, isSteamSource} from '../dataTypes';
import {LoginException, LoginExceptionType} from '../error/exceptions';
import {log} from '../utils/logger';
import {sleep} from '../utils/sleep';
import {monotonicTimer} from '../utils/timer';
import * as totp from '../utils/totp';
import {generateSteamGuard} from '../utils/totp';
import {get} from './axios';

const defaultRetryTimeout = 300;
const authCodeUrl = '/auth-code';

type SourceOrPortal = {source?: Source; portal: Portal} | {source: Source; portal?: Portal};

async function makeAuthCodeRequest({source, portal}: SourceOrPortal): Promise<string | null> {
    const url = !!source && !!portal ? `${authCodeUrl}/${source.toLowerCase()}/${portal.toLowerCase()}` : `${authCodeUrl}/${(source || portal).toLowerCase()}`;

    log.debug('Requesting auth code from API');

    try {
        const response = await get(url, {
            headers: {
                Authorization: `Bearer ${getIndieBIKey()}`
            },
            validateStatus: (status): boolean => status === OK
        });
        return response.data.authCode;
    } catch (error) {
        throw new Error(`Requesting auth code failed: ${error}`);
    }
}

async function requestAuthCodeWithRetries(authCodeMode: SourceOrPortal, retryTimeout: number = defaultRetryTimeout): Promise<string> {
    const startTime = monotonicTimer();
    const retryDelay = 10 * 1000;

    log.info('Waiting for auth code...');

    // eslint-disable-next-line no-constant-condition
    while (true) {
        const code = await makeAuthCodeRequest(authCodeMode);
        const elapsedTime = monotonicTimer() - startTime;
        if (code !== null) {
            return code;
        }
        if (elapsedTime > retryTimeout) {
            const {source, portal} = authCodeMode;
            throw new LoginException(LoginExceptionType.MFA_REQUIRED, `Did not receive code in time for ${portal ? `portal: ${portal}` : `source: ${source}`}.`);
        }
        await sleep(retryDelay);
    }
}

export async function getAuthCode(authCodeMode: SourceOrPortal, totpSecret?: string, retryTimeout: number = defaultRetryTimeout): Promise<string> {
    if (totpSecret) {
        log.info('Generating 2FA code using provided TOTP secret');
        if (authCodeMode.source && isSteamSource(authCodeMode.source)) {
            return generateSteamGuard(totpSecret);
        }
        return totp.generate(totpSecret);
    }
    return requestAuthCodeWithRetries(authCodeMode, retryTimeout);
}
