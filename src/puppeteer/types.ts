import {Browser, BrowserConnectOptions, BrowserLaunchArgumentOptions, HTTPRequest, HTTPResponse, LaunchOptions, Page, Protocol} from 'puppeteer';

export {ElementHandle, KeyInput, CDPSession, Frame, JSHandle} from 'puppeteer';
export type Cookie = Protocol.Network.Cookie;
export type Response = HTTPResponse;
export type Request = HTTPRequest;

export type PuppeteerLaunchOptions = LaunchOptions & BrowserLaunchArgumentOptions & BrowserConnectOptions;
export type PuppeteerPage = Page;
export type PuppeteerBrowser = Browser;
