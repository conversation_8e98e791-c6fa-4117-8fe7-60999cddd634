import {LoginException, LoginExceptionType} from '../../../error/exceptions';
import {HTTPClient} from '../../../utils/http';
import {CheckSessionFunction} from '../../Scraper';
import {checkSteamSession} from '../common/checkSession';
import {SteamParams} from '../common/login';
import {SteamSession} from '../common/SteamSession';
import {steamGamesCheerioRequest, steamGamesRequest, steamGamesUrl} from './steamGamesRequest';

async function retrieveLoggedInUser(httpClient: HTTPClient): Promise<string> {
    const dashboardsPage = await steamGamesCheerioRequest('/dashboard', {httpClient});
    const username = dashboardsPage('.global_action_link').text();
    if (username === '') {
        throw new LoginException(LoginExceptionType.UNKNOWN);
    }
    return username;
}
export async function checkSteamGamesLogin(httpClient: HTTPClient) {
    const response = await steamGamesRequest('/dashboard', {httpClient, followRedirects: true});
    if (response.status === 200 && response.config.url && [`${steamGamesUrl}/dashboard`].includes(response.config.url)) {
        return true;
    }
    throw new LoginException(LoginExceptionType.SESSION_EXPIRED);
}

export const checkSteamGamesSession: CheckSessionFunction<SteamParams, SteamSession> = async (context) => {
    return checkSteamSession(context, checkSteamGamesLogin, retrieveLoggedInUser);
};
