# Direct Upload

To be able to perform a direct upload to Azure, without sending any files first to Scraper API, we need to first get Azure Shared Access Signature (SAS) for the blob in container, with write permissions, in form of `directUploadUrl`.

Such `directUploadUrl` is generated by indiebi-shared-package with corresponding `blobName`, and can be retrievied from Scraper API. Scraper API sets an expiration date for the SAS, so scrapers have 60 seconds to initiate the upload process.

After upload process is initiated, if needed, upload is allowed to take over 60 seconds, for example for very large reports.

After the report is uploaded to Azure, scrapers inform Scraper API about it, by sending additional `uploadInfo`, which later is used by Scraper API, to push proper events to queues, in order to initiate report processing by other services.
