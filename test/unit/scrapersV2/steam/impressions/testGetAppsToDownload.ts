import * as chai from 'chai';
import * as chaiAsPromised from 'chai-as-promised';
import * as moment from 'moment';
import * as td from 'testdouble';
import {NoProductsToDownloadException} from '../../../../../src/error/exceptions/NoProductsToDownloadException';
import getFullSetOfDataToScrape from '../../../../../src/scrapersV2/steam/games/impressionScrapingMetadata';
import {HTTPClient} from '../../../../../src/utils/http';

chai.use(chaiAsPromised);
const {expect} = chai;

function generateSteamImpressionsHTMLPage(apps: {id: string; name: string}[]): string {
    let html = `<div class="section">
                    <div class="section">
                </div>
            <h2>All Apps</h2>`;
    for (const app of apps) {
        html += `<div class="recent_app_row">
                    <div class="recent_app_name">
                        <a href="https://partner.steamgames.com/apps/landing/${app.id}">
                            ${app.name}
                        </a>
                    </div>
                    <div class="recent_app_type">Game</div>
					<div class="recent_app_links"></div>
					<div style="clear: both"></div>
				</div>`;
    }
    return html;
}

describe('Test Get Steam Visibility Apps', () => {
    afterEach(td.reset);

    it('should return NoProductsToDownloadException if no apps are scraped', async () => {
        const httpClient = td.object<HTTPClient>();
        const startDate = moment('2021-01-01');
        const endDate = moment('2021-01-01');

        td.when(httpClient.request(td.matchers.anything())).thenResolve({data: ''});

        try {
            await getFullSetOfDataToScrape({httpClient, progress: () => {}, startDate, endDate, ignoredProducts: []});
            expect.fail('Expected exception not thrown');
        } catch (error) {
            expect(error).to.be.instanceOf(NoProductsToDownloadException);
        }
    });

    it('should return all scraped apps if no ignored products are provided', async () => {
        const scrapedApps = [
            {id: '1', name: 'App_1'},
            {id: '2', name: 'App_2'},
            {id: '3', name: 'App_3'}
        ];
        const httpClient = td.object<HTTPClient>();
        const startDate = moment('2021-01-01');
        const endDate = moment('2021-01-01');

        td.when(httpClient.request(td.matchers.anything())).thenResolve({
            data: generateSteamImpressionsHTMLPage(scrapedApps)
        });

        const result = await getFullSetOfDataToScrape({httpClient, progress: () => {}, startDate, endDate, ignoredProducts: []});
        for (const item of scrapedApps) {
            expect(item.id).to.be.oneOf(result.map((app) => app.id));
            expect(item.name).to.be.oneOf(result.map((app) => app.name));
        }
    });

    it('should filter out ignored products from the scraped apps', async () => {
        const scrapedApps = [
            {id: '1', name: 'App_1'},
            {id: '2', name: 'App_2'},
            {id: '3', name: 'App_3'}
        ];
        const httpClient = td.object<HTTPClient>();
        const startDate = moment('2021-01-01');
        const endDate = moment('2021-01-01');

        td.when(httpClient.request(td.matchers.anything())).thenResolve({
            data: generateSteamImpressionsHTMLPage(scrapedApps)
        });

        const result = await getFullSetOfDataToScrape({httpClient, progress: () => {}, startDate, endDate, ignoredProducts: ['1', '3']});
        expect(result.length).to.equal(1);
        expect(result[0].id).to.equal('2');
        expect(result[0].name).to.equal('App_2');
    });

    it('should throw NoProductsToDownloadException if all products are ignored', async () => {
        const scrapedApps = [
            {id: '1', name: 'App_1'},
            {id: '2', name: 'App_2'}
        ];
        const httpClient = td.object<HTTPClient>();
        const startDate = moment('2021-01-01');
        const endDate = moment('2021-01-01');

        td.when(httpClient.request(td.matchers.anything())).thenResolve({
            data: generateSteamImpressionsHTMLPage(scrapedApps)
        });

        try {
            await getFullSetOfDataToScrape({httpClient, progress: () => {}, startDate, endDate, ignoredProducts: ['1', '2']});
            expect.fail('Expected exception not thrown');
        } catch (error) {
            expect(error).to.be.instanceOf(NoProductsToDownloadException);
        }
    });
});
