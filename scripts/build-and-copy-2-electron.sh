#!/bin/bash

if [[ "$OSTYPE" == "darwin"* ]]; then # Mac OSX
    TARGET=node20-macos-x64
    TARGET_SCRAPE_JS_DIR=$(find ~/Library/Application\ Support/indiebi-desktop/scrapers/binaries/scrapers -name "scrapers-cli-mac-*" ! -name "*-py" ! -name "*shadow*" -type d)
else # Linux
    TARGET=node20-linux-x64
    TARGET_SCRAPE_JS_DIR=$(find ~/.config/indiebi-desktop/scrapers/binaries/scrapers -name "scrapers-cli-linux-*" ! -name "*-py" ! -name "*shadow*" -type d)
fi

if [[ -z "$TARGET_SCRAPE_JS_DIR" ]]; then
    echo "Target directory not found!"
    exit 1
fi

./node_modules/.bin/tsc
./node_modules/.bin/pkg ./pkg.config.json --targets $TARGET --out-path=package_cli_local
cp ./package_cli_local/scrape "$TARGET_SCRAPE_JS_DIR"

echo "--------------------------"
echo "Binary has been copied to: $TARGET_SCRAPE_JS_DIR"
