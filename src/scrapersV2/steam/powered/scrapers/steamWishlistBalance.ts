import {<PERSON><PERSON><PERSON>, <PERSON>eerioAP<PERSON>, Element} from 'cheerio';
import * as _ from 'lodash';
import {Moment} from 'moment';
import {unparse} from 'papapar<PERSON>';
import {BrowserSession} from '../../../../browserV2';
import {Source} from '../../../../dataTypes';
import {downloadForOrganizationFinished, foundMultipleOrganizations, startingDownloadForOrganization} from '../../../../telemetry/events/multiorgEvents';
import {getDaysBetweenDates} from '../../../../utils/dates/getDaysBetweenDates';
import {FileExtension} from '../../../../utils/files/FileExtension';
import {generateFileName, saveFileToDownloads} from '../../../../utils/files/fileUtils';
import {HTTPClient} from '../../../../utils/http';
import {retryAction} from '../../../../utils/retryAction';
import {ProgressCallback} from '../../../ProgressCallback';
import {ScrapeFunction, Scraper} from '../../../Scraper';
import {prepareZipReports} from '../../../zip';
import {SteamParams, getLoginFunction} from '../../common/login';
import {steamScraperWrapper} from '../../common/scraperWrapper';
import {SteamOrganization} from '../../common/SteamSession';
import {checkSteamPoweredLogin, checkSteamPoweredSession} from '../checkSession';
import {getSteamPoweredSourceSideOrganizations, retrieveSourceOrgs} from '../multiorg';
import {SteamPackage} from '../scrapeSalesFromOrgs';
import {steamPoweredManualLoginDetails} from '../steamPoweredManualLoginDetails';
import {steamPoweredCheerioRequest, steamPoweredUrl} from '../steamPoweredRequest';

const dateFormat = 'YYYY-MM-DD';

const scrape: ScrapeFunction<SteamParams, BrowserSession> = async ({progress, getHttpClient}, startDate, endDate) => {
    const httpClient = getHttpClient();
    const metadata = {};

    const orgs = await retrieveSourceOrgs(httpClient);
    foundMultipleOrganizations(orgs.map((org) => org.name));

    for (const org of orgs) {
        startingDownloadForOrganization(org.name);
        progress(`Going to download regional wishlist for organization: ${org.name}.`);

        progress(`Scraping packages of ${org.name} (${org.id})`);
        const products = await getProducts(httpClient, org);

        progress(`Found ${products.length} products: ${products.map((p) => `${p.name} (${p.id})`).join(', ')}`);

        for (const product of products) {
            const filename = await downloadProductWishlists(progress, httpClient, org, product, startDate, endDate);
            metadata[filename] = {
                organization: org.name,
                productName: product.name,
                productId: product.id
            };
        }

        downloadForOrganizationFinished(org.name);
    }

    return prepareZipReports(startDate, endDate, Object.keys(metadata), Source.STEAM_WISHLIST_BALANCE, progress, metadata);
};

const downloadProductWishlists = async (
    progress: ProgressCallback,
    httpClient: HTTPClient,
    org: SteamOrganization,
    product: SteamPackage,
    startDate: Moment,
    endDate: Moment
): Promise<string> => {
    progress(`Downloading regional wishlist for ${product.name} (${product.id})`);

    const rows: string[][] = [];
    const days = getDaysBetweenDates(startDate, endDate);
    const daysPerChunk = 3;
    const dayChunks = _.chunk(days, daysPerChunk);

    for (const dayChunk of dayChunks) {
        for (const day of dayChunk) {
            await retryAction({
                target: async () => {
                    const countryWishlistPage = await steamPoweredCheerioRequest(generateCountryWishlistsLink(product, day), httpClient, org);
                    const data = extractWishlistsDataPerDay(countryWishlistPage);
                    rows.push(...data.map((d) => [day.format(dateFormat), product.id, d.country, d.balance.replaceAll(',', '')]));
                },
                maxAttempts: 5
            });
        }
    }

    const filename = generateFileName(Source.STEAM_WISHLIST_BALANCE, startDate, endDate, FileExtension.CSV, `${org.name} - ${product.name} (${product.id})`);
    const sortedRowsByDate = _.sortBy(rows, (row) => row[0]);
    const csv = unparse({fields: ['Date', 'App ID', 'Country', 'Wishlist balance'], data: sortedRowsByDate}, {quotes: true});
    await saveFileToDownloads(filename, csv);
    return filename;
};

async function getProducts(http: HTTPClient, org: SteamOrganization) {
    const $ = await steamPoweredCheerioRequest('/nav_games.php', http, org);
    const idFromUrlRegex = /details\/(\d+)\//;
    const packages = $(`a[href^="${steamPoweredUrl}/app/details"]`)
        .map((_i, el) => <SteamPackage>{id: $(el).attr('href')!.match(idFromUrlRegex)![1], name: $(el).text()})
        .toArray();

    return _.uniqBy(packages, 'id');
}

function extractWishlistsDataPerDay($: CheerioAPI): {country: string; balance: string}[] {
    function findDataRow(rows: Cheerio<Element>, content: string): Cheerio<Element> {
        function rowContainsContent(_i: number, el: Element): boolean {
            const children = $(el).children('td');
            return children.toArray().some((el) => $(el).text().trim().includes(content));
        }

        return rows.filter(rowContainsContent);
    }

    function extractDataFromRow(row: Cheerio<Element>): string {
        if (!row.length) return '0';
        const WISHLIST_ROW_INDEX = 4;
        const children = row.children('td');
        return $(children.get(WISHLIST_ROW_INDEX)).text().trim();
    }

    function isCountryRow(_i: number, el: Element): boolean {
        const link = $(el).find('td:eq(1) a');
        const href = link.attr('href') || '';
        return link.length > 0 && href.includes('country.php?countryCode');
    }

    // Find all rows with region/country name
    const regions = $('tr.publisher_row.spaceAbove');

    const countries = regions.filter(isCountryRow);

    const result: {country: string; balance: string}[] = [];

    countries.each(function (_i: number, el: Element) {
        const countryName = $(el).children('td:eq(1)').text().trim();

        // List all rows which contains data until the next country row
        const $dataRows = $(el).nextUntil('tr.publisher_row.spaceAbove');

        const balance = extractDataFromRow(findDataRow($dataRows, 'Wishlist balance'));

        result.push({country: countryName, balance});
    });

    return result;
}

function generateCountryWishlistsLink(product: SteamPackage, date: Moment): string {
    const day = date.format(dateFormat);
    return `/region/?appID=${product.id}&dateStart=${day}&dateEnd=${day}&alignPriorAnnual=Immediate`;
}

export const steamWishlistBalanceScraper: Scraper<SteamParams, BrowserSession> = steamScraperWrapper({
    login: getLoginFunction(Source.STEAM_WISHLIST_BALANCE, checkSteamPoweredLogin),
    scrape,
    getSourceSideOrganizations: getSteamPoweredSourceSideOrganizations,
    checkSession: checkSteamPoweredSession,
    manualLoginDetails: steamPoweredManualLoginDetails
});
