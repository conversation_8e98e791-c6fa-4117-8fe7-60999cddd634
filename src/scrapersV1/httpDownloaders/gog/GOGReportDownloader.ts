import * as cheerio from 'cheerio';
import {Moment} from 'moment';
import {UnparseObject, unparse} from 'papaparse';
import {TimeoutError} from 'puppeteer';
import {Browser} from '../../../browser/Browser';
import {Source} from '../../../dataTypes';
import {SourceIdentifier, SourceSideOrganization} from '../../../dataTypes/SourceSideOrganization';
import {MissingPermissionsException} from '../../../error/common/MissingPermissionsException';
import {CustomException} from '../../../error/CustomException';
import {errorTypes} from '../../../error/errorTypes';
import {LoginException, LoginExceptionType} from '../../../error/exceptions';
import {Invalid2FACode} from '../../../error/exceptions/Invalid2FACode';
import ManualLoginDetails from '../../../scrapersV2/ManualLoginDetails';
import {ReportManifest} from '../../../scrapersV2/ReportManifest';
import {createZipBasedOnManifest} from '../../../scrapersV2/zip';
import {downloadForOrganizationFinished, foundMultipleOrganizations, startingDownloadForOrganization} from '../../../telemetry/events/multiorgEvents';
import {isManualSession} from '../../../utils/conditions';
import {FileExtension} from '../../../utils/files/FileExtension';
import {generateFileName, saveFileToDownloads} from '../../../utils/files/fileUtils';
import {log} from '../../../utils/logger';
import {retryAction} from '../../../utils/retryAction';
import {DownloadReportResult} from '../../DownloadReportResult';
import {HttpReportDownloader} from '../../HttpReportDownloader';
import {LoginResult} from '../../LoginResult';
import {Report} from '../../Report';
import {ReportDownloaderProperties} from '../../ReportDownloaderProperties';
import {validateDownloader} from '../validateDownloader';
import * as GOGConstants from './GOGConstants';
import {
    DashCard,
    DashboardMetadata,
    DashboardQueryResult,
    Parameter,
    ProductsResponse,
    getDashboardMetadataQueryUrl,
    getProductsListQueryUrl,
    getSalesDataQueryUrl
} from './GOGConstants';

export class GOGReportDownloader implements HttpReportDownloader {
    private readonly login: string;
    private readonly password: string;
    private readonly browser: Browser;

    constructor({login, password, browser}: ReportDownloaderProperties) {
        validateDownloader(Source.GOG_SALES, {browser});
        this.login = login!;
        this.password = password!;
        this.browser = browser!;
    }

    public static manualLoginDetails: ManualLoginDetails = {
        url: GOGConstants.salesSummaryReportPageUrl,
        successSelector: {value: GOGConstants.logoutButton, type: 'css'}
    };

    public async fillSecondAuthenticationFactor(secondAuthenticationFactor: string): Promise<void> {
        if (secondAuthenticationFactor.length != GOGConstants.secondFactorAuthenticationCodeLength) {
            throw new Invalid2FACode();
        }
        await this.browser.getIFrame().click(GOGConstants.mailCodeInputSelector);
        for (let i = 0; i < GOGConstants.secondFactorAuthenticationCodeLength; i += 1) {
            await this.browser.press('Backspace');
        }
        log.info('Filling two factor authentication...');
        await this.browser.getIFrame().type(GOGConstants.mailCodeInputSelector, secondAuthenticationFactor);
        log.info('Clicking two factor authentication button...');
        await this.browser.getIFrame().click(GOGConstants.mailCodeConfirmationButtonSelector);

        if (!(await this.isLoggedIn())) {
            throw new Invalid2FACode();
        }
    }

    public get dualAuthMaxAttempts(): number {
        return GOGConstants.dualAuthMaxAttempts;
    }

    public async completeReportDownload(startDate: Moment, endDate: Moment): Promise<Report[]> {
        log.info('Opening report page');

        log.info("Getting user's organizations");
        const organizations = await this.getSourceSideOrganizations();
        log.info(`Found ${organizations.length} organizations: ${organizations.map((org) => org.name)}`);
        foundMultipleOrganizations(organizations.map((org) => org.name));

        const invalidOrganizations: SourceSideOrganization[] = [];
        for (const organization of organizations) {
            await this.changeOrganization(organization);
            // This item is visible after clicking, so for simplicity, we want to check for non-visible elements
            const hasPermission = await this.browser.elementExists(GOGConstants.hasPermissionSelector, Browser.defaultSelectorTimeout, false);
            if (!hasPermission) {
                log.warning(`Found organizations ${organization.name} without required permission`);
                invalidOrganizations.push(organization);
            }
        }
        if (invalidOrganizations.length !== 0 || organizations.length === 0) {
            throw new MissingPermissionsException(invalidOrganizations);
        } else {
            log.info('All organizations have required permissions');
        }

        const manifest = new ReportManifest(startDate, endDate, 'v3');

        for (const organization of organizations) {
            startingDownloadForOrganization(organization.name);

            await this.changeOrganization(organization);

            const jwt = await this.extractMetabaseJWT();
            const {parameter, dashcard} = await this.extractDashboardMetadata(jwt);

            const products = await this.extractProducts(jwt, parameter);
            log.info(`Found ${products.length} products`);
            log.debug('[GOG] Got list of products', {products});

            for (const product of products) {
                const filenames = await this.downloadProductData(startDate, endDate, product, dashcard, jwt, parameter);
                filenames.map((filename) => {
                    manifest.addFile(filename, {organization: organization.name, product, rawData: filename.endsWith('.json')});
                });
            }
            downloadForOrganizationFinished(organization.name);
        }

        log.debug('[GOG] Finished downloading reports');
        return createZipBasedOnManifest(manifest, Source.GOG_SALES, false);
    }

    public async downloadReport(startDate: Moment, endDate: Moment): Promise<DownloadReportResult> {
        log.info('Opening report page');
        await this.browser.goto(GOGConstants.salesSummaryReportPageUrl);
        log.info('Checking if user is required to log in...');
        const loginResult = await this.performLogin();
        if (loginResult.isFailedLogin()) {
            return DownloadReportResult.createLoginErrorResult(loginResult);
        }
        return DownloadReportResult.createReportResult(await this.completeReportDownload(startDate, endDate));
    }

    public async checkSession(): Promise<SourceIdentifier> {
        await this.browser.goto(GOGConstants.salesSummaryReportPageUrl);
        if (!(await this.isLoggedIn())) {
            throw new LoginException(LoginExceptionType.SESSION_EXPIRED);
        }
        await this.browser.goto('https://partners.gog.com/profile');
        const email = await this.browser.getElementText('li.list-group-item');

        return {
            id: email
        };
    }

    public async performLogin(): Promise<LoginResult> {
        await this.browser.goto(GOGConstants.salesSummaryReportPageUrl);
        if (await this.isLoggedIn()) {
            return LoginResult.createSuccessfulLoginResult();
        } else if (isManualSession(this.login, this.password)) {
            return LoginResult.createExpiredSessionResult();
        }
        log.info('Opening login window...');
        await this.browser.click(GOGConstants.loginPopupButton);
        await this.browser.waitForSelector(GOGConstants.loginIFrameSelector);
        await this.browser.setIFrameWithSelector(GOGConstants.loginIFrameSelector);

        await this.browser.getIFrame().waitForSelector(GOGConstants.passwordInputSelector);
        await this.browser.wait(GOGConstants.gogWaitTimeout);
        log.info('Checking if user needs to fill captcha.');

        if (await this.isCaptchaRequired()) {
            log.info('You will be asked to login manually soon.');
            return LoginResult.createCaptchaErrorResult();
        }

        await this.handleUsernameInput();

        log.info('Typing password...');
        await this.browser.getIFrame().clearAndType(GOGConstants.passwordInputSelector, this.password);
        log.info('Logging in...');
        await this.browser.getIFrame().click(GOGConstants.loginButtonSelector);
        if (await this.browser.getIFrame().elementExists(GOGConstants.incorrectPasswordXPathSelector, GOGConstants.gogWaitTimeout)) {
            return LoginResult.createAuthenticationExceptionResult('Wrong credentials, please enter correct username and/or password.');
        }
        return this.determineLoginResult();
    }

    private async isCaptchaRequired(): Promise<boolean> {
        const element = await this.browser.getIFrame().querySelector(GOGConstants.loginCaptchaSelector);

        if (!element) {
            return false;
        }

        return await element.isIntersectingViewport({threshold: 0});
    }

    private async handleUsernameInput(): Promise<void> {
        log.debug('Checking if username field exists');
        const usernameInputElement = await this.browser.getIFrame().querySelector(GOGConstants.usernameInputSelector);
        if (usernameInputElement) {
            const propertyHandleValue = await (await usernameInputElement.getProperty('disabled')).jsonValue();
            const isUsernameInputEnabled = !(propertyHandleValue as unknown as boolean);
            if (isUsernameInputEnabled) {
                log.info('Typing user name...');
                await this.browser.getIFrame().clearAndType(GOGConstants.usernameInputSelector, this.login);
            } else {
                log.info('Username field already set');
            }
        }
    }

    private async determineLoginResult(): Promise<LoginResult> {
        log.info('Checking for two factor authentication...');
        if ((await this.browser.getIFrame().isDetached()) && (await this.isLoggedIn())) {
            return LoginResult.createSuccessfulLoginResult();
        }
        if (await this.browser.getIFrame().elementExists(GOGConstants.mailCodeInputSelector)) {
            log.debug('[GOG] 2FA');
            return LoginResult.create2FactorAuthResult();
        }
        if (!(await this.isLoggedIn())) {
            return LoginResult.createAuthenticationExceptionResult('Login failed. Invalid credentials or too many login attempts in a row.');
        }
        return LoginResult.createSuccessfulLoginResult();
    }

    private async isLoggedIn(): Promise<boolean> {
        return this.browser.elementExists(GOGConstants.logoutButton);
    }

    public async getSourceSideOrganizations(): Promise<SourceSideOrganization[]> {
        log.debug('Obtaining raw organization data');
        const profilePageHtml: string = await this.browser.get('https://partners.gog.com/profile');
        const $ = cheerio.load(profilePageHtml);
        log.debug('Extracting raw organization data');

        return $('.list-group>a[href^="https://partners.gog.com/profile/changeRoleTo/"]')
            .map((_i, element) => {
                log.debug('Normalizing raw organization data');
                const id = $(element).attr('href')?.replace('https://partners.gog.com/profile/changeRoleTo/', '');
                const normalized = $(element)
                    .text()
                    .split('\n')
                    .map((data) => data.trim())
                    .filter((trimmed) => trimmed !== '' && trimmed != 'Publisher');
                return {
                    id: id!,
                    name: normalized[1],
                    hasScrapeBlockingIssues: false
                };
            })
            .toArray();
    }

    private async extractMetabaseJWT(): Promise<string> {
        await this.browser.goto(GOGConstants.salesSummaryReportPageUrl);

        const options = await this.browser.querySelector('iframe');
        const value = (await (await options?.getProperty('src'))?.jsonValue()) as string;
        const match = value?.match(/(?:dashboard\/)(ey.*)#/);
        const jwt = match ? match[1] : undefined;
        if (!jwt) {
            throw new CustomException({
                message: "Couldn't extract JWT from Metabase iframe",
                errorType: errorTypes.TEMPORARY_PORTAL_ISSUE
            });
        }
        return jwt;
    }

    private async extractDashboardMetadata(jwt: string): Promise<{dashcard: DashCard; parameter: Parameter}> {
        const {dashcards, parameters} = await this.browser.get<DashboardMetadata>(getDashboardMetadataQueryUrl(jwt));

        const dashcard = dashcards.find((d) => d.card.name === 'Sales Summary Periodical');
        const parameter = parameters.find((p) => p.name === 'Products');

        if (!dashcard || !parameter) {
            throw new CustomException({
                message: `Couldn't successfully extract required dashboard properties. Dashcard: ${dashcard}, parameter: ${parameter}`,
                suggestedAction: "Couldn't extract required dashboard properties",
                errorType: errorTypes.TEMPORARY_PORTAL_ISSUE
            });
        }

        return {
            dashcard,
            parameter
        };
    }

    private async extractProducts(jwt: string, parameter: Parameter): Promise<string[]> {
        const result = await this.browser.get<ProductsResponse>(getProductsListQueryUrl(jwt, parameter));
        return result.values.flat();
    }

    private async downloadProductData(startDate: Moment, endDate: Moment, product: string, dashcard: DashCard, jwt: string, parameter: Parameter): Promise<string[]> {
        log.info(`Downloading data for ${product}`);

        const rawData: DashboardQueryResult = await retryAction({
            target: async () => this.browser.get<DashboardQueryResult>(getSalesDataQueryUrl(jwt, dashcard, startDate, endDate, product)),
            maxAttempts: 3
        });

        const data = this.filterIncorrectData(rawData, product, parameter);

        const csvFilename = generateFileName(Source.GOG_SALES, startDate, endDate, FileExtension.CSV, product);
        const rawFilename = generateFileName(Source.GOG_SALES, startDate, endDate, FileExtension.JSON, product);

        const csvData: UnparseObject<Array<any>> = {
            fields: data.data.cols.map((c) => c.name),
            data: data.data.rows
        };

        await saveFileToDownloads(csvFilename, unparse(csvData, {delimiter: ';'}));
        await saveFileToDownloads(rawFilename, rawData);

        return [csvFilename, rawFilename];
    }

    private filterIncorrectData(data: DashboardQueryResult, product: string, productsParameter: Parameter) {
        // json_query parameters indicates which product is being queried. Sometimes, GOG returns random data that does not apply to the product.
        // In such case, the parameter is missing or the value is not the one we were asking for.
        const parameter = data.json_query?.parameters?.find((p) => p.id === productsParameter.id);
        if (!parameter || !(parameter.value as string[]).includes(product)) {
            return {
                ...data,
                data: {
                    ...data.data,
                    rows: []
                }
            };
        }
        return data;
    }

    private async changeOrganization(org: SourceSideOrganization): Promise<void> {
        log.info(`Changing organization to ${org.name} (${org.id})`);

        let attempt = 1;
        do {
            try {
                // When you change the organization, GOG displays a successful message box.
                // But changing the organization does not always work (even on normal browser when doing it by hand)
                // In such case we need to repeat the process. It usually works on second attempt.
                log.debug(`Attempt ${attempt}`);
                await this.browser.goto(`https://partners.gog.com/profile/changeRoleTo/${org.id}`);
                await this.browser.waitForSelector('.alert.alert-success');
                break;
            } catch (error) {
                attempt++;
                if (error instanceof TimeoutError) {
                    continue;
                }
                throw error;
            }
        } while (attempt <= 3);

        if (attempt > 3) {
            throw new CustomException({
                message: 'Unexpected problem with organization occurred',
                errorType: errorTypes.TEMPORARY_PORTAL_ISSUE
            });
        }
    }
}
