import {ReadStream, WriteStream} from 'fs';

export function waitTillStreamFinished(source: ReadStream, destination: WriteStream): Promise<void> {
    return new Promise((resolve, reject) => {
        let error: Error;

        source.on('error', (err) => {
            error = err;
            destination.close(); // TODO verify if that's necessary, please handle it in SCP-970
            reject(err);
        });

        destination.on('error', (err) => {
            error = err;
            reject(err);
        });

        destination.on('close', () => {
            if (!error) {
                resolve();
            }
        });
    });
}
