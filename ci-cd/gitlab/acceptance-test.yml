.acceptance-tests-for-all-sources: &acceptance-tests-for-all-sources
    stage: acceptance-test
    parallel:
        matrix:
            - DOMAIN:
                  - playstation_sales
                  - steam_sales
                  - steam_wishlists
                  - steam_impressions
                  - meta_rift_sales # also covers meta_quest_sales
                  - humble_sales
                  - gog_sales
                  - nintendo_sales
                  - epic_sales

run prod acceptance manual login details:
    needs: ['build', 'package cli']
    rules:
        - if: '$PIPELINE == "Release"'
    <<: *acceptance-tests-for-all-sources
    script:
        - echo "PASS" # TODO configure VCR tests with real binary file
