import {<PERSON>rowser} from '../browser/Browser';
import {Source} from '../dataTypes';
import {FeatureFlag} from '../scrapersV2/Scraper';

export interface ReportDownloaderProperties {
    appId?: string;
    login?: string;
    password?: string;
    browser?: Browser;
    source?: Source; // TODO this should be non optional?
    ignoredProductsArray?: string[];
    // secret for generating 2fa code. Should only be used by integration tests.
    totpSecret?: string;
    featureFlags?: FeatureFlag[];
}
