import {expect} from 'chai';
import {filterIgnoredItems} from '../../../src/utils/filters';

type TestType = {
    id: string;
    name: string;
};

const filterStringsArrayTestData = [
    {array: ['foo', 'bar'], ignored: ['bar'], expected: ['foo']},
    {array: ['foo', 'bar'], ignored: ['cat'], expected: ['foo', 'bar']},
    {array: ['foo', 'bar'], ignored: [], expected: ['foo', 'bar']},
    {array: ['foo', 'bar'], ignored: ['foo', 'bar'], expected: []}
];

const filterObjectsArrayTestData = [
    {array: [{id: '1', name: 'test'}], ignored: ['test'], nestedKey: 'id', expected: [{id: '1', name: 'test'}]},
    {array: [{id: '1', name: 'test'}], ignored: ['test'], nestedKey: 'name', expected: []},
    {array: [{id: '1', name: 'test'}], ignored: ['test'], nestedKey: 'noProp', expected: [{id: '1', name: 'test'}]},
    {array: [{id: '1', name: 'test'}], ignored: [], expected: [{id: '1', name: 'test'}]},
    {
        array: [
            {id: '1', name: 'test'},
            {id: '2', name: 'test'}
        ],
        ignored: ['test'],
        nestedKey: 'name',
        expected: []
    }
];

describe('filterIgnoredItems', () => {
    filterStringsArrayTestData.forEach(({array, ignored, expected}, index) => {
        it(`should filter strings array - run: ${index + 1}`, () => {
            const results = filterIgnoredItems(array, ignored);
            expect(results).deep.equal(expected);
        });

        it(`should work if ignoredArray is undefined - run ${index + 1}`, () => {
            const results = filterIgnoredItems(array);
            expect(results).deep.equal(array);
        });
    });

    filterObjectsArrayTestData.forEach(({array, ignored, nestedKey, expected}, index) => {
        it(`should filter Objects array - run: ${index + 1}`, () => {
            const results = filterIgnoredItems<TestType>(array, ignored, nestedKey as keyof TestType);
            expect(results).deep.equal(expected);
            results.forEach((result) => {
                expect(result.id).to.be.string;
                expect(result.name).to.be.string;
            });
        });

        it(`should work if ignoredArray is undefined (with nested key) - run ${index + 1}`, () => {
            const results = filterIgnoredItems<TestType>(array, undefined, nestedKey as keyof TestType);
            expect(results).deep.equal(array);
        });
    });
});
