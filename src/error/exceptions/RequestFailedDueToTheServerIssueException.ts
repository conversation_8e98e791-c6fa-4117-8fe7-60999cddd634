import {LogLevel} from '../../utils/logger';
import {CustomException} from '../CustomException';
import {errorTypes} from '../errorTypes';

export class RequestFailedDueToTheServerIssueException extends CustomException {
    constructor(error: Error, url: string) {
        super({
            error,
            message: `Request failed. URL: ${url}`,
            suggestedAction: 'Request to the external resource failed due to the platform issue. Please wait a few hours and try again.',
            telemetryLogLevel: LogLevel.WARN,
            errorType: errorTypes.TEMPORARY_PORTAL_ISSUE
        });
    }
}
