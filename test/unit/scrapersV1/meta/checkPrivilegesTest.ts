import * as chai from 'chai';
import * as chaiAsPromised from 'chai-as-promised';
import * as td from 'testdouble';
import {Browser} from '../../../../src/browser/Browser';
import {AccountIsNotVerifiedException} from '../../../../src/error/exceptions';
import {MetaReportDownloader} from '../../../../src/scrapersV1/httpDownloaders/meta/MetaReportDownloader';
import * as testData from './testData';

chai.use(chaiAsPromised);
const {expect} = chai;

describe('Meta scraper - check privileges', () => {
    const login = 'login';
    const password = 'passsword';
    const {source} = testData;

    it('should check that user has verified account', async () => {
        const fakeBrowser = td.object<Browser>();
        testData.prepareFakeCookiesAndMetaOrganizations(fakeBrowser, undefined, false);
        const oculusReportDownloader = new MetaReportDownloader({login, password, browser: fakeBrowser, source});
        await expect(oculusReportDownloader.validatePrivileges()).to.be.rejectedWith(AccountIsNotVerifiedException);
    });
});
