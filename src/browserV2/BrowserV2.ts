import * as path from 'path';
import {utc} from 'moment';
import * as puppeteer from 'puppeteer';
import {ScreenRecorder} from 'puppeteer';
import {isHeadlessBrowser} from '../browser/browserMethods';
import {setDownloadPath} from '../browser/download';
import * as config from '../config/ConfigService';
import {CustomException} from '../error/CustomException';
import {errorTypes} from '../error/errorTypes';
import {PuppeteerBrowser} from '../puppeteer';
import {Session} from '../scrapersV2/Scraper';
import {log} from '../utils/logger';
import {BrowserPage} from './BrowserPage';
import {getRecordingPath} from './debugHelpers';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const PuppeteerHar = require('puppeteer-har');

const targetClosedError = 'Protocol error (Target.closeTarget): Target closed';
export const userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36';

export class BrowserV2 {
    readonly page: BrowserPage;
    private readonly browser: PuppeteerBrowser;
    private har: any | undefined;
    private recorder: ScreenRecorder | undefined;

    private constructor(browser: PuppeteerBrowser, page: BrowserPage) {
        this.browser = browser;
        this.page = page;
    }

    public static async launch(session: Session): Promise<BrowserV2> {
        const pupeteerBrowser = await puppeteer.launch({
            headless: config.puppeteerHeadless(),
            slowMo: config.puppeteerSlowMo,
            devtools: config.puppeteerDevtools,
            args: ['--no-sandbox', '--disable-features=SameSiteByDefaultCookies,CookiesWithoutSameSiteMustBeSecure'],
            executablePath: config.chromiumExecutablePath()
        });
        const page = await BrowserV2.newPage(pupeteerBrowser);
        await page.setSession(session);

        const browser = new BrowserV2(pupeteerBrowser, page);

        if (config.isFeatureFlagEnabled('full-history-dump')) {
            if (config.getDumpDirPath()) {
                await browser.configureFullDump(page);
            } else {
                log.warning('Full history dump enabled, but dump directory not set. Feature will not be used!');
            }
        }

        return browser;
    }

    private static async newPage(browser: PuppeteerBrowser): Promise<BrowserPage> {
        const page = await browser.newPage();
        await page.setExtraHTTPHeaders({
            'Accept-Language': 'en'
        });

        await page.setViewport({width: 1366, height: 768});
        await page.setUserAgent(userAgent);

        if (config.downloadDirPath()) {
            await setDownloadPath(page, config.downloadDirPath());
        }

        // TODO this makes it impossible to push to S2 because of some requests/data is sooooo long it cannot be handled by the Azure Gateway
        // setupPageRequestsTelemetry(page);
        const client = await page.createCDPSession();

        return new BrowserPage(page, client);
    }

    public async close(): Promise<void> {
        if (this.recorder) {
            await this.recorder.stop();
        }
        if (this.har) {
            await this.har.stop();
        }

        const userAgent = await this.browser.userAgent();
        const pages = await this.browser.pages();
        for (const page of pages) {
            try {
                await page.close();
            } catch (error) {
                /**
                 * This is specific problem with closing browser with headless: false.
                 * The problem occurs with closing the last page of the browser, but it is needed to remove all processes and temporary file in the operating system.
                 * NOTE: Still valid for puppeteer v23.5.0 on 2024-10-03
                 */
                if (!isHeadlessBrowser(userAgent) && error.message === targetClosedError) {
                    continue;
                }
                throw error;
            }
        }
        try {
            await this.browser.close();
        } catch (error) {
            if (error.message.includes('Puppeteer was unable to kill the process which ran the browser binary.')) {
                /*
                TODO:
                  I don't think we need to throw an error and kill a potential scrape.
                  This sounds more like a cleanup issue than a failed scrape.
                 */
                throw new CustomException({
                    message: 'Application encountered unexpected problem with permissions to manage the browser.',
                    error,
                    errorType: errorTypes.BROWSER_ISSUE
                });
            }
            throw error;
        }
    }

    private async configureFullDump(page: BrowserPage): Promise<void> {
        log.info('Full history dump enabled, creating HAR and recording');
        const dateStr = utc().format('YYYY-MM-DD-HH-mm-ss.SSS');

        this.har = new PuppeteerHar(page.page);

        await this.har.start({
            saveResponse: true,
            path: path.join(config.getDumpDirPath()!, `${dateStr}_puppeteer.har`),
            captureMimeTypes: [
                'text/html',
                'text/csv',
                'text/css',
                'text/javascript',
                'application/json',
                'application/font-sfnt',
                'application/octet-stream',
                'image/svg+xml'
            ]
        } as any); // because types are incorrect
        this.recorder = await page.page.screencast({
            path: getRecordingPath(config.getDumpDirPath()!)
        });
    }
}
