import {expect} from 'chai';
import {afterEach} from 'mocha';
import * as td from 'testdouble';
import {TestDouble} from 'testdouble';
import {parseArguments} from '../../../src/cli/arguments';
import {exitCode} from '../../../src/cli/exitCodes';

describe('argument parsing should:', () => {
    afterEach(td.reset);

    const failingParse = (args, expectedErrorString) => {
        const exit = td.replace(process, 'exit');
        const error = td.replace(console, 'error');
        parseArguments(args, 'scrape.exe');
        td.verify(exit(exitCode.GENERAL_ERROR));
        td.verify(error(td.matchers.contains(expectedErrorString)));
    };

    it('accepts a scrape command', async () => {
        const exit: TestDouble<any> = td.replace(process, 'exit');
        const args = [
            'node',
            'scrape.js',
            'scrape',
            '--from=2021-05-05',
            '--to=2021-06-05',
            '--source=steam_sales',
            '--report-path=.',
            '--session-file=x',
            '--credentials={}',
            '--encryption-token=foo'
        ];
        const parsedArgs = parseArguments(args, 'scrape.exe');
        expect(parsedArgs.command).to.be.equal('scrape');
        expect(exit.callCount).to.be.undefined;
    });

    it('fail on invalid dates for --from and --to', async () => {
        failingParse(
            ['node', 'scrape.js', 'scrape', '--from=202dasdas5', '--to=2021-06-05', '--source=steam_sales', '--report-path=.', '--session-file=x', '--credentials={}'],
            'could not be parsed as a date'
        );
    });

    it('fail on invalid JSON in credentials', async () => {
        // node v18 and v20 have different error messages for invalid JSON so we need to check for both with regex
        failingParse(
            ['node', 'scrape.js', 'scrape', '--from=2021-06-05', '--to=2021-06-05', '--source=steam_sales', '--report-path=.', '--session-file=x', '--credentials={x}'],
            /(credentials: Expected property name)|(credentials: Unexpected token x in JSON at position 1)/
        );
    });

    it('fail without a command', async () => {
        failingParse(
            ['node', 'scrape.js', '--from=2021-06-05', '--to=2021-06-05', '--source=steam_sales', '--report-path=.', '--session-file=x', '--credentials={}'],
            'Not enough non-option arguments: got 0, need at least 1'
        );
    });

    it('accept env vars in place of command line arguments', async () => {
        try {
            const exit: TestDouble<any> = td.replace(process, 'exit');
            process.env.NDBI_ENCRYPTION_TOKEN = 'testing';
            const args = [
                'node',
                'scrape.js',
                'scrape',
                '--from=2021-05-05',
                '--to=2021-06-05',
                '--source=steam_sales',
                '--report-path=.',
                '--session-file=x',
                '--credentials={}'
            ];
            const parsedArgs = parseArguments(args, 'scrape.exe');
            expect(parsedArgs.encryptionToken).to.be.equal('testing');
            expect(exit.callCount).to.be.undefined;
        } finally {
            delete process.env.NDBI_ENCRYPTION_TOKEN;
        }
    });

    it('fail when required params are missing', async () => {
        delete process.env.NDBI_SOURCE; // prevent test fail by local variable
        failingParse(['node', 'scrape.js', 'scrape', '--from=2021-05-05', '--to=2021-06-05', '--report-path=.', '--session-file=x'], 'Missing required argument: source');
    });
});
