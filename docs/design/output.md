`Logs` are statements that are printed and to be used by the user in the electron-app.
`Traces` are statements that are representing additional information about the application in the given state.
`Events` are statements that represent the application switching from one state to another. 

There are no additional 'logging mechanisms', everything is printed on standard output, and by everything, I mean everything.
It's up to the receiving application to figure out what should be sent where.
All of the logs are to be further sent to our S2 service (which means that all of our logs are in it, along with their events)

# Scraper Lib
ALL of the logs/traces/events are to be sent to the S2 service.
Event entries are to be additionally sent to the s2 event endpoint
Logs should be displayed to the user in the electron-app
