import {log} from '../../../utils/logger';
import {SteamOrganization} from '../common/SteamSession';
import {SteamPackage, SteamSalesScrapingOptions} from './scrapeSalesFromOrgs';
import {steamPoweredCheerioRequest} from './steamPoweredRequest';

const dateFormat = 'YYYY-MM-DD';

export async function retrieveTotalUnitsSold(context: SteamSalesScrapingOptions, org: SteamOrganization) {
    context.progress('Retrieving the total unit sold amount for checksum purposes...');
    const startDateStr = context.startDate.format(dateFormat);
    const endDateStr = context.endDate.format(dateFormat);
    const path = `/?dateStart=${startDateStr}&dateEnd=${endDateStr}`;

    const $ = await steamPoweredCheerioRequest(path, context.httpClient, org);

    const totalUnitsStr = $('tr:contains("Total units") td:nth-child(3)').text(); // this can sometimes be NaN
    const totalUnits = parseInt(totalUnitsStr.replace(',', ''));
    context.progress(`Total units sold (organization id: ${org.id}): ${totalUnits}`);

    /**
     * We had some issues with Kepler regarding the total units sold.
     * Adding the table content for debug purposes.
     */
    const totalUnitsTable = $('td')
        .map((_, el) => $(el).text().replace(/\t/g, ''))
        .filter((_, el) => el.length > 0)
        .get();

    const startIndex = totalUnitsTable.indexOf('Top Steam packages - units (all platforms)');
    const stopIndex = totalUnitsTable.indexOf('Top Mac packages - revenue');

    log.debug('Top steam packages info', {TopSteamUnitsTable: JSON.stringify(totalUnitsTable.slice(startIndex, stopIndex))});

    return totalUnits;
}

export async function retrievePackageUnitsSold(ctx: SteamSalesScrapingOptions, org: SteamOrganization, pkg: SteamPackage) {
    ctx.progress(`Retrieving the total unit sold amount (package ID ${pkg.id}) for checksum purposes...`);
    const startDateStr = ctx.startDate.format(dateFormat);
    const endDateStr = ctx.endDate.format(dateFormat);

    const path = `/package/details/${pkg.id}/?dateStart=${startDateStr}&dateEnd=${endDateStr}`;

    const $ = await steamPoweredCheerioRequest(path, ctx.httpClient, org);

    const totalUnitsStr = $('tr:contains("Total units") td:nth-child(3)').text(); // this can sometimes be NaN
    const totalUnits = parseInt(totalUnitsStr.replace(',', ''));

    ctx.progress(`Total units sold (package id: ${pkg.id}): ${totalUnits}`);
    return totalUnits;
}
