import * as fs from 'fs/promises';
import * as _ from 'lodash';
import {printTraceWithError} from '../../cli/messaging';
import {CustomException} from '../../error/CustomException';
import {errorTypes} from '../../error/errorTypes';
import {log} from '../logger';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const Cryptr = require('cryptr');

type OPERATION = 'load' | 'save';

const jsonStringifiedNull = JSON.stringify('\0');

export class EmptyFileException extends CustomException {
    constructor(operation: OPERATION, error?: Error) {
        super({message: `Cannot ${operation} file`, error, errorType: errorTypes.INTERNAL_SCRAPER_ISSUE});
    }
}

class EmptySessionFileException extends CustomException {
    constructor(operation: OPERATION, error?: Error | CustomException) {
        super({
            message: `Cannot ${operation} session file`,
            suggestedAction: `An error occurred while trying to ${operation} the session. If the issue persists please try reconfiguring your scraper. If that fails please contact IndieBI support.`,
            errorType: errorTypes.SESSION_EXPIRED,
            error
        });
    }
}

export class EncryptedJsonFile {
    private readonly filename: string;

    private readonly cryptr: typeof Cryptr;

    constructor(filename: string, encryptionKey?: string) {
        this.filename = filename;
        if (encryptionKey) {
            this.cryptr = new Cryptr(encryptionKey);
        }
    }

    public async load(): Promise<any> {
        let content: string;
        try {
            content = await fs.readFile(this.filename, 'utf8');
            if (this.cryptr) {
                content = this.cryptr.decrypt(content);
            }
        } catch (error) {
            if (_.get(error, 'code') === 'ENOENT') {
                return {};
            }
            throw error;
        }

        try {
            return JSON.parse(content);
        } catch (error) {
            log.debug('Failed to parse JSON content, probably file is empty');
            printTraceWithError(error);
            throw new EmptyFileException('load', error);
        }
    }

    public async save(obj: unknown, failOnEmptySaveAttempt = true): Promise<void> {
        let stringifyContent = JSON.stringify(obj);
        /*
            For reasons unknown at the moment the session passed to this function is sometimes an unicode null '\0'.
            This is an issue that can break the scraper and we want to eliminate it.
            However, sometimes session saving is used as a "optimization" mechanism allowing for automatic session extension.
            In such cases we do not want to fail the entire scrape just because the optimization failed
         */
        if (!stringifyContent || stringifyContent.length === 0 || stringifyContent === jsonStringifiedNull) {
            if (failOnEmptySaveAttempt) {
                const emptyFileException = new EmptyFileException('save');
                printTraceWithError(emptyFileException);
                throw emptyFileException;
            }
            return;
        }

        if (this.cryptr) {
            stringifyContent = this.cryptr.encrypt(stringifyContent);
        }

        await fs.writeFile(this.filename, stringifyContent);
    }
}

export class JsonFile extends EncryptedJsonFile {
    constructor(filename: string) {
        super(filename);
    }
}

export class SessionFile extends EncryptedJsonFile {
    constructor(filename: string, encryptionKey?: string) {
        super(filename, encryptionKey);
    }

    public async save(obj: unknown, failOnEmptySaveAttempt = true): Promise<void> {
        try {
            await super.save(obj, failOnEmptySaveAttempt);
        } catch (error) {
            if (error instanceof EmptyFileException) {
                const emptySessionFileException = new EmptySessionFileException('save', error);
                printTraceWithError(emptySessionFileException);
                throw emptySessionFileException;
            }
        }
    }

    public async load(): Promise<any> {
        try {
            return await super.load();
        } catch (error) {
            if (error instanceof EmptyFileException) {
                //TODO trowing this error should be enough, why the extra logs? (here and in above methods)
                const emptySessionFileException = new EmptySessionFileException('load', error);
                printTraceWithError(emptySessionFileException);
                throw emptySessionFileException;
            }
        }
    }
}
