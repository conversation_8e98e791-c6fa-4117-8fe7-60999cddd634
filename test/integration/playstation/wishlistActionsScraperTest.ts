import {expect} from 'chai';
import * as moment from 'moment';
import {Source} from '../../../src/dataTypes';
import {FileExtension} from '../../../src/utils/files/FileExtension';
import {createCheckSessionTest} from '../checkSessionTestGenerator';
import {createGetSourceSideOrgTest} from '../getSourceSideOrganizationTestGenerator';
import {createScrapeTest} from '../scrapeTestGenerator';
import {LOGIN_ARGS, getScrapeArgs} from '../utils/ArgsProvider';
import {removeIfFilesExist, runCliMainWithConvertedArgs, zipEntryToString} from '../utils/Utils';
import {expectedPlayStationLoginResult, expectedPlayStationOrganizationsResult} from './common';

describe('PlayStation Wishlist actions runner:', () => {
    const defaultStartDateString = '2023-08-01';
    const defaultEndDateString = '2023-08-10';
    const recentStartDate = moment.utc().subtract(3, 'days').format('YYYY-MM-DD');
    const recentEndDate = moment.utc().subtract(5, 'days').format('YYYY-MM-DD');
    const loginArgs = LOGIN_ARGS.for_playstation(Source.PLAYSTATION_WISHLIST_ACTIONS);
    const scrapeArgs = getScrapeArgs(loginArgs, defaultStartDateString, defaultEndDateString, FileExtension.ZIP);
    const recentScrapeArgs = getScrapeArgs(loginArgs, recentStartDate, recentEndDate, FileExtension.ZIP);

    before(async function () {
        await removeIfFilesExist(scrapeArgs.testedFilePath!);
        await runCliMainWithConvertedArgs(loginArgs);
    });

    createCheckSessionTest(loginArgs, expectedPlayStationLoginResult, true);

    createGetSourceSideOrgTest(loginArgs, expectedPlayStationOrganizationsResult, true);

    createScrapeTest(
        'should download PlayStation zipped data',
        scrapeArgs,
        [`${Source.PLAYSTATION_WISHLIST_ACTIONS}-${defaultStartDateString}_${defaultEndDateString}.json`],
        async (dataFiles) => {
            expect(dataFiles).to.be.an.instanceof(Array).and.have.lengthOf(1);
            const {numRows, numColumns} = await JSON.parse(zipEntryToString(dataFiles[0]));
            expect(numRows).to.be.equal(505);
            expect(numColumns).to.be.equal(20);
        }
    );

    createScrapeTest('should download recent PlayStation zipped data', recentScrapeArgs, [
        `${Source.PLAYSTATION_WISHLIST_ACTIONS}-${recentStartDate}_${recentEndDate}.json`
    ]);
});
