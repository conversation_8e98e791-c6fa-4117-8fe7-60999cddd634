import * as cheerio from 'cheerio';
import {HTTPClient} from '../../../utils/http';
import {SteamRequestOptions, requestSteamFileWithCorruptionHandling, steamRequest} from '../common/requests';
import {SteamOrganization} from '../common/SteamSession';

export const steamPoweredUrl = 'https://partner.steampowered.com';

export async function steamPoweredRequest<T = any>(path: string, {httpClient, org, isStream = false, followRedirects = false}: SteamRequestOptions) {
    return steamRequest<T>({
        baseUrl: steamPoweredUrl,
        path,
        method: 'GET',
        httpClient,
        extraCookies: org ? {steamworksRunas: `${org.id}`} : undefined,
        isStream,
        followRedirects
    });
}

export async function steamPoweredCheerioRequest(path: string, httpClient: HTTPClient, org?: SteamOrganization) {
    const response = await steamPoweredRequest(path, {httpClient, org, isStream: false});
    return cheerio.load(response.data);
}

export async function downloadSteamPoweredFile(httpClient: HTTPClient, path: string, orgId: string) {
    return requestSteamFileWithCorruptionHandling({
        httpClient,
        baseUrl: steamPoweredUrl,
        path,
        method: 'GET',
        isStream: false,
        followRedirects: false,
        extraCookies: {steamworksRunas: orgId}
    });
}

export async function downloadSteamPoweredFileViaPost(httpClient: HTTPClient, path: string, orgId: string, formData: Record<string, string>) {
    return requestSteamFileWithCorruptionHandling({
        httpClient,
        baseUrl: steamPoweredUrl,
        formData,
        path,
        method: 'POST',
        isStream: true,
        followRedirects: true,
        extraCookies: {steamworksRunas: orgId}
    });
}
