import {getAuthCode} from '../../apiCommunication/authCodes';
import {Browser<PERSON><PERSON>, BrowserSession, classContains, classStartsWith} from '../../browserV2';
import {printDualAuth, printDualAuthSuccess} from '../../cli/messaging';
import {Portal, Source} from '../../dataTypes';
import {LoginException, LoginExceptionType} from '../../error/exceptions';
import {isManualSession} from '../../utils/conditions';
import {log} from '../../utils/logger';
import {ProgressCallback} from '../ProgressCallback';
import {EpicParams} from './epic';

const loginWithSteamButton = '#login-with-steam';
const loggedInUserSelector = '.OpenID_loggedInName';
const loginButtonSelector = `${classStartsWith('newlogindialog_SubmitButton')}[type="submit"]`;
const usernameInputSelector = `${classStartsWith('newlogindialog_TextInput')}[type="text"]`;
const passwordInputSelector = `${classStartsWith('newlogindialog_TextInput')}[type="password"]`;
const formErrorSelector = classContains('newlogindialog_Danger');
const acceptAllCookiesSelector = '#acceptAllButton';

const mfaAppChallengeSelector = classStartsWith('newlogindialog_AwaitingAppConfIcon'); // selector to confirm
const mfaEmailChallengeSelector = classStartsWith('newlogindialog_AwaitingEmailConfIcon');
const mfaEmailInputSelector = `${classStartsWith('segmentedinputs_SegmentedCharacterInput')}`;
const steamAccountConfirmButtonSelector = '#imageLogin';

export const hasSessionSteamCookies = (session: BrowserSession): boolean => !!session.cookies.find((cookie) => cookie.name.startsWith('steamRefresh_'));

export const loginWithSteam = async (progress: ProgressCallback, params: EpicParams, page: BrowserPage): Promise<BrowserSession> => {
    // precondition: page navigated to Epic Social Login (/docs/images/epicSocialLogins.jpg)

    progress('Logging in with Steam');
    progress('Opening login window');
    const steamPopup = await page.clickAndOpenPopup(loginWithSteamButton);

    if (await steamPopup.selectorTest({successSelector: loggedInUserSelector, failureSelector: usernameInputSelector})) {
        // already logged into steam
        await steamPopup.click(steamAccountConfirmButtonSelector);
        return page.getSession();
    }

    if (isManualSession(params.user, params.password)) {
        throw new LoginException(LoginExceptionType.SESSION_EXPIRED);
    }

    progress('Typing user name');
    await steamPopup.input(usernameInputSelector, params.user);

    progress('Typing password');
    await steamPopup.input(passwordInputSelector, params.password);

    progress('Check for cookies popup');
    if (await steamPopup.checkSelectorExists(acceptAllCookiesSelector)) {
        await steamPopup.click(acceptAllCookiesSelector);
    }

    progress('Logging in');
    await steamPopup.click(loginButtonSelector);

    if (
        !(await steamPopup.selectorTestMulti({
            successSelectors: [mfaEmailChallengeSelector, mfaAppChallengeSelector],
            failureSelectors: [formErrorSelector]
        }))
    ) {
        const errorMessage = await steamPopup.text(formErrorSelector);
        throw new LoginException(
            LoginExceptionType.CREDENTIALS_INVALID,
            `Error logging into Epic using Steam credentials: ${errorMessage}. Did you provide correct username and password?`
        );
    }

    if (await steamPopup.selectorTest({successSelector: mfaEmailChallengeSelector, failureSelector: mfaAppChallengeSelector})) {
        await handleSteamDualAuth(progress, steamPopup);
    } else {
        log.debug('Steam Guard login attempt');
        await handleSteamDualAuth(progress, steamPopup);
    }

    return page.getSession();
};

const handleSteamDualAuth = async (progress: ProgressCallback, steamPopup: BrowserPage) => {
    progress('Waiting for Code from Steam');
    printDualAuth({portal: Portal.STEAM, attempt: 1, maxAttempts: 1});
    const code = await getAuthCode({portal: Portal.STEAM, source: Source.EPIC_SALES});
    progress(`Entering Email Code: ${code}`);
    await steamPopup.input(mfaEmailInputSelector, code);
    const isCodeCorrect = await steamPopup.selectorTest({successSelector: steamAccountConfirmButtonSelector, failureSelector: formErrorSelector});
    if (!isCodeCorrect) {
        throw new LoginException(LoginExceptionType.MFA_INVALID, 'Submitted Steam Email Code is invalid. Did you configure email forwarding rules correctly?');
    }
    printDualAuthSuccess({portal: Portal.STEAM, attempt: 1});
    await steamPopup.click(steamAccountConfirmButtonSelector);
};
