export const errorTypes = {
    IP_BANNED: 'IP_BANNED',
    INCORRECT_CREDENTIALS: 'INCORRECT_CREDENTIALS',
    CREDENTIALS_EXPIRED: 'CREDENTIALS_EXPIRED',
    INCORRECT_2FA: 'INCORRECT_2FA',
    TIMEOUT_2FA: 'TIMEOUT_2FA',
    TIMEOUT_2FA_MOBILE_APP_APPROVAL: 'TIMEOUT_2FA_MOBILE_APP_APPROVAL',
    MISSING_2FA: 'MISSING_2FA',
    TOO_MANY_2FA_ATTEMPTS: 'TOO_MANY_2FA_ATTEMPTS',
    MISSING_CAPTCHA: 'MISSING_CAPTCHA',
    MISSING_PERMISSIONS: 'MISSING_PERMISSIONS',
    MANUAL_ACTION_REQUIRED: 'MANUAL_ACTION_REQUIRED',
    SESSION_EXPIRED: 'SESSION_EXPIRED',
    NO_INTERNET: 'NO_INTERNET',
    UNEXPECTED_ERROR: 'UNEXPECTED_ERROR', // this should be only used as a last resort
    TEMPORARY_PORTAL_ISSUE: 'TEMPORARY_PORTAL_ISSUE',
    SECRET_EXPIRED: 'SECRET_EXPIRED',
    RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',

    //---- Need to be added in electron?
    BROWSER_ISSUE: 'BROWSER_ISSUE', // It can be hard to determine this and temporary portal issue and portal changed
    NETWORK_ISSUE: 'NETWORK_ISSUE',
    CONFIGURATION_ISSUE: 'CONFIGURATION_ISSUE',
    INTERNAL_SCRAPER_ISSUE: 'INTERNAL_SCRAPER_ISSUE',
    NOT_SUPPORTED: 'NOT_SUPPORTED'
} as const;
export type ErrorType = typeof errorTypes[keyof typeof errorTypes];
