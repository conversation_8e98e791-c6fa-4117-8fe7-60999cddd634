# non operational cases
#                  - epic/scraper # commenting out since we do not have any sustainable automated login capabilities
#                  - steam/salesLowPermissionScraper # commenting out due to lack of account

integration test:
    stage: test
    interruptible: true
    retry: 2
    needs: ['build']
    rules:
        - if: '$PIPELINE == "Nightly"'
          when: always
          allow_failure: false
        - when: manual
          allow_failure: true

    parallel:
        matrix:
            - CASE:
                  - gog/scraper
                  - humble/scraper
                  - meta/questScraper
                  - meta/riftScraper
                  - meta/notVerifiedAccountScraper
                  - nintendo/scraper
                  - playstation/salesScraper
                  - playstation/wishlistActionsScraper
                  - steam/impressionsScraper
                  - steam/salesScraper
                  - steam/wishlistsScraper
                  - steam/discountsScraper
                  - steam/wishlistBalanceScraper
#                  - steam/multiorgSteamGamesGetSourceSideOrgs # SH account does not have access to multiple organizations anymore
    before_script:
        # Create directories for the failure report, screenshots and html dumps
        - mkdir -p failures dumps downloads
        - touch failures/${CASE////_} # replace / with _ to create a file instead of a dir
    script:
        - ./ci-cd/run-integration-test.sh $CASE
    after_script:
        # Because artifacts paths can only be relative paths, we need to copy all screenshots and dumps to a new directory
        - cp -R ~/.local/share/indieBI/errors/. dumps/
        - cp -R ~/.local/share/indieBI/downloads/. downloads/
        # To avoid reporting successful runs
        - if [ "$CI_JOB_STATUS" == "success" ]; then rm failures/${CASE////_}; fi

    # Pass variables to failure report (handle-failed)
    artifacts:
        when: always
        paths:
            - failures/
            - dumps/
            - downloads/
        reports:
            coverage_report:
                coverage_format: cobertura
                path: coverage/cobertura-coverage.xml
            junit: coverage/junit-coverage.xml
