import {expect} from 'chai';
import * as sinon from 'sinon';

export async function runInFakeTime(fun: () => Promise<any>, expectedTimeout?: number): Promise<any> {
    const clock = sinon.useFakeTimers();
    try {
        const [result, timeTakenMs] = await Promise.all([fun(), clock.runAllAsync()]);
        if (expectedTimeout) {
            expect(timeTakenMs).to.be.equal(expectedTimeout, 'Routine ran in fake time had a different than expected timeout');
        }
        return result;
    } finally {
        clock.restore();
    }
}

export async function expectTimeout(fun: () => Promise<any>, expectedTimeout?: number): Promise<any> {
    return runInFakeTime(fun, expectedTimeout);
}
