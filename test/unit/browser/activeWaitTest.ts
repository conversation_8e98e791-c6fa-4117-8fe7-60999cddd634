import {expect} from 'chai';
import {TimeoutError} from 'puppeteer';
import rewire = require('rewire');
import * as td from 'testdouble';
import {elementDoesNotExist, intervalSearchAnyElementExists, intervalSearchElementExists} from '../../../src/browser/activeWait';
import {ElementHandle, PuppeteerPage} from '../../../src/puppeteer';
import {expectTimeout, runInFakeTime} from '../../utils/fakeTime';
import {shouldThrowAsync} from '../../utils/shouldThrowAsync';

describe('Active wait test:', function () {
    const activeWait = rewire('../../../src/browser/activeWait');
    const finalTimeoutOffset: number = activeWait.__get__('finalTimeoutOffset');
    const timeout: number = activeWait.__get__('intervalTimeout');
    const defaultOptions = {timeout, visible: true};
    const fakeSelector = 'asd';
    const secondFakeSelector = 'dsa';
    const fakeXPathSelector = 'div > p';

    describe('intervalSearchElementExists', function () {
        it('should find element immediately when looking for it by a css selector', async function () {
            const pageStub = td.object<PuppeteerPage>();
            const elementHandleStub = td.object<ElementHandle>();
            td.when(pageStub.waitForSelector(fakeSelector, defaultOptions)).thenResolve(elementHandleStub);

            const elementExists = await runInFakeTime(async () => intervalSearchElementExists(pageStub, fakeSelector));
            expect(elementExists).to.be.equal(elementHandleStub);
        });

        it('should find element immediately when looking for it by xpath selector', async function () {
            const pageStub = td.object<PuppeteerPage>();
            const elementHandleStub = td.object<ElementHandle>();
            td.when(pageStub.waitForSelector(`::-p-xpath(${fakeXPathSelector})`, defaultOptions)).thenResolve(elementHandleStub);

            const elementExists = await runInFakeTime(async () => intervalSearchElementExists(pageStub, fakeXPathSelector, {isXPath: true}));
            expect(elementExists).to.be.equal(elementHandleStub);
        });

        it('should propagate non timeout errors', async () => {
            const pageStub = td.object<PuppeteerPage>();
            const error = new Error('fiz baz foo');
            td.when(pageStub.waitForSelector(fakeSelector, defaultOptions)).thenReject(error);

            await runInFakeTime(async () => shouldThrowAsync(async () => intervalSearchElementExists(pageStub, fakeSelector), error));
        });

        it('should timeout when element does not exists for too long', async () => {
            const pageStub = td.object<PuppeteerPage>();
            td.when(pageStub.waitForSelector(fakeSelector, defaultOptions)).thenReject(new TimeoutError(''));

            await expectTimeout(
                async () =>
                    shouldThrowAsync(
                        async () => intervalSearchElementExists(pageStub, fakeSelector, {timeout: 1500}),
                        new TimeoutError(`Selector '${fakeSelector}' not found.`)
                    ),
                1500 + finalTimeoutOffset
            );
        });

        it('should find element on third try', async () => {
            const pageStub = td.object<PuppeteerPage>();
            let internalCounter = 0;
            td.when(pageStub.waitForSelector(fakeSelector, defaultOptions)).thenDo(() => {
                if (internalCounter < 3) {
                    internalCounter++;
                    throw new TimeoutError('');
                }

                return null;
            });
            const result = await expectTimeout(async () => intervalSearchElementExists(pageStub, fakeSelector), 3 * timeout);
            expect(result).to.be.null;
        });
    });

    describe('elementDoesNotExists', function () {
        it('should propagate non timeout errors', async () => {
            const pageStub = td.object<PuppeteerPage>();
            const error = new Error('fiz baz foo');
            td.when(pageStub.waitForSelector(fakeSelector, defaultOptions)).thenReject(error);

            await runInFakeTime(async () => shouldThrowAsync(async () => elementDoesNotExist(pageStub, fakeSelector), error));
        });

        it('should not find element that does not exist', async () => {
            const pageStub = td.object<PuppeteerPage>();
            td.when(pageStub.waitForSelector(fakeSelector, defaultOptions)).thenReject(new TimeoutError(''));

            const result = await runInFakeTime(async () => elementDoesNotExist(pageStub, fakeSelector));
            expect(result).to.be.undefined;
        });

        it('should timeout when element exists for too long', async () => {
            const pageStub = td.object<PuppeteerPage>();
            td.when(pageStub.waitForSelector(fakeSelector, defaultOptions)).thenResolve(null as any);

            await expectTimeout(
                async () =>
                    shouldThrowAsync(async () => elementDoesNotExist(pageStub, fakeSelector, 1500), new TimeoutError(`Selector '${fakeSelector}' is still visible.`)),
                1500 + finalTimeoutOffset
            );
        });

        it('should not find element on third try', async () => {
            const pageStub = td.object<PuppeteerPage>();
            let internalCounter = 0;
            td.when(pageStub.waitForSelector(fakeSelector, defaultOptions)).thenDo(() => {
                if (internalCounter < 3) {
                    internalCounter++;
                    return null;
                }
                throw new TimeoutError('');
            });

            const result = await expectTimeout(async () => elementDoesNotExist(pageStub, fakeSelector), 3 * timeout);
            expect(result).to.be.undefined;
        });
    });

    describe('intervalSearchAnyElementExists', function () {
        it('should find one element when looking for it by a css selector', async () => {
            const pageStub = td.object<PuppeteerPage>();
            const elementHandleStub = td.object<ElementHandle>();
            td.when(pageStub.waitForSelector(fakeSelector, defaultOptions)).thenResolve(elementHandleStub);
            td.when(pageStub.waitForSelector(secondFakeSelector, defaultOptions)).thenReject(new Error('error'));

            const existingElements = await runInFakeTime(async () => intervalSearchAnyElementExists(pageStub, [fakeSelector, secondFakeSelector]));
            expect(existingElements).to.be.an.instanceOf(Array).with.lengthOf(1);
            expect(existingElements[0]).to.be.deep.equal(elementHandleStub);
        });

        it('should find two elements when looking for it by a css selectors', async () => {
            const pageStub = td.object<PuppeteerPage>();
            const elementHandleStub = td.object<ElementHandle>();
            const secondElementHandleStub = td.object<ElementHandle>();
            td.when(pageStub.waitForSelector(fakeSelector, defaultOptions)).thenResolve(elementHandleStub);
            td.when(pageStub.waitForSelector(secondFakeSelector, defaultOptions)).thenResolve(secondElementHandleStub);

            const existingElements = await runInFakeTime(async () => intervalSearchAnyElementExists(pageStub, [fakeSelector, secondFakeSelector]));
            expect(existingElements).to.be.an.instanceOf(Array).with.lengthOf(2);
            expect(existingElements).to.deep.include(elementHandleStub);
            expect(existingElements).to.deep.include(secondElementHandleStub);
        });

        it('should throw an error when no elements are found', async () => {
            const pageStub = td.object<PuppeteerPage>();
            const error = new Error('error');
            td.when(pageStub.waitForSelector(fakeSelector, defaultOptions)).thenReject(error);

            await runInFakeTime(async () =>
                shouldThrowAsync(async () => intervalSearchAnyElementExists(pageStub, [fakeSelector]), new TimeoutError('Selectors not found'))
            );
        });
    });
});
