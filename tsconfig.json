{"compilerOptions": {"experimentalDecorators": true, "outDir": "./dist", "lib": ["dom", "ES2021"], "module": "commonjs", "target": "ES2021", "sourceMap": true, "resolveJsonModule": true, "strictNullChecks": true, "allowJs": true, "allowSyntheticDefaultImports": true}, "exclude": ["node_modules", ".idea", "built", ".git", "coverage", ".nyc_output"], "include": ["./src/**/*", "./src/config/default.json", "./test/**/*"], "ts-node": {"transpileOnly": false, "files": true}}