For certain platforms, we're now supporting TOTP (Time-based One-Time Password) code generation based on user-specified secrets.

TOTP is the secret sauce behind Google Authenticator and compatible apps. Steam Guard Authenticator also uses a slightly modified version of TOTP.

Fortunately for us, TOTP (as well as Steam Guard codes) is well documented and has existing open-source implementations in NPM. That means that as long as we are provided with a shared secret by the user, we can generate 2FA codes for platforms without communicating with any external services.

[src/utils/totp.ts](../../src/utils/totp.ts) contains an implementation of TOTP-generation routines based on imported NPM packages.

The first scraper to use these is the new implementation of EPIC scraper ([src/utils/totp.ts](../../src/scrapersV2/epic.ts)).
