import {Source} from '../../../src/dataTypes';
import {LoginException, LoginExceptionType} from '../../../src/error/exceptions';
import {shouldThrowAsync} from '../../utils/shouldThrowAsync';
import {getLoginArgs} from '../utils/ArgsProvider';
import {runCliMainWithConvertedArgs} from '../utils/Utils';

describe('Meta (notVerifiedAccountScraper) runner:', () => {
    const loginArgs = getLoginArgs(Source.META_RIFT_SALES, 'oculus_notverified', 'oculus_notverified.json');

    it('should throw LoginException from Meta Quest in case user account is not verified', async function () {
        await shouldThrowAsync(
            () => runCliMainWithConvertedArgs(loginArgs),
            new LoginException(
                LoginExceptionType.CREDENTIALS_INVALID,
                'insufficientPrivilegeLevel Your account is not verified. Please log in to your Meta account and verify it.'
            )
        );
    });
});
