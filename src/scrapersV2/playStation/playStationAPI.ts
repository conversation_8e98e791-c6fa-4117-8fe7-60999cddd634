import {ReadStream} from 'fs';
import {StatusCodes} from 'http-status-codes';
import * as moment from 'moment';
import {printTraceWithError} from '../../cli/messaging';
import {Source} from '../../dataTypes';
import {SourceIdentifier} from '../../dataTypes/SourceSideOrganization';
import {CustomException} from '../../error/CustomException';
import {errorTypes} from '../../error/errorTypes';
import {LoginException, LoginExceptionType} from '../../error/exceptions';
import {FileExtension} from '../../utils/files/FileExtension';
import {generateFileName, saveStreamToFile} from '../../utils/files/fileUtils';
import {HTTPClient} from '../../utils/http';
import {log} from '../../utils/logger';
import {ProgressCallback} from '../ProgressCallback';
import {CheckSessionFunction, GetSourceSideOrganizationsFunction, LoginFunction, ScrapeFunction, Scraper, ScraperParams, SessionObjectSyntaxValidator} from '../Scraper';
import {prepareZipReports} from '../zip';

export interface PlayStationParams extends ScraperParams {
    clientId: string;
    clientSecret: string;
}

export interface PlayStationSession {
    accessToken: string;
    expirationDate: string;
    userId: string;
}

const BASE_API_URL = 'https://api.domo.com';

export const createPlayStationApiScraper = (source: Source, datasetName: string, columns?: string[]): Scraper<PlayStationParams, PlayStationSession> => {
    const [, observationType] = source.split('_');

    const login: LoginFunction<PlayStationParams, PlayStationSession> = async ({progress, params, getHttpClient}) => {
        try {
            progress('Acquiring access token...');
            const httpClient = getHttpClient();
            const authData = await acquireAuthData(httpClient, params);
            progress('Token acquired successfully');
            return authData;
        } catch (error) {
            log.info('PlayStation API responded with an error');
            printTraceWithError(error);
            if (error.response?.status === StatusCodes.UNAUTHORIZED) {
                throw new LoginException(LoginExceptionType.CREDENTIALS_INVALID);
            }
            throw new LoginException(LoginExceptionType.UNKNOWN, 'Unknown login issue encountered.', error);
        }
    };

    const scrape: ScrapeFunction<PlayStationParams, PlayStationSession> = async ({progress, params, session, getHttpClient}, startDate, endDate) => {
        const httpClient = getHttpClient();
        progress(`Downloading data for ${startDate.format('YYYY-MM-DD')} - ${endDate.format('YYYY-MM-DD')}`);
        const accessToken = await getAccessToken(httpClient, progress, params, session);
        progress('Getting list of supported Data Sets...');
        const datasetId = await getDataSetId(httpClient, accessToken);
        progress(`Data set acquired successfully, found proper dataset ID: ${datasetId}`);
        progress(`Downloading ${observationType} data...`);
        const filename = generateFileName(source, startDate, endDate, FileExtension.JSON);
        const stream = await getDataStream(httpClient, accessToken, datasetId, startDate, endDate);
        await saveStreamToFile(stream, filename);
        return prepareZipReports(startDate, endDate, [filename], source, progress);
    };

    const getAccessToken = async (httpClient: HTTPClient, progress: ProgressCallback, params: PlayStationParams, session?: PlayStationSession): Promise<string> => {
        if (session && moment(session.expirationDate).isAfter(moment())) {
            progress('Reusing session access token...');
            return session.accessToken;
        }
        progress('Acquiring access token...');
        const {accessToken} = await acquireAuthData(httpClient, params);
        progress('Token acquired successfully');
        return accessToken;
    };

    const acquireAuthData = async (httpClient: HTTPClient, params: PlayStationParams): Promise<PlayStationSession> => {
        const {
            data: {access_token, expires_in, userId}
        } = await httpClient.get<any>({
            url: `${BASE_API_URL}/oauth/token?grant_type=client_credentials&scope=data`,
            auth: {
                username: params.clientId,
                password: params.clientSecret
            }
        });
        return {
            accessToken: access_token,
            expirationDate: moment().add(expires_in, 'seconds').utc().format(),
            userId: userId.toString()
        };
    };

    const getDataSetId = async (httpClient: HTTPClient, accessToken: string): Promise<string> => {
        const {data} = await httpClient.get<any>({
            url: `${BASE_API_URL}/v1/datasets`,
            headers: {
                Authorization: `Bearer ${accessToken}`
            }
        });
        const dataset = data.find((dataset) => dataset.name === datasetName);
        if (!dataset) {
            const availableDataSets = data.map((dataset) => dataset.name).join(', ');
            throw new CustomException({
                message: `Unable to find dataset id '${datasetName}'. Available data sets: ${availableDataSets}`,
                errorType: errorTypes.CONFIGURATION_ISSUE
            });
        }
        return dataset.id;
    };

    const getDataStream = async (
        httpClient: HTTPClient,
        accessToken: string,
        datasetId: string,
        startDate: moment.Moment,
        endDate: moment.Moment
    ): Promise<ReadStream> => {
        const query = JSON.stringify({
            sql: `
                SELECT ${columns ? columns.map((column) => `\`${column}\``).join(',') : '*'}
                FROM table 
                WHERE Date >= '${startDate.format('YYYY-MM-DD')}' AND Date <= '${endDate.format('YYYY-MM-DD')}'`
        });

        const {data} = await httpClient.post<any>({
            url: `${BASE_API_URL}/v1/datasets/query/execute/${datasetId}`,
            stream: true,
            data: query,
            headers: {
                Authorization: `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            }
        });

        return data;
    };

    const getPartnerData = async (httpClient: HTTPClient, accessToken: string, datasetId: string): Promise<Array<string[]>> => {
        const query = JSON.stringify({
            sql: `
                SELECT DISTINCT
                    \`Global Partner ID\`, 
                    \`Partner Name\`
                FROM table `
        });

        const {data} = await httpClient.post<any>({
            url: `${BASE_API_URL}/v1/datasets/query/execute/${datasetId}`,
            data: query,
            headers: {
                Authorization: `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            }
        });
        return data.rows;
    };

    const getSourceSideOrganizations: GetSourceSideOrganizationsFunction<PlayStationParams, PlayStationSession> = async ({progress, params, session, getHttpClient}) => {
        const httpClient = getHttpClient();
        const accessToken = await getAccessToken(httpClient, progress, params, session);
        progress('Getting list of supported Data Sets...');
        const datasetId = await getDataSetId(httpClient, accessToken);
        progress('Parsing acquired partner data...');
        const partnerRawData = await getPartnerData(httpClient, accessToken, datasetId);

        if (!partnerRawData.length) {
            throw new CustomException({
                message: `The response contained no ${observationType} organization data`,
                suggestedAction: `The account you are using does not seem to have any data. We were unable to determine your organization list. Please try using an account with access to ${observationType} data.`,
                errorType: errorTypes.CONFIGURATION_ISSUE
            });
        }

        return partnerRawData.map((dataArray) => {
            if (dataArray.length !== 2) {
                throw new CustomException({
                    message: `There was an issue with the obtained partner raw data. Problematic data: ${JSON.stringify(dataArray)}. Whole data set ${JSON.stringify(
                        partnerRawData
                    )}`,
                    suggestedAction:
                        'The account you are using does not seem to contain proper data. We were unable to determine your organization list. Please try using a different account',
                    errorType: errorTypes.CONFIGURATION_ISSUE
                });
            }
            return {name: dataArray[1], id: dataArray[0] + ''};
        });
    };

    const sessionObjectSyntaxValidator: SessionObjectSyntaxValidator<PlayStationSession> = (session: any): session is PlayStationSession => {
        return !!session?.accessToken && !!session.expirationDate;
    };

    const checkSession: CheckSessionFunction<PlayStationParams, PlayStationSession> = async ({session, progress, getHttpClient}): Promise<SourceIdentifier> => {
        const httpClient = getHttpClient();
        if (moment(session.expirationDate).isBefore(moment())) {
            throw new LoginException(LoginExceptionType.SESSION_EXPIRED);
        }
        try {
            progress('Checking if the user has access to the data...');
            await getDataSetId(httpClient, session.accessToken);
        } catch {
            return {id: session.userId, hasScrapeBlockingIssues: true};
        }
        return {id: session.userId};
    };

    return {
        getSourceSideOrganizations,
        login,
        scrape,
        checkSession,
        sessionObjectSyntaxValidator
    };
};
