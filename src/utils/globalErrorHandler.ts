import * as Sentry from '@sentry/node';
import {exitCode} from '../cli/exitCodes';
import {printError} from '../cli/messaging';
import {CustomException} from '../error/CustomException';

export default async function globalErrorHandler(error: Error): Promise<void> {
    const isNotACustomException = !(error instanceof CustomException);
    if (isNotACustomException || error.errorType == 'UNEXPECTED_ERROR') {
        Sentry.captureException(error);
    }
    printError(error);

    await Sentry.close();
    process.exit(exitCode.GENERAL_ERROR);
}
