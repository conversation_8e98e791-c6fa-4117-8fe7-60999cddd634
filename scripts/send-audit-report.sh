#!/bin/bash

# after https://gitlab.com/bluebrick/indiebi/gitlab-shared/-/blob/master/send-audit-report.sh

if [[ $# -eq 0 ]] ; then
    echo 'Please provide mattermost webhook url as an argument'
    exit 1
fi

FILE_WITH_NOTIFICATION_CONTENT="message_to_webhook.json"

# read the file and replace newlines with literal \n
REPORT=$(sed -E ':a;N;$!ba;s/\r{0,1}\n/\\n/g' report.md)
MESSAGE='{"text": "## NPM audit report for '${CI_PROJECT_NAME}'\n\n'${REPORT}'"}'

echo "$MESSAGE" > ${FILE_WITH_NOTIFICATION_CONTENT}

curl -g -k -i -X POST -H "Content-Type:application/json" --url "$1" -d @${FILE_WITH_NOTIFICATION_CONTENT}
