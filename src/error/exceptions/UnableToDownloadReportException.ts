import {CustomException} from '../CustomException';
import {errorTypes} from '../errorTypes';

export class UnableToDownloadReportException extends CustomException {
    constructor(error?: Error, filename?: string) {
        super({
            message: `Couldn't download report. ${filename ? `Expected file name: ${filename}` : ''}`,
            error,
            errorType: errorTypes.TEMPORARY_PORTAL_ISSUE
        });
    }
}
