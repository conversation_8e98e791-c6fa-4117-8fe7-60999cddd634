import * as chai from 'chai';
import * as chaiAsPromised from 'chai-as-promised';
import * as cheerio from 'cheerio';
import * as mockfs from 'mock-fs';
import * as moment from 'moment';
import * as td from 'testdouble';
import {BrowserPage} from '../../../../../src/browserV2';
import {Source} from '../../../../../src/dataTypes/Source';
import {additionalSourceSideOrganization, defaultSourceSideOrganization} from '../../../../../src/dataTypes/SourceSideOrganization';
import {MissingPermissionsException} from '../../../../../src/error/common';
import {ReportManifest} from '../../../../../src/scrapersV2/ReportManifest';
import {ScraperContext} from '../../../../../src/scrapersV2/ScraperContext';
import * as steamLoginCommon from '../../../../../src/scrapersV2/steam/common/login';
import {SteamParams} from '../../../../../src/scrapersV2/steam/common/login';
import {SteamSession} from '../../../../../src/scrapersV2/steam/common/SteamSession';
import * as steamGamesCommon from '../../../../../src/scrapersV2/steam/games/getSourceSideOrganizations';
import {steamDiscountsScraper} from '../../../../../src/scrapersV2/steam/games/scrapers/steamDiscounts';
import * as convertToCSVsfiles from '../../../../../src/scrapersV2/steam/games/steamDiscountsJson2CsvConverters';
import * as steamGamesrequest from '../../../../../src/scrapersV2/steam/games/steamGamesRequest';
import * as createZipBasedOnmanifest from '../../../../../src/scrapersV2/zip';
import * as buildFilepath from '../../../../../src/utils/files/fileUtils';
import {JsonFile} from '../../../../../src/utils/files/JsonFile';
import {HTTPClient} from '../../../../../src/utils/http';

chai.use(chaiAsPromised);
const {expect} = chai;

const pageWithErrorMessage = '<html><div class="errorContainer"><div id="message">You do not have any packages available for pricing.</div></div></html>';
const showAllWeeklongDiscounts = "//span[contains(text(), 'Show all weeklong discounts')]";
const downloadCSVButtonXPath = "//button[contains(text(), 'Download all') and not(contains(@class,'Disabled'))]";
const startDate = moment('2021-01-01');
const endDate = moment('2021-01-01');

const bodyWithExportCSVButton = `
            <html>
                <body>
                    <!-- Other mock content -->
                    <button>Download all</button>
                    <!-- Other mock content -->
                </body>
            </html>
        `;

const discountManagementDataMock = `
            <html>
                <body>
                    <div id="application_config" data-base-prices='{"1111": 1000}' data-max-discount-percentages='{"1111": 50}' data-package-ids='["1111"]' data-price-increase-times='{"1111": "2021-01-01T00:00:00Z"}' data-publisherid='-1' data-userinfo='{"user": "mockUser"}'></div>
                    <div class="errorContainer">
                        <div id="message"></div>
                    </div>
                </body>
            </html>
        `;

const discountHistoryMock = `
            <html>
                <body>
                    <table>
                        <tr data-discount-data='{"discount": 50, "startDate": "2021-01-01T00:00:00Z", "endDate": "2021-01-31T23:59:59Z"}' class="discount-row">
                            <td>50%</td>
                            <td>2021-01-01</td>
                            <td>2021-01-31</td>
                        </tr>
                    </table>
                </body>
            </html>
        `;

const discountEventsMock = [
    {
        event_1: {
            id: 'event_1',
            name: 'Mock Discount Event',
            startDate: '2021-01-01T00:00:00Z',
            endDate: '2021-01-31T23:59:59Z'
        }
    }
];

const packageDiscountsMock = [
    {
        package_1: {
            id: '1111',
            discount: 50,
            startDate: '2021-01-01T00:00:00Z',
            endDate: '2021-01-31T23:59:59Z'
        }
    }
];

describe('Steam Discounts Permissions Test', () => {
    afterEach(td.reset);

    it('Should throw "MissingPermissions" error when the user does not have permissions to his single organization', async () => {
        const httpClient = td.object<HTTPClient>();
        const BrowserPage = td.object<BrowserPage>();
        const context = {
            getHttpClient: () => httpClient,
            progress: (_message, _progress) => {},
            getPage: async () => BrowserPage
        } as ScraperContext<SteamParams, SteamSession>;

        const steamGamesCheerioRequestMock = td.replace(steamGamesrequest, 'steamGamesCheerioRequest');

        td.replace(steamLoginCommon, 'refreshSessionCookies', async () => ({}));
        td.replace(steamGamesCommon, 'getSourceSideOrganizations', async () => [defaultSourceSideOrganization]);

        td.when(steamGamesCheerioRequestMock(`/dashboard?requestedPrimaryPublisher=${defaultSourceSideOrganization.id}`, td.matchers.anything())).thenResolve({} as any);
        td.when(steamGamesCheerioRequestMock(`/promotion/discounts/dashboard/${defaultSourceSideOrganization.id}`, td.matchers.anything())).thenResolve(
            cheerio.load(pageWithErrorMessage)
        );

        await expect(steamDiscountsScraper.scrape(context, startDate, endDate)).to.be.rejectedWith(MissingPermissionsException);
    });

    it('Should throw "MissingPermissions" error when the user has empty list of organizations', async () => {
        const httpClient = td.object<HTTPClient>();
        const BrowserPage = td.object<BrowserPage>();
        const context = {
            getHttpClient: () => httpClient,
            progress: (_message, _progress) => {},
            getPage: async () => BrowserPage
        } as ScraperContext<SteamParams, SteamSession>;

        td.replace(steamLoginCommon, 'refreshSessionCookies', async () => ({}));
        td.replace(steamGamesCommon, 'getSourceSideOrganizations', async () => []);

        await expect(steamDiscountsScraper.scrape(context, startDate, endDate)).to.be.rejectedWith(MissingPermissionsException);
    });

    it('Should scrape succesfully when the user has required permissions at least to one of orgainzations', async () => {
        const httpClient = td.object<HTTPClient>();
        const BrowserPage = td.object<BrowserPage>();
        const context = {
            getHttpClient: () => httpClient,
            progress: (_message, _progress) => {},
            getPage: async () => BrowserPage
        } as ScraperContext<SteamParams, SteamSession>;

        const steamGamesCheerioRequestMock = td.replace(steamGamesrequest, 'steamGamesCheerioRequest');
        const steamGamesRequestMock = td.replace(steamGamesrequest, 'steamGamesRequest');
        const convertToCSVsFilesMock = td.replace(convertToCSVsfiles, 'convertToCSVsFiles');
        const createZipBasedOnManifestMock = td.replace(createZipBasedOnmanifest, 'createZipBasedOnManifest');

        // Additional organization should fail

        td.replace(steamLoginCommon, 'refreshSessionCookies', async () => ({}));
        td.replace(steamGamesCommon, 'getSourceSideOrganizations', async () => [additionalSourceSideOrganization, defaultSourceSideOrganization]);

        td.when(steamGamesCheerioRequestMock(`/dashboard?requestedPrimaryPublisher=${additionalSourceSideOrganization.id}`, td.matchers.anything())).thenResolve(
            {} as any
        );
        td.when(steamGamesCheerioRequestMock(`/promotion/discounts/dashboard/${additionalSourceSideOrganization.id}`, td.matchers.anything())).thenResolve(
            cheerio.load(pageWithErrorMessage)
        );

        // Default organization should pass

        const manifest = td.object<ReportManifest>();

        td.when(steamGamesCheerioRequestMock(`/dashboard?requestedPrimaryPublisher=${defaultSourceSideOrganization.id}`, td.matchers.anything())).thenResolve({} as any);
        td.when(steamGamesCheerioRequestMock(`/promotion/discounts/dashboard/${defaultSourceSideOrganization.id}`, td.matchers.anything())).thenResolve(
            cheerio.load(showAllWeeklongDiscounts)
        );
        td.when(steamGamesCheerioRequestMock(`/promotion/discounts/dashboard/${defaultSourceSideOrganization.id}`, td.matchers.anything())).thenResolve(
            cheerio.load(bodyWithExportCSVButton)
        );
        td.replace(BrowserPage, 'waitForAnySelector', async () => [downloadCSVButtonXPath, BrowserPage]);
        td.replace(BrowserPage, 'downloadByXPathSelector', async () => [downloadCSVButtonXPath, '-1']);

        td.when(steamGamesCheerioRequestMock(`/promotion/discounts/dashboard/${defaultSourceSideOrganization.id}`, td.matchers.anything())).thenResolve(
            cheerio.load(discountManagementDataMock)
        );

        td.when(steamGamesRequestMock(`/promotion/discounts/ajaxgetalldiscountevents/${defaultSourceSideOrganization.id}`, td.matchers.anything())).thenResolve({
            data: {
                success: 1,
                events: discountEventsMock
            }
        });

        td.when(
            steamGamesRequestMock(
                `/promotion/discounts/ajaxgetpackagediscounts/${defaultSourceSideOrganization.id}?packageids=${packageDiscountsMock[0].package_1.id}`,
                td.matchers.anything()
            )
        ).thenResolve({
            data: {
                success: 1,
                discounts: packageDiscountsMock
            }
        });

        td.when(steamGamesCheerioRequestMock(`/packages/discounts/${packageDiscountsMock[0].package_1.id}`, td.matchers.anything())).thenResolve(
            cheerio.load(discountHistoryMock)
        );

        mockfs({
            '/mock/path': {}
        });
        const discountManagementDataFileName = 'discountManagementData.json';
        const discountHistoryFileName = 'discountHistory.json';

        const JsonFileMock = td.constructor(JsonFile);
        const buildFilePathMock = td.replace(buildFilepath, 'buildFilePath');

        td.when(buildFilePathMock(discountManagementDataFileName)).thenReturn('/mock/path/discountManagementData.json');
        td.when(buildFilePathMock(discountHistoryFileName)).thenReturn('/mock/path/discountHistory.json');

        td.when(new JsonFileMock('/mock/path/discountManagementData.json').save(td.matchers.anything())).thenResolve();
        td.when(new JsonFileMock('/mock/path/discountHistory.json').save(td.matchers.anything())).thenResolve();
        td.when(convertToCSVsFilesMock(td.matchers.anything(), td.matchers.anything())).thenResolve(['discountManagementData.json', 'discountHistory.json']);
        td.when(createZipBasedOnManifestMock(manifest, Source.STEAM_DISCOUNTS, td.matchers.anything()));

        await expect(steamDiscountsScraper.scrape(context, startDate, endDate)).to.be.fulfilled;
    });

    it('Should throw "MissingPermissions" error when the user does not have permissions to all organizations', async () => {
        const httpClient = td.object<HTTPClient>();
        const BrowserPage = td.object<BrowserPage>();
        const context = {
            getHttpClient: () => httpClient,
            progress: (_message, _progress) => {},
            getPage: async () => BrowserPage
        } as ScraperContext<SteamParams, SteamSession>;

        const steamGamesCheerioRequestMock = td.replace(steamGamesrequest, 'steamGamesCheerioRequest');

        td.replace(steamLoginCommon, 'refreshSessionCookies', async () => ({}));
        td.replace(steamGamesCommon, 'getSourceSideOrganizations', async () => [additionalSourceSideOrganization, defaultSourceSideOrganization]);

        // First organization should fail
        td.when(steamGamesCheerioRequestMock(`/dashboard?requestedPrimaryPublisher=${additionalSourceSideOrganization.id}`, td.matchers.anything())).thenResolve(
            {} as any
        );
        td.when(steamGamesCheerioRequestMock(`/promotion/discounts/dashboard/${additionalSourceSideOrganization.id}`, td.matchers.anything())).thenResolve(
            cheerio.load(pageWithErrorMessage)
        );

        // Second organization should fail too
        td.when(steamGamesCheerioRequestMock(`/dashboard?requestedPrimaryPublisher=${defaultSourceSideOrganization.id}`, td.matchers.anything())).thenResolve({} as any);
        td.when(steamGamesCheerioRequestMock(`/promotion/discounts/dashboard/${defaultSourceSideOrganization.id}`, td.matchers.anything())).thenResolve(
            cheerio.load(pageWithErrorMessage)
        );

        await expect(steamDiscountsScraper.scrape(context, startDate, endDate)).to.be.rejectedWith(MissingPermissionsException);
    });
});
