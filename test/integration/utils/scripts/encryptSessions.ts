import {readFileSync, readdirSync, writeFileSync} from 'fs';
import path = require('path');
import {FileCrypter} from '../FileCrypter';
import {DEFAULT_ENCRYPTED_SESSIONS_PATH, DEFAULT_TEST_SESSIONS_PATH} from '../SessionsManager';

/** Helper file meant for local usage only when updating stored encrypted sessions.
 * It uses the same encryption as VCR tests, so the resulting file may be used in (or moved to) VCR
 * when we have a mechanism for session files in VCR.
 */

function encryptSessions() {
    const fileList = readdirSync(DEFAULT_TEST_SESSIONS_PATH);
    const tmp = {};
    fileList.forEach((fileName) => {
        const key = fileName.split('.')[0];
        tmp[key] = JSON.parse(readFileSync(path.join(DEFAULT_TEST_SESSIONS_PATH, fileName)).toString());
    });
    const stringified = JSON.stringify(tmp);
    const encrypted = new FileCrypter().crypt(stringified);
    writeFileSync(DEFAULT_ENCRYPTED_SESSIONS_PATH, encrypted);
}

encryptSessions();
