import {expect} from 'chai';
import * as moment from 'moment';
import {Source} from '../../../src/dataTypes';
import {FileExtension} from '../../../src/utils/files/FileExtension';
import {steamSuperhotOrgs} from '../expectedData';
import {createGetSourceSideOrgTest} from '../getSourceSideOrganizationTestGenerator';
import {getLoginArgs, getScrapeArgs} from '../utils/ArgsProvider';
import steamGenerateTestZipEntries from '../utils/steam/SteamGenerateTestZipEntries';
import {listZipContentNames, removeIfFilesExist, runCliMainWithConvertedArgs} from '../utils/Utils';

describe('Steam Impressions runner should', () => {
    const defaultStartDateString = '2020-01-01';
    const defaultEndDateString = defaultStartDateString;
    const recentStartDate = moment.utc().subtract(4, 'days');
    const recentStartDateAsFormattedString = recentStartDate.format('YYYY-MM-DD');
    const recentEndDate = moment.utc().subtract(2, 'days');
    const recentEndDateAsFormattedString = recentEndDate.format('YYYY-MM-DD');
    const loginArgs = getLoginArgs(Source.STEAM_IMPRESSIONS, 'steam_sales_superhot_fullpermissions', 'steam_fullpermissions.json');
    const scrapeArgs = getScrapeArgs(loginArgs, defaultStartDateString, defaultEndDateString, FileExtension.ZIP);
    const recentScrapeArgs = getScrapeArgs(loginArgs, recentStartDateAsFormattedString, recentEndDateAsFormattedString, FileExtension.ZIP);
    const numberOfApps = 7;

    before(async () => {
        await removeIfFilesExist(scrapeArgs.testedFilePath!);
        await runCliMainWithConvertedArgs(loginArgs);
    });

    createGetSourceSideOrgTest(loginArgs, steamSuperhotOrgs, true);

    it('download zipped data and verify number of files', async function () {
        await runCliMainWithConvertedArgs(scrapeArgs);

        expect(listZipContentNames(scrapeArgs.testedFilePath!)).to.have.members(
            steamGenerateTestZipEntries(Source.STEAM_IMPRESSIONS, defaultStartDateString, defaultEndDateString, numberOfApps)
        );
    });

    const currentDayOffset = 1;
    const manifestFileOffset = 1;
    it('download recent zipped data and verify number of files', async function () {
        await runCliMainWithConvertedArgs(recentScrapeArgs);
        const fileNames = listZipContentNames(recentScrapeArgs.testedFilePath!);
        expect(fileNames).to.have.length(numberOfApps * (recentEndDate.diff(recentStartDate, 'day') + currentDayOffset) + manifestFileOffset);
        fileNames.forEach((name) => {
            if (name.startsWith('additionalData')) {
                expect(name).to.eq(`additionalData_steam_impressions_${recentStartDateAsFormattedString}_${recentEndDateAsFormattedString}.json`);
            } else {
                expect(name).to.match(/^steam_impressions-\d{4}-\d{2}-\d{2}_\d{4}-\d{2}-\d{2}-\d+.csv$/);
            }
        });
    });
});
