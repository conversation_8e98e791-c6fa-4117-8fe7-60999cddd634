# Plan analizy walidacji credentiali w scrapers-js

## Ce<PERSON> jak obecnie działa walidacja credentiali w projekcie scrapers-js, por<PERSON><PERSON><PERSON> z oczekiwaniami scraperLib i zaproponować ulepszenia.

## Etapy analizy

### 1. <PERSON><PERSON><PERSON> ogólnej architektury walidacji credentiali

-   [x] Przeanalizować entry pointy (`src/scrape.ts`, `src/cli/arguments.ts`)
-   [x] P<PERSON><PERSON><PERSON><PERSON><PERSON>ć `ScraperProxy` i sposób przekazywania credentiali
-   [x] Zbadać interfejsy `ScraperParams` dla różnych scraperów
-   [x] Sprawd<PERSON>ć czy istnieje centralne miejsce walidacji credentiali
-   [x] Przeanalizować mechanizm `LoginException` i jego typy

### 2. Analiza każdego źródła danych (Source)

D<PERSON> każdego <PERSON>ródła z `Source.ts`:

#### 2.1 PLAYSTATION_SALES & PLAYSTATION_WISHLIST_ACTIONS

-   [ ] P<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> `PlayStationParams` interface
-   [ ] Sprawd<PERSON><PERSON> walidację `clientId` i `clientSecret`
-   [ ] Zbadać reakcję na brakujące credentiale

#### 2.2 STEAM\_\* (wszystkie źródła Steam)

-   [ ] Przeanalizować `SteamParams` interface
-   [ ] Sprawdzić walidację `user`, `password`, `totpSecret`
-   [ ] Zbadać logikę w `getLoginFunction`
-   [ ] Sprawdzić różnice między różnymi źródłami Steam

#### 2.3 EPIC_SALES

-   [ ] Przeanalizować `EpicParams` interface
-   [ ] Sprawdzić walidację `user`, `password`, `totpSecret`, `loginWith`
-   [ ] Zbadać logikę logowania przez Steam vs Epic

#### 2.4 NINTENDO_SALES

-   [ ] Przeanalizować parametry Nintendo scraper
-   [ ] Sprawdzić walidację credentiali
-   [ ] Zbadać obsługę `totpSecret`

#### 2.5 META_RIFT_SALES & META_QUEST_SALES

-   [ ] Przeanalizować parametry Meta scraper
-   [ ] Sprawdzić walidację credentiali
-   [ ] Zbadać obsługę `totpSecret`

#### 2.6 GOG_SALES & HUMBLE_SALES

-   [ ] Przeanalizować parametry dla tych scraperów
-   [ ] Sprawdzić wymagane credentiale
-   [ ] Zbadać reakcję na brakujące dane

### 3. Analiza mechanizmów błędów i wyjątków

-   [ ] Przeanalizować `LoginException` i jego typy
-   [ ] Sprawdzić `LoginExceptionType.CREDENTIALS_INVALID`
-   [ ] Zbadać inne typy błędów związanych z credentialami
-   [ ] Sprawdzić jak błędy są propagowane do CLI

### 4. Analiza CLI i skryptów

-   [ ] Przeanalizować `scripts/run_login.sh` i sposób przekazywania credentiali
-   [ ] Sprawdzić walidację w CLI (`src/cli/cli.ts`)
-   [ ] Zbadać jak credentiale są ładowane z `.env`

### 5. Porównanie z scraperLib

-   [ ] Porównać `SourceCredentials` interface z scraperLib
-   [ ] Sprawdzić zgodność wymaganych pól
-   [ ] Zidentyfikować rozbieżności
-   [ ] Sprawdzić funkcje `sourceAccountToCredentials`

### 6. Analiza punktów walidacji

-   [ ] Zidentyfikować wszystkie miejsca gdzie sprawdzane są credentiale
-   [ ] Sprawdzić czy walidacja odbywa się przed rozpoczęciem scrapowania
-   [ ] Zbadać czy istnieje early validation
-   [ ] Przeanalizować moment rzucania wyjątków

## Rezultat końcowy

### Tabela porównawcza

Utworzyć tabelę zawierającą:

-   Source name
-   Wymagane credentiale (według scrapers-js)
-   Wymagane credentiale (według scraperLib)
-   Opcjonalne pola
-   Typ walidacji (early/late)
-   Typ błędu przy braku credentiali
-   Zgodność między projektami

### Rekomendacje

-   [ ] Zaproponować ulepszenia walidacji
-   [ ] Wskazać miejsca do refaktoringu
-   [ ] Zasugerować centralizację walidacji
-   [ ] Określić czy scraperLib powinien walidować credentiale

## Metodologia

1. Każdy punkt planu = osobny commit
2. Dokumentowanie wyników w komentarzach do commitów
3. Tworzenie notatek w trakcie analizy
4. Finalna tabela i rekomendacje w ostatnim commicie
