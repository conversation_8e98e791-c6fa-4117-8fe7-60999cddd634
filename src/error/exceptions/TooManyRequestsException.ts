import {LogLevel} from '../../utils/logger';
import {CustomException} from '../CustomException';
import {errorTypes} from '../errorTypes';

export class TooManyRequestsException extends CustomException {
    constructor() {
        super({
            message: 'The target portal failed after the app sent too many scraping requests',
            canBeFixedByRetry: false,
            telemetryLogLevel: LogLevel.WARN,
            errorType: errorTypes.TEMPORARY_PORTAL_ISSUE
        });
    }
}
