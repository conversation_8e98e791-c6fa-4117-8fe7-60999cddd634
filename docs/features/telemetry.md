## Dictionary

AI - Azure Application Insights

## Introduction

For tracking telemetry events we are using Azure Application Insights.
By default, telemetry is turned off. To activate it you need to set the `app.appinsights.connectionString` with an appropriate connection string.

## Help

If for some reason you activate your telemetry and do not see any logs in AI try debugging the `aiClient` and confirm that its `aiClient.config._instrumentationKey` is set to the proper value.
It is possible to override your key with the local `config.json` file (which is an external file not in this repo) and not spot it right away.
