import {printTraceWithError} from '../cli/messaging';
import {CustomException} from '../error/CustomException';
import {log} from './logger';
import {sleep} from './sleep';

interface RetryActionOptions<T> {
    /**
     * The async function to call repeatedly.
     */
    target: (attemptCounter: number, maxAttempts: number) => Promise<T>;

    /**
     * The maximum number of attempts to make before giving up.
     */
    maxAttempts: number;

    /**
     * The delay between attempts in milliseconds.
     */
    delay?: number;

    /**
     * Function that determines whether to retry the target based on the error that caused the action to fail.
     * If not provided, the action will be retried until the maximum number of attempts is reached.
     * Can be used to break the retry loop early if the error is not retryable.
     */
    retryCondition?(error: Error | CustomException): boolean;

    /**
     * A function that logs messages. By default, the messages are logged as debug messages.
     */
    logger?: (message: string) => void;

    /**
     * Optional label to identify the action being executed.
     */
    label?: string;
}

/**
 *  Call an async function repeatedly until it either resolves or maximum number of attempts is reached.
 *  Each exception that causes a retry is logged externally as an information message instead of an error.
 */
export async function retryAction<T>(options: RetryActionOptions<T>): Promise<T> {
    const logger = options.logger || log.debug;
    let attemptCounter = 0;
    // eslint-disable-next-line no-constant-condition
    while (true) {
        try {
            attemptCounter++;
            logger(`Starting ${options.label ?? options.target.name}, attempt: ${attemptCounter}/${options.maxAttempts}`);
            return await options.target(attemptCounter, options.maxAttempts);
        } catch (error) {
            logger('An error occurred while executing an action');
            logger(error.userFriendlyMessage || error.message);
            printTraceWithError(error);
            logger('Checking if we should retry.');
            if (attemptCounter >= options.maxAttempts || (options.retryCondition && !options.retryCondition(error))) {
                logger('Maximum number of attempts reached or the error is not retryable. Stopping the retry loop.');
                throw error;
            }
            if (options.delay) {
                logger(`Waiting ${options.delay}ms before retrying.`);
                await sleep(options.delay);
            }
            logger('Retrying...');
        }
    }
}
