import {Source} from '../../../dataTypes';
import {createPlayStationApiScraper} from '../playStationAPI';

const datasetName = 'PROD| Daily Wishlist Items by Product and Concept';
const columns = [
    `Date`,
    `Partner Name`,
    `Concept ID`,
    `Concept`,
    `Title ID`,
    `Product ID`,
    `Product Name`,
    `Product Primary Class`,
    `Product Secondary Class`,
    `Product Tertiary Class`,
    `Title Platform`,
    `SIE Region`,
    `Country Code`,
    `Country/Region`,
    `Wishlist Platform`,
    `Additions`,
    `Deletions`,
    `Deletions by Purchase`,
    `Deletions by User`,
    `Deletions Other`
];

export const playStationWishlistActionsScraper = createPlayStationApiScraper(Source.PLAYSTATION_WISHLIST_ACTIONS, datasetName, columns);
