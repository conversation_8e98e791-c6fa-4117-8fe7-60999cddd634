import {Moment} from 'moment';

export interface IReportManifest<T = unknown> {
    dateFrom: Moment;
    dateTo: Moment;
    version?: string;
    metadata?: {
        // INFO this field may contain additional metadata, like parent-child mapping
        [property: string]: T;
    };
}

export class ReportManifest<T = unknown> {
    metadata: {
        [fileName: string]: T;
    } = {};
    constructor(public dateFrom: Moment, public dateTo: Moment, public version?: string) {}

    public addFile(fileName: string, fileMetaData: T) {
        this.metadata[fileName] = fileMetaData;
    }

    public getFileNames(): string[] {
        return Object.keys(this.metadata);
    }

    public getManifest(): IReportManifest {
        return {dateFrom: this.dateFrom, dateTo: this.dateTo, metadata: this.metadata, version: this.version};
    }
}
