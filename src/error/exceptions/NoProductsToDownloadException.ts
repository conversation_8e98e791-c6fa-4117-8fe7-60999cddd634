import {LogLevel} from '../../utils/logger';
import {CustomException} from '../CustomException';
import {errorTypes} from '../errorTypes';

export class NoProductsToDownloadException extends CustomException {
    constructor(message?: string) {
        super({
            message: message || '<PERSON><PERSON><PERSON> was unable to locate any downloadable products in all accounts/organizations.',
            suggestedAction: 'Please setup your scraper using an account with access to downloadable products.',
            telemetryLogLevel: LogLevel.WARN,
            errorType: errorTypes.CONFIGURATION_ISSUE
        });
    }
}
