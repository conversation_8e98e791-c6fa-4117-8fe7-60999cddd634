import {expect} from 'chai';
import * as td from 'testdouble';
import {<PERSON><PERSON><PERSON>} from '../../../../src/browser/Browser';
import {Source} from '../../../../src/dataTypes';
import {TooManyIterationsException} from '../../../../src/error/exceptions/TooManyIterationsException';
import {GraphQLPersistedQuery} from '../../../../src/scrapersV1/httpDownloaders/meta/MetaConstants';
import {MetaReportDownloader} from '../../../../src/scrapersV1/httpDownloaders/meta/MetaReportDownloader';
import {gameId, gameName, graphqlRequestMatcher, validLogin, validPassword} from './testData';

const metaApp = {
    id: '123123',
    is_test: false,
    name: 'sampleRiftGame',
    release_date: 1710516997,
    source: 'meta_rift_sales'
};

const cursor = 'some-random-very-long-string';

describe('getAvailableApps', () => {
    it('should return available apps without paging', async () => {
        const fakeBrowser = td.object<Browser>();
        td.when(fakeBrowser.cookies()).thenResolve([{value: 'asda', name: 'oc_ac_at'} as any]);
        // get available apps
        td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_AVAILABLE_APPS))).thenResolve({
            data: {
                organization: {
                    applications: {
                        edges: [{node: {id: gameId, display_name: gameName, is_test: false, release_date: 1710516997}}],
                        page_info: {
                            end_cursor: cursor,
                            has_next_page: false
                        }
                    }
                }
            }
        });

        const metaReportDownloader = new MetaReportDownloader({
            login: validLogin,
            password: validPassword,
            browser: fakeBrowser,
            source: Source.META_RIFT_SALES
        });

        const availableApps = await metaReportDownloader['getAvailableApps']('123', Source.META_RIFT_SALES);

        expect(availableApps).to.be.deep.equal([metaApp]);
    });

    it('should return available apps with paging', async () => {
        const fakeBrowser = td.object<Browser>();

        td.when(fakeBrowser.cookies()).thenResolve([{value: 'asda', name: 'oc_ac_at'} as any]);
        // get available apps
        td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_AVAILABLE_APPS))).thenResolve({
            data: {
                organization: {
                    applications: {
                        edges: [{node: {id: gameId, display_name: gameName, is_test: false, release_date: 1710516997}}],
                        page_info: {
                            end_cursor: cursor,
                            has_next_page: true
                        }
                    }
                }
            }
        });
        td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_AVAILABLE_APPS_NEXT_PAGE))).thenResolve({
            data: {
                node: {
                    applications: {
                        edges: [{node: {id: gameId, display_name: gameName, is_test: false, release_date: 1710516997}}],
                        page_info: {
                            end_cursor: cursor,
                            has_next_page: false
                        }
                    }
                }
            }
        });

        const metaReportDownloader = new MetaReportDownloader({
            login: validLogin,
            password: validPassword,
            browser: fakeBrowser,
            source: Source.META_RIFT_SALES
        });

        const availableApps = await metaReportDownloader['getAvailableApps']('123', Source.META_RIFT_SALES);

        expect(availableApps).to.be.deep.equal([metaApp, metaApp]);

        // verify if second page was requested with variables from first page
        td.verify(fakeBrowser.post('https://graph.oculus.com/graphql', td.matchers.contains({variables: td.matchers.contains({after: cursor, first: 1})})));
    });

    it('should throw exception when reaching MAX_PAGE_SAFETY_LIMIT', async () => {
        const fakeBrowser = td.object<Browser>();

        td.when(fakeBrowser.cookies()).thenResolve([{value: 'asda', name: 'oc_ac_at'} as any]);
        // get available apps
        td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_AVAILABLE_APPS))).thenResolve({
            data: {
                organization: {
                    applications: {
                        edges: [{node: {id: gameId, display_name: gameName, is_test: false, release_date: 1710516997}}],
                        page_info: {
                            end_cursor: cursor,
                            has_next_page: true
                        }
                    }
                }
            }
        });
        td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_AVAILABLE_APPS_NEXT_PAGE))).thenResolve({
            data: {
                node: {
                    applications: {
                        edges: [{node: {id: gameId, display_name: gameName, is_test: false, release_date: 1710516997}}],
                        page_info: {
                            end_cursor: cursor,
                            has_next_page: true // this will cause infinite loop
                        }
                    }
                }
            }
        });

        const metaReportDownloader = new MetaReportDownloader({
            login: validLogin,
            password: validPassword,
            browser: fakeBrowser,
            source: Source.META_RIFT_SALES
        });

        try {
            await metaReportDownloader['getAvailableApps']('123', Source.META_RIFT_SALES);
            expect.fail('Exception was not thrown');
        } catch (e) {
            expect(e).to.be.instanceOf(TooManyIterationsException);
        }
    });
});
