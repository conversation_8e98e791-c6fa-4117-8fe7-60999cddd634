import * as chai from 'chai';
import * as chaiAsPromised from 'chai-as-promised';
import * as td from 'testdouble';
import {BrowserPage, BrowserSession, BrowserV2} from '../../../src/browserV2';
import * as configService from '../../../src/config/ConfigService';
import {ScraperContext} from '../../../src/scrapersV2/ScraperContext';

const expect = chai.expect;
chai.use(chaiAsPromised);

describe('ScraperContext', function () {
    const progress = () => {
        // empty progress function for silent tests
    };
    const params = {user: 'x', password: 'x'};
    const session: BrowserSession = {
        cookies: []
    };

    let context: ScraperContext;

    beforeEach(() => {
        context = new ScraperContext(progress, params, session);
        td.replace(configService, 'downloadDirPath', () => '.private');
    });

    afterEach(async function () {
        td.reset();
        await context.close();
    });

    it('correctly update session ', async () => {
        const newSession = {foo: 'bar'};
        expect(context.session).be.equal(session);
        context.session = newSession;
        expect(context.session).be.equal(newSession);
    });

    it('launch browser and return BrowserPage', async () => {
        const page = await context.getPage();
        expect(page).be.instanceOf(BrowserPage);
    }).timeout(20000);

    it('return always the same page', async () => {
        const page = await context.getPage();
        expect(page).be.instanceOf(BrowserPage);
        const newPage = await context.getPage();
        expect(page).to.be.deep.equal(newPage);
    });

    it('return new page after context close', async () => {
        const page = await context.getPage();
        await context.close();
        const newPage = await context.getPage();
        expect(page !== newPage).to.be.equal(true);
    });

    it('check if browser is closed correctly', async () => {
        const mockedBrowser = td.object<BrowserV2>();
        td.replace(BrowserV2, 'launch', () => mockedBrowser);
        const testContext = new ScraperContext(progress, params, session);
        // launch browser and get page
        await testContext.getPage();
        await testContext.close();
        td.verify(mockedBrowser.close());
    });

    it(`skip closing browser if context don't use browser`, async () => {
        const mockedBrowser = td.object<BrowserV2>();
        td.replace(BrowserV2, 'launch', () => mockedBrowser);
        const testContext = new ScraperContext(progress, params, session);
        await testContext.close();
        td.verify(mockedBrowser.close(), {times: 0});
    });
});
