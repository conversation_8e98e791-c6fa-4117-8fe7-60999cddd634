export interface SourceIdentifier {
    id: string;

    /**
     * In some `Source` cases we can determine that an organization is not scrapeable before running a scraper.
     * For example:
     * Unconfirmed organizations in Meta
     * Non publishing organization in Epic
     */
    hasScrapeBlockingIssues?: boolean;
}

export interface SourceSideOrganization extends SourceIdentifier {
    name: string;
}

export const defaultSourceSideOrganization: SourceSideOrganization = {name: 'Client organization', id: '-1'};

export const additionalSourceSideOrganization: SourceSideOrganization = {name: 'Additional Client organization', id: '-2'};
