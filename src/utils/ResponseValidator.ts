import {Stream} from 'stream';
import {AxiosResponse} from 'axios';
import {CustomException} from '../error/CustomException';
import {errorTypes} from '../error/errorTypes';
import {InvalidContentTypeException} from '../error/exceptions';
import {log} from '../utils/logger';
import {HTTPResponse} from './http';

const defaultAllowedContentTypes = [
    'application/octet-stream',
    'application/octet-stream;charset=utf-8',
    'application/octet-stream;charset=UTF-16',
    'text/csv;charset=utf-8',
    'unknown/unknown;charset=UTF-8',
    'text/csv',
    'text/html;charset=utf-8', //humble
    'text/html;charset=UTF-8' // steam complimentary packages
];

export function validateReportResponseHeaders(response: AxiosResponse<Stream> | HTTPResponse, allowedContentTypes: string[] = defaultAllowedContentTypes): void {
    const contentType = response.headers['content-type'] ? response.headers['content-type'].replace(/\s/g, '') : '';
    log.debug(`Content-Type: ${contentType}`);
    if (!allowedContentTypes.includes(contentType)) {
        log.debug(`Content-Type: ${contentType} not in allowed list: ${allowedContentTypes}`);
        throw new InvalidContentTypeException(contentType, response.config.url);
    }
}

export function validateResponseHasData(response: AxiosResponse<Stream> | HTTPResponse): void {
    if (response.data === '') {
        throw new CustomException({
            message: ' The downloaded file content is empty!',
            errorType: errorTypes.TEMPORARY_PORTAL_ISSUE
        });
    }
}
