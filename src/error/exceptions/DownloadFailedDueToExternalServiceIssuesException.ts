import {LogLevel} from '../../utils/logger';
import {CustomException} from '../CustomException';
import {errorTypes} from '../errorTypes';

export class DownloadFailedDueToExternalServiceIssuesException extends CustomException {
    constructor(info: {reportId: string; data: string | Record<string, string>; status: number}) {
        super({
            suggestedAction: 'Unable to download the report because the target platform responded with a server issue. Please wait a few hours and try again.',
            message: `Report ${info.reportId} download failed with status ${info.status} and response data: ${JSON.stringify(info.data)}`,
            telemetryLogLevel: LogLevel.WARN,
            errorType: errorTypes.TEMPORARY_PORTAL_ISSUE
        });
    }
}
