# Steam Impressions scraper

## Permissions and multiorganizations

It is possible for the account to have access to multiple organizations. Usually we iterate through the organization list and download products from each organization. In this case, if the user can see the game in their dashboard, it means it have access to impressions data, therefore we can download it without any additional checks. Also, it's not necessary to perform any action in order to change the organization as impressions data can be downloaded for any product the client have access to.  

## Data acquisition

Data is available at `https://partner.steamgames.com/apps/navtrafficstats/${appId}?attribution_filter=all&preset_date_range=custom&start_date=${dateString}&end_date=${dateString}&format=csv` in form of CSV file.

We only need to download it without any modifications. We are making a single request for a single day. Unfortunately, sometimes Steam returns an empty file even if there's data for such period (it's a Steam bug). To mitigate the risk of miss-reporting the data, we introduced a concept of the "checksum" which we add to the ZIP manifest for each product. Checksum is then checked by the converter in order to determine if the empty report is valid or not. 

For Steam Impressions scraper, the checksum is just a number of impressions per game accessible on a dashboard. It should be the same as the sum of impressions in the downloaded CSV file. To keep it consistent, we are making both request at the same time (CSV and dashboard request to parse for the checksum).
