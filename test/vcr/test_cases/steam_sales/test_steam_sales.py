import json

import pytest

from scrapers_vcr.vcr_proxy import VCRProxy
from scrapers_vcr.util.testing import no_live, no_record, use_credentials, use_recording
from scrapers_vcr.util.scraper_runner import ScraperRunConfig

from .superhot_package_ids import SUPERHOT_ALL_PACKAGE_IDS


@pytest.fixture
def steam_scraper(default_scraper):
    default_scraper.source = "steam_sales"
    return default_scraper


@use_recording("fullpermissions_report")
@use_credentials("steam_sales_superhot_fullpermissions")
def test_fullpermissions_report(steam_scraper: ScraperRunConfig):
    result = steam_scraper.login_and_scrape()
    result.expect_zip_with_additional_data()
    assert len(result.list_files_in_zip()) == 2
    result.expect_nonempty_report_in_zip("1")


@use_recording("multiorg_report")
@use_credentials("steam_sales_superhot_multiorg")
def test_multiorg_report(steam_scraper: ScraperRunConfig):

    result = steam_scraper.login_and_scrape()

    result.expect_zip_with_additional_data()
    result.expect_nonempty_report_in_zip("1")
    result.expect_nonempty_report_in_zip("2")
    result.expect_telemetry_event("Found multiple organizations")
    result.expect_telemetry_event("Starting download process for organization", count=2)
    result.expect_telemetry_event("Download process for organization finished successfully", count=2)


@use_recording("multiorg_report_runas")
@use_credentials("steam_sales_superhot_multiorg")
def test_multiorg_report_runas(steam_scraper: ScraperRunConfig):
    """
    Test if cookies in session (for example, left over from manual login) don't impact org selection functionality.
    """

    result = steam_scraper.login()
    result.expect_success()

    with open(result.output_session_file, "r+") as f:
        session = json.load(f)
        session["cookies"].append({"name": "steamworksRunas", "value": "123147", "domain": "partner.steampowered.com", "expires": 1, "maxAge": 0, "path": "/"})
        f.seek(0)
        json.dump(session, f)

    result = steam_scraper.scrape()

    assert result.get_report_file_size("1") != result.get_report_file_size("2")


@use_recording("lowpermissions_report")
@use_credentials("steam_sales_superhot_lowpermissions")
def test_lowpermissions_report(steam_scraper: ScraperRunConfig):
    result = steam_scraper.login_and_scrape()

    result.expect_zip_with_additional_data()
    assert len(result.list_files_in_zip()) == 59

    for pkg in [1, 3, 4, 12, 38]:
        result.expect_nonempty_report_in_zip(str(pkg))


@use_recording("ignored_products")
@use_credentials("steam_sales_superhot_fullpermissions")
def test_ignored_products(steam_scraper: ScraperRunConfig):
    assert steam_scraper.credentials
    steam_scraper.excluded_skus = SUPERHOT_ALL_PACKAGE_IDS[3:]

    result = steam_scraper.login_and_scrape()

    result.expect_zip_with_additional_data()
    assert len(result.list_files_in_zip()) == 4

    for pkg in [1, 2, 3]:
        result.expect_nonempty_report_in_zip(str(pkg))


@use_recording("ignore_all")
@use_credentials("steam_sales_superhot_fullpermissions")
def test_ignore_all(steam_scraper: ScraperRunConfig):
    assert steam_scraper.credentials
    steam_scraper.excluded_skus = SUPERHOT_ALL_PACKAGE_IDS

    result = steam_scraper.login_and_scrape()

    result.expect_failure(exit_code=1, data="All downloadable products were marked to be ignored!")


@use_recording("fullpermissions_report")
@use_credentials("steam_sales_superhot_fullpermissions")
@no_live
def test_corrupted_report_on_logout(steam_scraper: ScraperRunConfig, proxy: VCRProxy):
    """
    User can get logged out anytime, especially if another user starts another session using the same credentials.
    Make sure that the scraper fails instead of downloading a corrupted report.
    """

    proxy.when(url="https://partner.steampowered.com/report_csv.php").reply(status=302, headers={"location": "https://partner.steampowered.com/login/?goto=%2Fnav_games.php"})

    result = steam_scraper.login_and_scrape()

    result.expect_failure(exit_code=1, data="Session expired.")


@use_recording("mfa_required_error")
@use_credentials("steam_sales_superhot_fullpermissions")
def test_fails_without_machine_token(steam_scraper: ScraperRunConfig):
    assert steam_scraper.credentials
    steam_scraper.credentials.pop("steamLoginSecureCookie")

    result = steam_scraper.login()

    result.expect_failure(exit_code=1, data="Logging in requires 2-factor authentication", errorType="MISSING_2FA")


@use_recording("captcha_required_error")
@use_credentials("steam_sales_superhot_fullpermissions")
@no_live
def test_captcha_required_error(steam_scraper: ScraperRunConfig):
    result = steam_scraper.login()
    result.expect_failure(exit_code=1, data="Logging in requires solving captcha.")


@use_recording("ip_ban_error")
@use_credentials("steam_sales_superhot_fullpermissions")
@no_live
def test_ip_ban_error(steam_scraper: ScraperRunConfig):
    result = steam_scraper.login()
    result.expect_failure(exit_code=1, data="Your IP is temporarily blocked by partner.steampowered.com because of too many unsuccessful login attempts")


@use_recording("invalid_credentials_error")
@use_credentials("steam_sales_invalid")
def test_fails_with_invalid_credentials(steam_scraper: ScraperRunConfig):
    assert steam_scraper.credentials

    result = steam_scraper.login()

    result.expect_failure(exit_code=1, data="Invalid credentials.")


@use_recording("fullpermissions_report")
@use_credentials("steam_sales_superhot_fullpermissions")
@no_record
def test_check_session(steam_scraper: ScraperRunConfig):
    result = steam_scraper.login()
    result.expect_success()
    result = steam_scraper.check_session()

    result_data = result.data
    assert result_data
    assert result_data["id"] == "supersuperdata"


@use_recording("empty_session")
@use_credentials("steam_sales_empty")
def test_check_session_no_session(steam_scraper: ScraperRunConfig):
    result = steam_scraper.check_session()  # check-session without login beforehand should fail
    result.expect_failure(exit_code=1, data="Session expired.")


@use_recording("multiorg_report")
@use_credentials("steam_sales_superhot_multiorg")
@no_record
def test_get_organizations(steam_scraper: ScraperRunConfig):
    result = steam_scraper.login()
    result.expect_success()
    result = steam_scraper.get_source_side_organizations()

    result_data = result.data
    assert result_data
    assert result_data == [{"id": "3204", "name": "SUPERHOT Sp. z o.o."}, {"id": "123147", "name": "straka.studio s. r. o."}]


@use_recording("fullpermissions_report")
@use_credentials("steam_sales_superhot_fullpermissions")
@no_record
def test_can_login_from_session(steam_scraper: ScraperRunConfig):
    result = steam_scraper.login()
    result.expect_success()
    steam_scraper.credentials = {}
    # even without valid credentials, login should succeed if the sesssion file has valid session cookies
    result = steam_scraper.login()
    result.expect_success()


def test_can_get_manual_login_details(steam_scraper: ScraperRunConfig):
    result = steam_scraper.get_manual_login_details()
    result.expect_success()
    result_data = result.data
    assert result_data
    assert result_data["url"] == "https://partner.steampowered.com/login/"
    assert result_data["successSelector"] == {"value": "#header_menu", "type": "css"}
