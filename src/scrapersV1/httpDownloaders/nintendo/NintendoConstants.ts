export const signInButtonSelector = '#signin';
export const reportWebsiteUrl = 'https://sst.mng.nintendo.net/shoptools/switchLicenseeReports/titleReport';
export const productIdsApiRequestUrl = 'https://sst.mng.nintendo.net/shoptools/api/switchLicenseeProductCode';
export const authorizationRenewalUrl = 'https://sst.mng.nintendo.net/shoptools/oauth2/authorization/ndid';
export const loginInputSelector = 'input[name=loginid]';
export const passwordInputSelector = 'input[name=password]';
export const submitButton = 'button[type=submit]';
export const isEnglishLanguageSelector = 'a[href^="/ndid/oauth"][href$="lang=en"]';
export const isJapaneseLanguageSelector = 'a[href^="/ndid/oauth"][href$="lang=ja"]';
export const invalidCredentialsSelector = '.loginForm div.text_red';
export const periodSelector = '#period';
export const startDateSelector = '#begin';
export const endDateSelector = '#end';
export const selectAllRegions = '#checkAllRegion';
export const selectAllTypes = '#checkAllType';
export const selectAllPaidAndFreeSelector = '#paidItems > label:nth-child(1)';
export const displayUnitsSelector = '#searchUnitItems >label:nth-child(1)';
export const selectShowPrice = '#searchPriceItems > label:nth-child(1)';
export const searchIdInput = '#searchCodes';
export const downloadAllCSV = '#downloadCsv';
export const elementToClickToHideDateChoosing = 'div.page-header';
export const resetSearchConditionsButtonSelector = 'button#reset';

export const periodSelectorValue = 'DAILY';

export const selectDateFormat = 'YYYY/MM/DD';

export const downloadWaitTime = 30000;

export const enterCharCode = 13;

export const noLoginPermissionsSelector = '#login  #signin-field #signin';

export const loggedInXpathSelector = '//a[@href="/shoptools/oauth/logout"]';
export const loggedOutSelector = loginInputSelector;
export const logOutButton = '//li/a[@href="/shoptools/oauth/logout" and contains(text(), "Log Out")]';
export const mfaCodeInputSelector = 'input#mfaCode,input#mfaMailCode'; // totp 2fa OR email code
export const mfaSkipVerification = 'a#skipMfaRegistrationLink';

export const dualAuthMaxAttempts = 1;

export const loginStateSelectors = {cssSelectors: [loggedOutSelector], xPaths: [loggedInXpathSelector]};
export const redirectStateSelectors = {cssSelectors: [signInButtonSelector, loginInputSelector]};
export const languageStateSelectors = {cssSelectors: [isEnglishLanguageSelector, isJapaneseLanguageSelector]};
export const afterLoginAttemptStateSelectors = {
    cssSelectors: [invalidCredentialsSelector, noLoginPermissionsSelector, mfaCodeInputSelector, mfaSkipVerification, signInButtonSelector],
    xPaths: [loggedInXpathSelector]
};
export const productCodeRegex = /^[A-Z0-9]{8,9}$/;
