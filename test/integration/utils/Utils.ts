import * as adm from 'adm-zip';
import * as fse from 'fs-extra';
import {CLIArguments} from '../../../src/cli/arguments';
import {main} from '../../../src/cli/cli';
import {mapToLegacyConfig} from '../../../src/config/ConfigService';
import {generateApiToken} from './ArgsProvider';

export async function removeIfFilesExist(...files: string[]): Promise<void> {
    await Promise.all(files.filter(Boolean).map((file) => fse.remove(file)));
}
export async function runCliMainWithConvertedArgs(args: CLIArguments) {
    args.apiToken = await generateApiToken();
    mapToLegacyConfig(args); // we need to convert to legacy config because we're calling main without going through scrape.ts which does it in production
    const result = await main(args, true);
    return result;
}

export function listZipContentNames(zipPath: string): string[] {
    return new adm(zipPath).getEntries().map((value) => value.entryName);
}

export function getDataFilesFromZip(zipPath: string) {
    return new adm(zipPath).getEntries().filter((entry) => entry.entryName !== 'manifest.json');
}

export function zipEntryToString(file: adm.IZipEntry): string {
    return file.getData().toString('utf8');
}

export function getFileFromZip(zipPath: string, fileName: string) {
    return new adm(zipPath).getEntries().filter((entry) => entry.entryName === fileName);
}
