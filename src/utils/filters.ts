export function filterIgnoredItems<T, K extends keyof T = keyof T>(
    itemsArray: T[],
    ignoredItemsArray: Array<T[K]> | undefined = [],
    nestedKey?: K,
    cb?: (ignoredItem: T) => void
): T[] {
    return itemsArray.filter((item) => {
        if (ignoredItemsArray.includes(item[nestedKey as string] ?? item)) {
            cb && cb(item[nestedKey as string] ?? item);
            return false;
        }
        return true;
    }) as T[];
}
