import {AxiosResponse} from 'axios';
import {INTERNAL_SERVER_ERROR} from 'http-status-codes';
import * as _ from 'lodash';
import * as puppeteer from 'puppeteer';
import {get, post} from '../apiCommunication/axios';
import {userAgent} from '../browserV2/BrowserV2';
import {chromiumExecutablePath, downloadDirPath, puppeteerDevtools, puppeteerHeadless, puppeteerSlowMo} from '../config/ConfigService';
import * as config from '../config/ConfigService';
import {CustomException} from '../error/CustomException';
import {errorTypes} from '../error/errorTypes';
import {RequestFailedDueToTheServerIssueException, RequestFailedException} from '../error/exceptions';
import {NoSuchSelectorException} from '../error/exceptions/NoSuchSelectorException';
import {UnableToLoadUrlException} from '../error/exceptions/UnableToLoadUrlException';
import {<PERSON><PERSON>, <PERSON>ementHand<PERSON>, KeyIn<PERSON>, PuppeteerBrowser, PuppeteerLaunchOptions, PuppeteerPage, Response} from '../puppeteer';
import {convertCookiesToString} from '../utils/http';
import {log} from '../utils/logger';
import {elementDoesNotExist, intervalSearchElementExists} from './activeWait';
import * as browserMethods from './browserMethods';
import {downloadFileUsingElementHandle, downloadFileUsingLink, setDownloadPath} from './download';
import {ErrorPageValidator} from './ErrorPageValidator';
import {IFrame} from './IFrame';

const targetClosedError = 'Protocol error (Target.closeTarget): Target closed';

interface WaitForAnyOptions {
    cssSelectors?: string[];
    xPaths?: string[];
    timeout?: number;
    visible?: boolean;
}

type SelectorMatch = [string, ElementHandle];

/** @deprecated */
export class Browser {
    static readonly defaultSelectorTimeout: number = 10000;
    private static readonly gotoMaxRetriesOnError: number = 5;
    private static readonly goToDefaultTimeout: number = 300000;
    private static readonly defaultRequestTimeout: number = 120000;
    private static readonly defaultWaitForJSONResponseTimeout: number = 30000;
    private static readonly puppeteerOptions: PuppeteerLaunchOptions = {
        headless: puppeteerHeadless(),
        slowMo: puppeteerSlowMo,
        args: ['--no-sandbox', '--proxy-server="direct://"', '--proxy-bypass-list=*', '--disable-features=SameSiteByDefaultCookies,CookiesWithoutSameSiteMustBeSecure'],
        executablePath: chromiumExecutablePath(),
        devtools: puppeteerDevtools
    };
    readonly page: PuppeteerPage;
    private readonly browser: PuppeteerBrowser;
    private iFrame: IFrame;

    constructor(browser: PuppeteerBrowser, page: PuppeteerPage) {
        this.browser = browser;
        this.page = page;
    }

    // TODO this is only for testing purposes, remove it after refactoring
    public static async launchNewBrowser(cookies: Cookie[]): Promise<Browser> {
        const puppeteerBrowser = await puppeteer.launch(Browser.puppeteerOptions);
        const page = await puppeteerBrowser.newPage();
        await page.setCookie(...(cookies as any));
        const browser = new Browser(puppeteerBrowser, page);
        await browser.page.setViewport({width: 1366, height: 768});
        if (config.downloadDirPath()) {
            await setDownloadPath(browser.page, downloadDirPath());
        }
        await browser.page.setUserAgent(userAgent);
        return browser;
    }

    public async waitForSelector(selector: string, timeout: number = Browser.defaultSelectorTimeout, visible = true, isXPath = false): Promise<ElementHandle | null> {
        return intervalSearchElementExists(this.page, selector, {timeout, visible, isXPath});
    }

    /**
     * Waits for any of the selectors to appear on the page. Will resolve with the first selector that appears.
     */
    public async waitForAnySelector({
        cssSelectors = [],
        xPaths = [],
        timeout = Browser.defaultSelectorTimeout,
        visible = true
    }: WaitForAnyOptions): Promise<SelectorMatch> {
        const selectorTasks = cssSelectors.map(async (selector) => <SelectorMatch>[selector, await this.page.waitForSelector(selector, {timeout, visible})]);
        const xPathTasks = xPaths.map(async (xPath) => <SelectorMatch>[xPath, await this.page.waitForSelector(`::-p-xpath(${xPath})`, {timeout, visible})]);

        return Promise.race([...selectorTasks, ...xPathTasks]);
    }

    public async type(selector: string, input: string): Promise<void> {
        return browserMethods.type(this, this.page, selector, input);
    }

    public async querySelector(selector: string): Promise<ElementHandle | null> {
        return browserMethods.querySelector(this.page, selector);
    }

    public async clickXpath(xpath: string): Promise<void> {
        await this.page.locator(`::-p-xpath(${xpath})`).click();
    }

    public async queryMultipleSelector(selector: string): Promise<ElementHandle[]> {
        return this.page.$$(selector);
    }

    public async clearAndType(selector: string, input: string): Promise<void> {
        return browserMethods.clearAndType(this, this.page, selector, input);
    }

    public async clearWithSelectAllAndType(selector: string, input: string): Promise<void> {
        await this.page.focus(selector);
        await this.page.keyboard.down('Control');
        await this.page.keyboard.press('A');
        await this.page.keyboard.up('Control');
        await this.page.keyboard.press('Backspace');
        return await this.type(selector, input);
    }

    public async click(selector: string): Promise<void> {
        return browserMethods.click(this, this.page, selector);
    }

    public async goto(url: string, isOnErrorPageValidator?: ErrorPageValidator, retryCounter = 0): Promise<Response | null> {
        const response = await this.page.goto(url, {timeout: Browser.goToDefaultTimeout});
        if (!isOnErrorPageValidator || !(await isOnErrorPageValidator(this))) {
            return response;
        }
        if (retryCounter < Browser.gotoMaxRetriesOnError) {
            log.info(`Error page detected, retrying ${retryCounter + 1} time`);
            return this.goto(url, isOnErrorPageValidator, retryCounter + 1);
        }
        throw new UnableToLoadUrlException(url);
    }

    public async getCurrentUrl(): Promise<string> {
        return this.page.url();
    }

    public async cookies(): Promise<Cookie[]> {
        const client = await this.page.target().createCDPSession();
        const {cookies} = (await client.send('Network.getAllCookies')) as {cookies: Cookie[]};
        return cookies;
    }

    public async close(): Promise<void> {
        const userAgent = await this.browser.userAgent();
        const pages = await this.browser.pages();
        for (const page of pages) {
            try {
                await page.close();
            } catch (error) {
                /**
                 * This is specific problem with closing browser with headless: false.
                 * The problem occurs with closing the last page of the browser, but it is needed to remove all processes and temporary file in the operating system.
                 */
                if (!browserMethods.isHeadlessBrowser(userAgent) && error.message === targetClosedError) {
                    continue;
                }
                throw error;
            }
        }

        try {
            await this.browser.close();
        } catch (error) {
            if (error.message.includes('Puppeteer was unable to kill the process which ran the browser binary.')) {
                throw new CustomException({
                    message: 'Application encountered unexpected problem with permissions to manage the browser.',
                    error,
                    errorType: errorTypes.TEMPORARY_PORTAL_ISSUE
                });
            }
            throw error;
        }
    }

    public async reloadPage(): Promise<Response | null> {
        return this.page.reload();
    }

    public async waitForSelectorToDisappear(selector: string, timeout: number = Browser.defaultSelectorTimeout): Promise<void> {
        return elementDoesNotExist(this.page, selector, timeout);
    }

    public async elementExists(selector: string, timeout: number = Browser.defaultSelectorTimeout, visible = true): Promise<boolean> {
        return browserMethods.elementExists(this, selector, timeout, visible);
    }

    public async waitForNavigation(timeout: number = Browser.defaultSelectorTimeout): Promise<Response | null> {
        return this.page.waitForNavigation({timeout});
    }

    public async select(selector: string, value: string): Promise<string[]> {
        return this.page.select(selector, value);
    }

    public async xPathElementExistsAfterTime(xPathSelector: string, timeout: number = Browser.defaultSelectorTimeout): Promise<boolean> {
        return browserMethods.xPathElementExistsAfterTime(this.page, xPathSelector, timeout);
    }

    public async getValueOfProperty(selector: string, property: string): Promise<string> {
        const element = await this.querySelector(selector);
        if (!element) {
            throw new NoSuchSelectorException(selector);
        }
        return this.getPropertyValueFromElement(element, property);
    }

    public async getPropertyValueFromElement(element: ElementHandle, property: string): Promise<string> {
        return (await element.getProperty(property)).jsonValue() as Promise<string>;
    }

    public async getElementText(selector: string): Promise<string> {
        await this.waitForSelector(selector);
        return this.getValueOfProperty(selector, 'innerText');
    }

    public async downloadReportFile(link: string, fileName: string, allowedContentTypes?: string[]): Promise<void> {
        const cookieString = await this.convertCookiesToRequestFormat();
        return downloadFileUsingLink(link, fileName, cookieString, allowedContentTypes);
    }

    public async downloadReportFileByClick(selector: string, fileName: string): Promise<void> {
        const element = await this.page.waitForSelector(selector);
        return downloadFileUsingElementHandle(this.page, element!, fileName);
    }

    public async get<T>(url: string, cookieDomainFilter?: string, timeout: number = Browser.defaultRequestTimeout): Promise<T> {
        const cookieString = await this.convertCookiesToRequestFormat(cookieDomainFilter);
        let response: AxiosResponse;
        try {
            response = await get(url, {
                headers: {
                    Cookie: cookieString
                },
                timeout
            });
        } catch (error) {
            const status = _.get(error, 'response.status', null);
            if (status >= INTERNAL_SERVER_ERROR) {
                throw new RequestFailedDueToTheServerIssueException(error, url);
            }
            throw new RequestFailedException(error);
        }

        return response.data;
    }

    public async post<T>(url: string, payload: unknown, cookiesFilter?: string, timeout: number = Browser.defaultRequestTimeout): Promise<T> {
        const cookieString = await this.convertCookiesToRequestFormat(cookiesFilter);
        try {
            const {data} = await post(url, payload, {
                headers: {
                    Cookie: cookieString
                },
                timeout
            });
            return data;
        } catch (error) {
            const status = _.get(error, 'response.status', null);
            if (status >= INTERNAL_SERVER_ERROR) {
                throw new RequestFailedDueToTheServerIssueException(error, url);
            }
            throw new RequestFailedException(error);
        }
    }

    public async wait(ms: number): Promise<void> {
        return new Promise((resolve) => setTimeout(resolve, ms));
    }

    public async getHandleAndClick(selector: string, timeout: number = Browser.defaultSelectorTimeout, isXPath = false): Promise<void> {
        await this.waitForSelector(selector, timeout, true, isXPath);
        const elem = isXPath ? await this.page.$(`::-p-xpath(${selector})`) : await this.page.$(selector);
        if (!elem) {
            throw new NoSuchSelectorException(selector);
        }
        // instead elem.click(), we doing it through evaluate: https://stackoverflow.com/a/66537619/338581
        const stringFn = isXPath ? `document.evaluate('${selector}', document).iterateNext().click()` : `document.querySelector('${selector}').click()`;
        await this.page.evaluate(stringFn);
    }

    public async clickAllXPathMatchingSelector(selector: string): Promise<void> {
        const elementHandles = await this.page.$$(`::-p-xpath(${selector})`);
        await Promise.all(
            elementHandles.map(async (elementHandle) => {
                try {
                    await elementHandle.click();
                } catch (e) {
                    log.warning(`Non-clickable element: ${selector}; ${e}`);
                }
            })
        );
    }

    public async press(key: KeyInput): Promise<void> {
        await this.page.keyboard.press(key);
    }

    public async setIFrameWithSelector(selector: string): Promise<void> {
        const iFrameElement = await this.querySelector(selector);
        if (!iFrameElement) {
            throw new NoSuchSelectorException(selector);
        }
        this.iFrame = new IFrame((await iFrameElement.contentFrame())!);
    }

    public getIFrame(): IFrame {
        return this.iFrame;
    }

    /**
     * Converts cookies to format that can be used as axios request headers
     * @param filterDomain - if provided, only cookies for this domain will be returned
     * @private
     */
    private async convertCookiesToRequestFormat(filterDomain?: string): Promise<string> {
        const allCookies = await this.cookies();
        const cookies = filterDomain ? allCookies.filter(({domain}) => domain === filterDomain) : allCookies;
        return convertCookiesToString(cookies);
    }
}
