import {Buffer} from 'buffer';
import {ReadStream, WriteStream, createWriteStream} from 'fs';
import {basename, extname, join} from 'path';
import * as fse from 'fs-extra';
import * as JSZip from 'jszip';
import {isNil} from 'lodash';
import * as moment from 'moment';
import {ParseConfig, parse as parseCSV} from 'papaparse';
import slugify from 'slugify';
import {downloadDirPath} from '../../config/ConfigService';
import {Source} from '../../dataTypes';
import {log} from '../../utils/logger';
import {sleep} from '../sleep';
import {waitTillStreamFinished} from '../streams';
import {FileExtension} from './FileExtension';

const FINAL_TIMEOUT_OFFSET = 1;
const NON_REG_EXP_INTERVAL_AMOUNT = 60;
const DEFAULT_TIMEOUT = 120000;
const DATE_FORMAT = 'YYYY-MM-DD';
export const MAX_RENAME_RETRIES = 5;

export async function checkFileExists(fileName: string, withTimeout?: boolean, timeout?: number): Promise<boolean> {
    async function handler(): Promise<boolean> {
        await fse.access(buildFilePath(fileName));
        return true;
    }

    if (withTimeout) {
        return !!(await createIntervalTask(timeout || DEFAULT_TIMEOUT, NON_REG_EXP_INTERVAL_AMOUNT, handler));
    }
    return fse.pathExists(buildFilePath(fileName));
}

export async function deleteFile(fileName: string): Promise<void> {
    const fileToDelete = buildFilePath(fileName);
    if (await fse.pathExists(fileToDelete)) {
        await fse.remove(fileToDelete);
    }
}

export async function deleteFiles(fileNames: string[]): Promise<void> {
    await Promise.all(fileNames.map(deleteFile));
}

export async function saveFileToDownloads(fileName: string, content: any): Promise<void> {
    const filePath = buildFilePath(fileName);
    await ensureDirectory(downloadDirPath());
    if (extname(fileName) === `.${FileExtension.JSON}`) {
        await fse.writeJson(filePath, content);
    } else {
        await fse.writeFile(filePath, content);
    }
}

export async function zipFiles(filenamesToZip: string[], zipFileName: string): Promise<void> {
    return new Promise<void>(async (resolve, reject): Promise<void> => {
        try {
            const zip: JSZip = new JSZip();
            const filePathsToZip = filenamesToZip.map(buildFilePath);
            for (const file of filePathsToZip) {
                const splittedPath = basename(file);
                zip.file(splittedPath, await fse.readFile(file));
            }
            zip.generateNodeStream({type: 'nodebuffer', streamFiles: true})
                .pipe(fse.createWriteStream(buildFilePath(zipFileName)))
                .on('close', resolve)
                .on('error', reject);
        } catch (error) {
            reject(error);
        }
    });
}

export async function renameFile(currentFileName: string, newFileName: string, retryCounter = 0): Promise<void> {
    return renameFileByPath(buildFilePath(currentFileName), buildFilePath(newFileName), retryCounter);
}

export async function renameFileByPath(currentFilePath: string, newFilePath: string, retryCounter = 0): Promise<void> {
    try {
        await fse.rename(currentFilePath, newFilePath);
    } catch (error) {
        const DELAY = 500;
        if (error.code === 'EBUSY' && retryCounter < MAX_RENAME_RETRIES) {
            log.debug(`${currentFilePath} is busy, retry  renaming in ${DELAY}ms`);
            await sleep(DELAY);
            return renameFileByPath(currentFilePath, newFilePath, retryCounter + 1);
        }
        throw error;
    }
}

export function generateFileName(source: Source, startDate: moment.Moment, endDate: moment.Moment, extension: FileExtension, additionalIndex?: string | number): string {
    const knownFormatStartDate = startDate.format(DATE_FORMAT);
    const knownFormatEndDate = endDate.format(DATE_FORMAT);
    if (!isNil(additionalIndex)) {
        const safeAdditionalIndex = slugify(additionalIndex.toString(), {remove: /[*+~.()'"!:@?\\/]/g});
        return `${source}-${knownFormatStartDate}_${knownFormatEndDate}-${safeAdditionalIndex}.${extension}`;
    }
    return `${source}-${knownFormatStartDate}_${knownFormatEndDate}.${extension}`;
}

export async function saveStreamToFile(source: ReadStream, filename: string): Promise<void> {
    const destination = createWriteStreamInDownloadDirectory(filename);
    source.pipe(destination);
    return waitTillStreamFinished(source, destination);
}

// WARNING this is Nintendo specific as we are removing last 2 lines from CSV file in order to parse it
export async function convertNintendoJSONOrCSVToCSV(filename: string, options: ParseConfig = {}): Promise<boolean> {
    const filePath = buildFilePath(filename);
    try {
        let content = await fse.readFile(filePath, 'utf-8');

        // Attempt to parse JSON if applicable
        try {
            const jsonContent = JSON.parse(content);
            content = Buffer.from(jsonContent.data, 'base64').toString('utf-8');
            // Override the original file with decoded content
            await fse.writeFile(filePath, content, 'utf-8');
            log.debug('File overridden with decoded CSV content.');
        } catch (error) {
            log.debug(`File is not a valid JSON. Treating as CSV. Error: ${error.message}`);
        }

        // Process CSV without removing the last 2 lines
        const contentWithout2LastLines = content.split('\n').slice(0, -2).join('\n');
        const {errors, data} = parseCSV(contentWithout2LastLines, {header: true, ...options});

        log.debug(`CSV file contains ${data.length} rows`);
        if (errors.length > 0) {
            log.debug('Errors in CSV file:');
            errors.forEach((error) => log.debug(error.message));
        }
        return errors.length === 0;
    } catch (error) {
        log.debug(`Failed to process file: ${error.message}`);
        return false;
    }
}

export function createWriteStreamInDownloadDirectory(fileName: string): WriteStream {
    return createWriteStream(buildFilePath(fileName));
}

async function createIntervalTask<T>(timeout: number, intervalAmount: number, handler: () => Promise<T>): Promise<T | null> {
    return new Promise((resolve: (result: T | null) => void): void => {
        const intervalTime = timeout / intervalAmount;
        const intervalId = setInterval(async () => {
            try {
                const result = await handler();
                clearTimeout(timeoutTimerId);
                clearInterval(intervalId);
                resolve(result);
            } catch {
                // Try again
            }
        }, intervalTime);

        function handleTimeout(): void {
            clearInterval(intervalId);
            resolve(null);
        }

        const timeoutTimerId = setTimeout(handleTimeout, timeout + FINAL_TIMEOUT_OFFSET);
    });
}

async function ensureDirectory(filePath: string): Promise<void> {
    return fse.ensureDir(filePath);
}

export function buildFilePath(fileName: string): string {
    return join(downloadDirPath(), fileName);
}
