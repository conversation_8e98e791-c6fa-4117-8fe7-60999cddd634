import {Source, sourceToPortal} from '../../dataTypes';
import {LogLevel} from '../../utils/logger';
import {CustomException} from '../CustomException';
import {errorTypes} from '../errorTypes';

export class AllOrganizationsIgnoredException extends CustomException {
    constructor(source: Source, canIgnoreOrgsDuringSetup: boolean) {
        const message = `All organizations were marked to be ignored! The ${sourceToPortal(source)} scraper has nothing to scrape.`;
        const suggestedAction = canIgnoreOrgsDuringSetup
            ? ` Please log out and set up the ${sourceToPortal(source)} scraper again and choose at least one organization.`
            : undefined;

        super({
            message,
            suggestedAction,
            telemetryLogLevel: LogLevel.WARN,
            errorType: errorTypes.CONFIGURATION_ISSUE
        });
    }
}
