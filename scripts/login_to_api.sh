#!/bin/bash

# Load variables from .env file
if [ -f .env ]; then
  source .env
else
  echo "Error: .env file not found"
  exit 1
fi

# Check if variables are provided
if [[ -z "$INDIEBI_EMAIL" || -z "$INDIEBI_PASSWORD" ]]; then
  echo "Error: INDIEBI_EMAIL or INDIEBI_PASSWORD not provided in .env file"
  exit 1
fi

# Make the curl request and extract the JWT token
response=$(curl -s -w "%{http_code}" --location --request POST "$SCRAPER_API_URL/1/login" \
--header 'Content-Type: application/json' \
--data-raw "{
    \"email\": \"$INDIEBI_EMAIL\",
    \"password\": \"$INDIEBI_PASSWORD\"
}")

status_code=${response: -3}

if [[ "$status_code" -ne 200 ]]; then
  if [[ "$status_code" == "000" ]]; then
    echo "Error: No response from server. Please check if your VPN is enabled or if there are network connectivity issues."
  elif echo "$response" | grep -q 'Invalid user or password'; then
    echo "Error: Invalid user or password"
  else
    echo "Error: Unexpected response status code: $status_code"
  fi
  exit 1
fi

jwt=$(echo "$response" | sed '$ s/...$//' | jq -r '.jwt')

# Check if JWT token was obtained
if [[ -z "$jwt" ]]; then
  echo "Error: JWT token not obtained"
  exit 1
fi

# Function to update or add JWT token variable in the .env file
update_jwt_in_env() {
  local file_path=$1
  local jwt_token=$2

  # Ensure the file exists and is a regular file or a symbolic link
  if [ ! -e "$file_path" ]; then
    echo "Error: $file_path does not exist."
    return 1
  fi

  # Read the content of the .env file
  local env_content=$(cat "$file_path")
  local jwt_exists=$(echo "$env_content" | grep -c '^INDIEBI_JWT=')

  if [ "$jwt_exists" -eq 0 ]; then
    # INDIEBI_JWT does not exist, append it
    echo -e "$env_content\nINDIEBI_JWT=$jwt_token" > "$file_path"
    echo "INDIEBI_JWT variable added to .env file."
  else
    # INDIEBI_JWT exists, replace its value
    local updated_content=$(echo "$env_content" | sed "s/^INDIEBI_JWT=.*/INDIEBI_JWT=$jwt_token/")
    echo "$updated_content" > "$file_path"
    echo "INDIEBI_JWT variable updated in .env file in location: $(realpath "$file_path")"
  fi
}

# Update or add the JWT token variable in the .env file
update_jwt_in_env .env "$jwt"
