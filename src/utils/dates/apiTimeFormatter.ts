import {Moment} from 'moment';

const dateFormat = 'YYYY-MM-DD[T]HH:mm:ss.SSS[Z]';

/**
 * @param date date which needs to be converted to proper format regardless of local timezone
 *
 * This format is here on purpose! It could have been switched to toISOString() but that uncovered an issue with
 * local time zones. For this reason we purposefully keep the date format the way you see it below.
 */
export default function forceProperFormatIgnoringLocalTimezone(date: Moment): string {
    return date.format(dateFormat);
}
