import {expect} from 'chai';
import * as moment from 'moment';
import {NintendoReportDownloader} from '../../../../src/scrapersV1/httpDownloaders/nintendo/NintendoReportDownloader';

describe('generateNintendoReportUrl', () => {
    it('should return the correct url', () => {
        const url = NintendoReportDownloader['generateNintendoReportUrl'](moment('2021-01-01'), moment('2021-01-02'), ['HACPA28VA', 'HACPA2L4A']);
        expect(url).to.equal(
            'https://sst.mng.nintendo.net/shoptools/switchLicenseeReports/titleReport/search?period=DAILY&beginYear=2021&beginMonth=01&endYear=2021&endMonth=01&begin=2021%2F01%2F01&end=2021%2F01%2F02&paid=&searchUnit=0&searchPrice=true&detail=none&searchTitle=&downloadCsv=downloadCsv&searchTitles=&searchCodes=HACPA28VA%0D%0AHACPA2L4A&regions=JPN&regions=USA&regions=EUR&regions=AUS&regions=KOR&regions=CHN&regions=TWN&regions=Other&types=TITLE&types=TRIAL&types=AOC&types=SERVICE_TICKET&types=BUNDLE&codes=P&codes=M&codes=V&codes=Y&codes=H&codes=X&devices=HAC&devices=BEE&additionals=NSUID'
        );
    });
});
