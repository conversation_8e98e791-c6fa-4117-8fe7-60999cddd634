# Steam Wishlist Country data scraper

## Permissions and multiorganizations

It is possible for the user to have access to multiple organizations, in such case, we iterate through organizations list and download the list of products from each organization. There's no further check for permissions as wishlist data is available for all products the user have access to.

## Data acquisition

Data is available at `https://partner.steampowered.com/region/?appID=${product.id}&dateStart=${day}&dateEnd=${day}&alignPriorAnnual=Immediate` in form of HTML table.

We scrape the table and save it to the CSV file after some modifications. We ignore most of the data, removing "World" and "Regions" from the table, just saving the "Country" data. We also only store the "Wishlist balance" column, ignoring the rest as it is useless in terms of the wishlist analytics.

Scraper iterates through products list and downloads data for each product as a separate CSV file. For each product (SKU) we iterate through days in a date range to get granular balance data. We are making a single request for a single day.

In order to speed up the download process, we simultaneously download data for multiple days at once, keeping the number of concurrent requests at 3 to prevent Steam from blocking our IP address.

