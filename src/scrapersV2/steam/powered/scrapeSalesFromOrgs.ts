import {Readable} from 'stream';
import _ = require('lodash');
import {Moment} from 'moment/moment';
import {Source} from '../../../dataTypes';
import {AllProductsIgnoredException, NoProductsToDownloadException} from '../../../error/exceptions';
import {downloadForOrganizationFinished, foundMultipleOrganizations, startingDownloadForOrganization} from '../../../telemetry/events/multiorgEvents';
import {HTTPClient} from '../../../utils/http';
import {ProgressCallback} from '../../ProgressCallback';
import {FeatureFlag} from '../../Scraper';
import {SteamOrganization} from '../common/SteamSession';
import {downloadSteamPoweredFileViaPost, steamPoweredCheerioRequest, steamPoweredRequest, steamPoweredUrl} from './steamPoweredRequest';
import {retrievePackageUnitsSold, retrieveTotalUnitsSold} from './steamSalesChecksums';

export interface SteamPackage {
    id: string;
    name: string;
}

const nonApplicableSemanticallyValidValue = -1;

interface DownloadResult {
    stream: Readable;
    /** @deprecated */
    totalUnitsSold: number; // After talking with Magda we would like to get rid of this field "sometime" in the future (written on 2024-05-15).
    contentType: 'sales' | 'in-app-sales' | 'complementary-packages';
    package?: SteamPackage;
}

export interface SteamSalesScrapingOptions {
    httpClient: HTTPClient;
    progress: ProgressCallback;
    startDate: Moment;
    endDate: Moment;
}

export async function* scrapeSalesFromOrgs(
    ctx: SteamSalesScrapingOptions,
    ignoredPackages: string[],
    steamOrganizations: SteamOrganization[],
    featureFlags: FeatureFlag[]
): AsyncGenerator<DownloadResult> {
    foundMultipleOrganizations(steamOrganizations.map((org) => org.name));

    let totalPackages = 0;
    let totalPackagesToDownload = 0;
    let hasFullOrgReports = false;

    for (const org of steamOrganizations) {
        startingDownloadForOrganization(org.name);
        const packages = await findAvailablePackages(ctx.httpClient, org);
        ctx.progress(`Found ${packages.length} packages for ${org.name} (${org.id})`);
        const packagesToDownload = filterIgnoredPackages(packages, ignoredPackages);
        ctx.progress(`Will scrape ${packagesToDownload.length} and ignore ${ignoredPackages.length} packages`);
        totalPackages += packages.length;
        totalPackagesToDownload += packagesToDownload.length;
        if (org.isLowPermission || ignoredPackages.length !== 0) {
            ctx.progress(`Scraping packages of ${org.name} (${org.id}) individually`);

            for (const pkg of packagesToDownload) {
                ctx.progress(`Downloading single report for #${pkg.id} ${pkg.name}`);
                yield await downloadSalesReport(ctx, org, pkg);
            }

            if (featureFlags.includes('steam-include-in-app-sales')) {
                for (const pkg of packagesToDownload) {
                    ctx.progress(`Downloading single in-app sales for #${pkg.id} ${pkg.name}`);
                    yield await downloadInAppSalesReport(ctx, org, pkg);
                }
            }
        } else {
            hasFullOrgReports = true;
            ctx.progress(`Downloading full report for ${org.name} (${org.id})`);
            yield await downloadSalesReport(ctx, org);

            if (featureFlags.includes('steam-include-in-app-sales')) {
                ctx.progress(`Downloading full in-app sales report for ${org.name} (${org.id})`);
                yield await downloadInAppSalesReport(ctx, org);
            }
        }

        if (featureFlags.includes('steam-include-complementary-packages')) {
            for (const pkg of packagesToDownload) {
                ctx.progress(`Downloading single complimentary report for #${pkg.id} ${pkg.name}`);
                yield await downloadComplementaryPackagesReports(ctx, org, pkg);
            }
        }

        downloadForOrganizationFinished(org.name);
    }

    if (!hasFullOrgReports) {
        if (totalPackages == 0) {
            throw new NoProductsToDownloadException();
        }
        if (totalPackagesToDownload == 0) {
            throw new AllProductsIgnoredException(Source.STEAM_SALES);
        }
    }
}

async function downloadComplementaryPackagesReports(ctx: SteamSalesScrapingOptions, org: SteamOrganization, steamPackage: SteamPackage): Promise<DownloadResult> {
    const startDateStr = ctx.startDate.format('YYYY-MM-DD');
    const endDateStr = ctx.endDate.format('YYYY-MM-DD');
    const path = `/report_csv.php`;

    const response = await downloadSteamPoweredFileViaPost(ctx.httpClient, path, org.id, {
        params: `query=QueryPackageActivationsForCSV^pkgID=${steamPackage.id}^dateStart=${startDateStr}^dateEnd=${endDateStr}^interpreter=PartnerActivationReportInterpreter`
    });

    return {stream: await response.data, totalUnitsSold: nonApplicableSemanticallyValidValue, contentType: 'complementary-packages', package: steamPackage};
}

async function downloadSalesReport(ctx: SteamSalesScrapingOptions, org: SteamOrganization, pkg?: SteamPackage): Promise<DownloadResult> {
    const csvStream = downloadCSV(ctx, org, pkg);
    const totalUnitsSold = pkg ? retrievePackageUnitsSold(ctx, org, pkg) : retrieveTotalUnitsSold(ctx, org);
    return {stream: await csvStream, totalUnitsSold: await totalUnitsSold, contentType: 'sales', package: pkg};
}

async function downloadInAppSalesReport(ctx: SteamSalesScrapingOptions, org: SteamOrganization, pkg?: SteamPackage): Promise<DownloadResult> {
    const csvStream = downloadInAppSalesCsv(ctx, org, pkg);

    return {stream: await csvStream, totalUnitsSold: nonApplicableSemanticallyValidValue, contentType: 'in-app-sales', package: pkg};
}

async function downloadInAppSalesCsv({httpClient, startDate, endDate}: SteamSalesScrapingOptions, org: SteamOrganization, pkg?: SteamPackage): Promise<Readable> {
    const startDateStr = startDate.format('YYYY-MM-DD');
    const endDateStr = endDate.format('YYYY-MM-DD');

    let path = `/report_csv.php?file=SteamInGameSales_${startDateStr}_to_${endDateStr}&params=query=QueryInGameSalesForCSV^dateStart=${startDateStr}^dateEnd=${endDateStr}^HasDivisions=0^interpreter=MicrotxnSalesReportInterpreter`;

    if (pkg) {
        path += `^appID=${pkg.id}`;
    }

    const response = await steamPoweredRequest(path, {httpClient, org, isStream: true});
    return response.data;
}

async function downloadCSV({httpClient, startDate, endDate}: SteamSalesScrapingOptions, org: SteamOrganization, pkg?: SteamPackage): Promise<Readable> {
    const startDateStr = startDate.format('YYYY-MM-DD');
    const endDateStr = endDate.format('YYYY-MM-DD');
    let path = `/report_csv.php?file=x&params=query=QueryPackageSalesForCSV^dateStart=${startDateStr}^dateEnd=${endDateStr}^HasDivisions=0^interpreter=PartnerSalesReportInterpreter`;
    if (pkg) {
        path += `^pkgID=${pkg.id}`;
    }

    const response = await steamPoweredRequest(path, {httpClient, org, isStream: true});
    return response.data;
}

async function findAvailablePackages(http: HTTPClient, org: SteamOrganization) {
    const $ = await steamPoweredCheerioRequest('/dir.php', http, org);
    const idFromUrlRegex = /details\/(\d+)\//;
    const packages = $(`a[href^="${steamPoweredUrl}/package/details"]`)
        .map(
            (_i, el) =>
                <SteamPackage>{
                    id: $(el).attr('href')!.match(idFromUrlRegex)![1],
                    name: $(el).text()
                }
        )
        .toArray();

    return _.uniqBy(packages, 'id');
}

function filterIgnoredPackages(packages: SteamPackage[], ignoredProducts: string[]) {
    return packages.filter((product) => !ignoredProducts.includes(product.id));
}
