import {ReadStream} from 'fs';
import {join} from 'path';
import {inspect} from 'util';
import {AxiosError, AxiosResponse} from 'axios';
import Protocol from 'devtools-protocol';
import {StatusCodes} from 'http-status-codes';
import * as _ from 'lodash';
import {get} from '../apiCommunication/axios';
import {printTraceWithError} from '../cli/messaging';
import {downloadDirPath} from '../config/ConfigService';
import {CustomException} from '../error/CustomException';
import {DownloadFailedDueToExternalServiceIssuesException, UnableToDownloadReportException} from '../error/exceptions';
import {ElementHandle, PuppeteerPage} from '../puppeteer';
import {renameFileByPath, saveStreamToFile} from '../utils/files/fileUtils';
import {log} from '../utils/logger';
import {validateReportResponseHeaders} from '../utils/ResponseValidator';
import {monotonicTimer} from '../utils/timer';

const defaultDownloadTimeout = 900000; // 15 min
export async function downloadFileUsingLink(link: string, fileName: string, cookieString: string, allowedContentTypes?: string[]): Promise<void> {
    log.debug(`Downloading ${fileName} from ${link}`);
    let response: AxiosResponse<ReadStream>;
    try {
        response = await get<ReadStream>(link, {
            responseType: 'stream',
            headers: {Cookie: cookieString},
            timeout: defaultDownloadTimeout
        });
        validateReportResponseHeaders(response, allowedContentTypes);
        await saveStreamToFile(response.data, fileName);
    } catch (error) {
        if (error instanceof CustomException) {
            throw error;
        }
        if (error instanceof AxiosError && error.config?.headers) {
            // prevent logging client headers
            error.config.headers = {} as any;
        }
        log.debug('Failed to download file', {fileName, link});
        printTraceWithError(error);
        const status = _.get(error, 'response.status', null);
        if (status >= StatusCodes.INTERNAL_SERVER_ERROR) {
            throw new DownloadFailedDueToExternalServiceIssuesException({reportId: fileName, status, data: error.response?.data});
        }
        throw new UnableToDownloadReportException(error, link);
    }
}

// It will fail if run simultaneously :(
export async function downloadFileUsingElementHandle(page: PuppeteerPage, element: ElementHandle, fileName: string): Promise<void> {
    return new Promise(async (resolve, reject) => {
        const startTime = monotonicTimer();
        const client = await page.createCDPSession();
        const expectedFilenames = {};

        await client.send('Browser.setDownloadBehavior', {
            behavior: 'allowAndName',
            downloadPath: downloadDirPath(),
            eventsEnabled: true
        });

        const downloadStartHandler = async (event: Protocol.Browser.DownloadWillBeginEvent) => {
            log.debug(`Download will begin for ${fileName}, url: ${event.url}, suggestedFileName: ${event.suggestedFilename}, guid: ${event.guid}`);
            expectedFilenames[event.guid] = fileName;
        };

        const downloadProgressHandler = async (event: Protocol.Browser.DownloadProgressEvent) => {
            log.debug(`Download progress for ${fileName}: ${inspect(event)}`);
            if (event.state === 'completed') {
                const downloadDuration = monotonicTimer() - startTime;
                log.info(`The file has been saved with name: ${fileName} in ${downloadDuration}s.`);
                log.debug('File download finished', {fileName, downloadDuration});
                await renameFileByPath(join(downloadDirPath(), event.guid), join(downloadDirPath(), expectedFilenames[event.guid]));
                client.off('Browser.downloadProgress', downloadProgressHandler);
                return resolve();
            }
            if (event.state === 'canceled') {
                log.info(`Download canceled for ${fileName}: ${event}`);
                client.off('Browser.downloadProgress', downloadProgressHandler);
                return reject();
            }
        };

        client.once('Browser.downloadWillBegin', downloadStartHandler);
        client.on('Browser.downloadProgress', downloadProgressHandler);

        log.debug('File download started', {fileName});
        log.info(`The file will be saved with name ${fileName}.`);
        log.info('Clicking download button');
        await element.click();
    });
}

export async function setDownloadPath(page: PuppeteerPage, downloadPath: string): Promise<void> {
    const client = await page.createCDPSession();
    await client.send('Page.setDownloadBehavior', {
        behavior: 'allow',
        downloadPath
    });
}
