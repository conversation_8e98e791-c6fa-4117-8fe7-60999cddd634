import * as moment from 'moment';
import {FileExtension} from '../../../src/utils/files/FileExtension';
import {createCheckSessionTest} from '../checkSessionTestGenerator';
import {LOGIN_ARGS, getScrapeArgs} from '../utils/ArgsProvider';
import {removeIfFilesExist, runCliMainWithConvertedArgs} from '../utils/Utils';
import {itShouldDownloadZippedDataAndCheckFileFormat} from './common';

describe('Meta (riftScraper) runner:', () => {
    const defaultStartDateString = '2020-01-01';
    const defaultEndDateString = '2020-02-01';
    const recentStartDate = moment.utc().subtract(4, 'days').format('YYYY-MM-DD');
    const recentEndDate = moment.utc().subtract(2, 'days').format('YYYY-MM-DD');
    const loginArgs = LOGIN_ARGS.for_meta_rift;
    const scrapeArg = getScrapeArgs(loginArgs, defaultStartDateString, defaultEndDateString, FileExtension.ZIP);
    const recentScrapeArgs = getScrapeArgs(loginArgs, recentStartDate, recentEndDate, FileExtension.ZIP);

    before(async () => {
        await removeIfFilesExist(scrapeArg.reportPath!);
        await runCliMainWithConvertedArgs(loginArgs);
    });

    createCheckSessionTest(loginArgs, {id: 'supersuperdata'});

    it('download Meta Rift zipped data and check file amount', itShouldDownloadZippedDataAndCheckFileFormat(scrapeArg, defaultStartDateString, defaultEndDateString));

    it(
        'download Meta Rift recent zipped data and verify number of files',
        itShouldDownloadZippedDataAndCheckFileFormat(recentScrapeArgs, recentStartDate, recentEndDate)
    );
});
