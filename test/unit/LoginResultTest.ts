import {expect} from 'chai';
import {LoginResult} from '../../src/scrapersV1/LoginResult';

describe('LoginResult should:', () => {
    it('create a successful login result', async () => {
        const result = LoginResult.createSuccessfulLoginResult();
        expect(result.getAuthenticationErrorString()).to.be.undefined;
        expect(result.isFailedLogin()).to.be.false;
        expect(result.isPasswordResetError()).to.be.false;
        expect(result.isAuthenticationException()).to.be.false;
        expect(result.is2FactorAuth()).to.be.false;
        expect(result.isPermanentlyBlocked()).to.be.false;
    });

    it('create a 2 factor auth result', async () => {
        const result = LoginResult.create2FactorAuthResult();
        expect(result.getAuthenticationErrorString()).to.be.undefined;
        expect(result.isFailedLogin()).to.be.true;
        expect(result.isPasswordResetError()).to.be.false;
        expect(result.isAuthenticationException()).to.be.false;
        expect(result.is2FactorAuth()).to.be.true;
        expect(result.isPermanentlyBlocked()).to.be.false;
    });

    it('create a password reset error result', async () => {
        const authenticationErrorString = 'string';
        const result = LoginResult.createPasswordChangeResult(authenticationErrorString);
        expect(result.getAuthenticationErrorString()).to.be.equal(authenticationErrorString);
        expect(result.isFailedLogin()).to.be.true;
        expect(result.isPasswordResetError()).to.be.true;
        expect(result.isAuthenticationException()).to.be.false;
        expect(result.is2FactorAuth()).to.be.false;
        expect(result.isPermanentlyBlocked()).to.be.false;
    });

    it('create a API login error result', async () => {
        const authenticationErrorString = 'string';
        const result = LoginResult.createAuthenticationExceptionResult(authenticationErrorString);
        expect(result.getAuthenticationErrorString()).to.be.equal(authenticationErrorString);
        expect(result.isFailedLogin()).to.be.true;
        expect(result.isPasswordResetError()).to.be.false;
        expect(result.isAuthenticationException()).to.be.true;
        expect(result.is2FactorAuth()).to.be.false;
        expect(result.isPermanentlyBlocked()).to.be.false;
    });

    it('create a authentication exception that is not permanently blocked result', async () => {
        const authenticationErrorString = 'string';
        const result = LoginResult.createAuthenticationExceptionResult(authenticationErrorString);
        expect(result.getAuthenticationErrorString()).to.be.equal(authenticationErrorString);
        expect(result.isFailedLogin()).to.be.true;
        expect(result.isPasswordResetError()).to.be.false;
        expect(result.isAuthenticationException()).to.be.true;
        expect(result.is2FactorAuth()).to.be.false;
        expect(result.isPermanentlyBlocked()).to.be.false;
    });

    [true, false].forEach((permanentlyBlockedStatus: boolean): void => {
        it('create a authentication exception that is permanently blocked result', async () => {
            const authenticationErrorString = 'string';
            const result = LoginResult.createAuthenticationExceptionResult(authenticationErrorString, permanentlyBlockedStatus);
            expect(result.getAuthenticationErrorString()).to.be.equal(authenticationErrorString);
            expect(result.isFailedLogin()).to.be.true;
            expect(result.isPasswordResetError()).to.be.false;
            expect(result.isAuthenticationException()).to.be.true;
            expect(result.is2FactorAuth()).to.be.false;
            expect(result.isPermanentlyBlocked()).to.be.equal(permanentlyBlockedStatus);
        });
    });
});
