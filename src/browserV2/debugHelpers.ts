import {join} from 'path';
import {AxiosResponse} from 'axios';
import * as fse from 'fs-extra';
import * as stringify from 'json-stringify-safe';
import {utc} from 'moment';
import {printTraceWithError} from '../cli/messaging';
import {PuppeteerPage} from '../puppeteer';
import {isHtmlResponse} from '../utils/http';
import {log} from '../utils/logger';

const DEFAULT_SUFFIX = 'emergency_close';
const timeFormatForDumps = 'YYYY-MM-DD-HH-mm-ss.SSS';

export async function httpClientEmergencyCloseHandler(response: AxiosResponse, dumpDirPath: string): Promise<void> {
    await dumpAxiosResponse(response, dumpDirPath);
}

export async function browserEmergencyCloseHandler(page: PuppeteerPage, dumpDirPath: string): Promise<void> {
    const dateStr = utc().format(timeFormatForDumps);

    const screenShotPath = join(dumpDirPath, `${dateStr}_${DEFAULT_SUFFIX}.png`);

    const html = await page.content();
    const htmlDumpPath = join(dumpDirPath, `${dateStr}_${DEFAULT_SUFFIX}.html`);

    try {
        await page.screenshot({path: screenShotPath, fullPage: true});
        await fse.writeFile(htmlDumpPath, html);
    } catch (error) {
        log.debug('Error while saving screenshot or html dump', {screenShotPath, htmlDumpPath});
        printTraceWithError(error);
    }
}

/**
 *  This function shouldn't be used in production code in other place than emergency action.
 *  It's exported to help debugging during development, but it's usage in other places should be removed before merging to master
 */
export async function dumpAxiosResponse(response: AxiosResponse, dumpDirPath: string, suffix = DEFAULT_SUFFIX): Promise<void> {
    const dateStr = utc().format(timeFormatForDumps);
    const isHtml = isHtmlResponse(response);
    const extension = isHtml ? 'html' : 'json';
    const path = join(dumpDirPath, `${dateStr}_${suffix}.${extension}`);
    const data = isHtml ? response.data : stringify({status: response.status, data: response.data});

    try {
        await fse.writeFile(path, data);
    } catch (error) {
        log.debug(`Error while saving ${extension} dump to path`), {path, extension};
        printTraceWithError(error);
    }
}

export async function dumpHAR(har: any, dumpDirPath: string, suffix: string = 'http-client'): Promise<void> {
    const dateStr = utc().format(timeFormatForDumps);
    const path = join(dumpDirPath, `${dateStr}_${suffix}.har`);
    try {
        await fse.writeFile(path, stringify(har));
    } catch (error) {
        log.debug('Error while saving HAR dump', {path});
        printTraceWithError(error);
    }
}

export function getRecordingPath(dumpDirPath: string, suffix = 'recording'): `${string}.webm` {
    const dateStr = utc().format(timeFormatForDumps);
    return `${join(dumpDirPath, `${dateStr}_${suffix}`)}.webm`;
}
