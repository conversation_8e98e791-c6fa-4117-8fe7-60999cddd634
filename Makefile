ROOT_DIR:=$(shell dirname $(realpath $(firstword $(MAKEFILE_LIST))))

encryptCredentials:
	cd ${ROOT_DIR}/test/vcr/test_cases && poetry run python encrypt_credentials.py --encrypt

decryptCredentials:
	cd ${ROOT_DIR}/test/vcr/test_cases && poetry run python encrypt_credentials.py --decrypt

encryptSessions:
	@echo "INFO: This script will encrypt sessions from .private/integration_tests_sessions into one file. \
	If you want to update a session, put/update the session file in that directory and then rerun this target.\n"
	cd ${ROOT_DIR}
	npm run test-integration-encryptSessions

decryptSessions:
	@echo "INFO: Sessions will be decrypted by any integration test code upon start. Run any integration test \
	and check .private/integration_tests_sessions. Not doing anything."

installVcr:
	@if [ -z ${GITLAB_PAT} ]; then \
		echo "Before installation you have to set up a GITLAB_PAT variable."; \
	else \
		cd ${ROOT_DIR}/test/vcr/test_cases && poetry run pip install scrapers-vcr --index-url https://__token__:${GITLAB_PAT}@gitlab.com/api/v4/projects/45807331/packages/pypi/simple; \
	fi
