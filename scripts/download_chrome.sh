#!/bin/bash

check_chrome() {
    echo "Checking if CHROME_PATH is a valid path and binary is working..."
    local chrome_path=$1
    if ! "$chrome_path" --version; then
        echo "Error: CHROME_PATH is not a valid path or binary is broken"
        exit 1
    fi
}

# Load variables from .env file
if [ -f .env ]; then
    source .env
else
    echo "Error: .env file not found"
    exit 1
fi

# check if CHROME_PATH is set. If it is, check if it's a valid path, by running Chromium --version,
if [[ -n "$CHROME_PATH" ]]; then
    echo "CHROME_PATH is set to: $CHROME_PATH"
    check_chrome "$CHROME_PATH"
else # otherwise download it
    # Get the chromium revision from the .gitlab-ci.yml file
    chromium_revision=$(grep 'CHROMIUM_REVISION:' .gitlab-ci.yml | awk '{ print $2 }')
    echo "Chromium revision extracted from .gitlab-ci.yml: $chromium_revision"

    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        platform="linux"
        arch="Linux_x64"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        platform="mac"
        arch="Mac"
    else
        echo "Error: Unsupported OS"
        exit 1
    fi

    downloadUrl="https://storage.googleapis.com/chromium-browser-snapshots/${arch}/${chromium_revision}/chrome-${platform}.zip"

    echo "Downloading Chrome from: $downloadUrl"

    # make sure the directory is empty
    rm -rf "$DATA_DIR/binaries/chromium/"
    mkdir -p "$DATA_DIR/binaries/chromium"

    # Download and unzip the chrome binary
    curl -L -# -o "$DATA_DIR/binaries/chromium/chrome.zip" "$downloadUrl"

    echo "Saved Chrome to: $DATA_DIR/binaries/chromium/chrome.zip"
    echo "Unzipping Chrome..."
    unzip -q "$DATA_DIR/binaries/chromium/chrome.zip" -d "$DATA_DIR/binaries/chromium"
    rm "$DATA_DIR/binaries/chromium/chrome.zip"

    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        CHROME_PATH="$DATA_DIR/binaries/chromium/chrome-linux/chrome"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        CHROME_PATH="$DATA_DIR/binaries/chromium/chrome-mac/Chromium.app/Contents/MacOS/Chromium"
    fi

    check_chrome "$CHROME_PATH"

    echo "CHROME_PATH=$CHROME_PATH" >> .env
    echo "CHROME_PATH in .env file is updated to: $CHROME_PATH"
fi
