import * as cheerio from 'cheerio';
import {InsufficientPrivilegesLevelException, LoginException, LoginExceptionType} from '../../../error/exceptions';
import {HTTPClient} from '../../../utils/http';
import {CheckSessionFunction} from '../../Scraper';
import {checkSteamSession} from '../common/checkSession';
import {SteamParams} from '../common/login';
import {SteamSession} from '../common/SteamSession';
import {steamPoweredCheerioRequest, steamPoweredRequest, steamPoweredUrl} from './steamPoweredRequest';

async function retrieveLoggedInUser(http: HTTPClient): Promise<string> {
    const $ = await steamPoweredCheerioRequest('/nav_games.php', http);
    const username = $('#header_left span:contains("Signed in as") strong').text();
    if (username === '') {
        throw new LoginException(LoginExceptionType.UNKNOWN);
    }
    return username;
}

export async function checkSteamPoweredLogin(httpClient: HTTPClient) {
    const response = await steamPoweredRequest('/login', {httpClient, followRedirects: true});
    if (response.status === 200 && response.config.url && [`${steamPoweredUrl}/`, `${steamPoweredUrl}/nav_games.php`].includes(response.config.url)) {
        return true;
    }

    if (response.data && typeof response.data === 'string') {
        const $ = cheerio.load(response.data);
        const errorMessage = $('#error_display').text().trim();
        if (errorMessage === 'The account you logged in to does not have access to this site.') {
            throw new InsufficientPrivilegesLevelException(errorMessage);
        }
    }

    throw new LoginException(LoginExceptionType.SESSION_EXPIRED);
}

export const checkSteamPoweredSession: CheckSessionFunction<SteamParams, SteamSession> = async (context) => {
    return checkSteamSession(context, checkSteamPoweredLogin, retrieveLoggedInUser);
};
