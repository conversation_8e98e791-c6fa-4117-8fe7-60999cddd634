import {LogLevel} from '../../utils/logger';
import {CustomException} from '../CustomException';
import {errorTypes} from '../errorTypes';

export class InsufficientPrivilegesLevelException extends CustomException {
    constructor(message = "Account has insufficient privileges or you don't have any products available to download.") {
        super({
            message,
            suggestedAction: `Check the User Guide on how to set it up correctly: https://indiebi.com/user-guide`,
            telemetryLogLevel: LogLevel.WARN,
            errorType: errorTypes.MISSING_PERMISSIONS
        });
    }
}
