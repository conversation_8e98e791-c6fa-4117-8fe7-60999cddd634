import {readFileSync} from 'fs';
import * as nconf from 'nconf';
import {FileCrypter} from './FileCrypter';

class TestsConfigRepository {
    private static readonly DEFAULT_ENCRYPTED_CREDS_PATH = './test/vcr/test_cases/credentials_encrypted.json';
    private config: any;

    constructor(encryptedConfigFilePath?: string) {
        const content = readFileSync(encryptedConfigFilePath || TestsConfigRepository.DEFAULT_ENCRYPTED_CREDS_PATH).toString();
        const decryptedContent = new FileCrypter().decrypt(content);
        const configSettings = JSON.parse(decryptedContent);
        this.config = new nconf.Provider();
        this.config.argv().env().overrides(configSettings).load();
    }

    public get(key: string): any {
        return this.config.get(key);
    }
}

export const testConfigRepo = new TestsConfigRepository();
