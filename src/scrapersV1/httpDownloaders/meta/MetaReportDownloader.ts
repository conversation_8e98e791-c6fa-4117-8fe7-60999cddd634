import * as _ from 'lodash';
import * as moment from 'moment';
import {<PERSON>rowser} from '../../../browser/Browser';
import {Source} from '../../../dataTypes';
import {SourceIdentifier, SourceSideOrganization, defaultSourceSideOrganization} from '../../../dataTypes/SourceSideOrganization';
import {CustomException} from '../../../error/CustomException';
import {errorTypes} from '../../../error/errorTypes';
import {
    AccountIsNotVerifiedException,
    InsufficientPrivilegesLevelException,
    LoginException,
    LoginExceptionType,
    NoProductsToDownloadException,
    UnableToDownloadReportException
} from '../../../error/exceptions';
import {Invalid2FACode} from '../../../error/exceptions/Invalid2FACode';
import {TooManyIterationsException} from '../../../error/exceptions/TooManyIterationsException';
import {TooManyRequestsException} from '../../../error/exceptions/TooManyRequestsException';
import ManualLoginDetails from '../../../scrapersV2/ManualLoginDetails';
import {prepareZipReports} from '../../../scrapersV2/zip';
import {downloadForOrganizationFinished, foundMultipleOrganizations, startingDownloadForOrganization} from '../../../telemetry/events/multiorgEvents';
import {isManualSession} from '../../../utils/conditions';
import {FileExtension} from '../../../utils/files/FileExtension';
import {generateFileName, saveFileToDownloads} from '../../../utils/files/fileUtils';
import {log} from '../../../utils/logger';
import {DownloadReportResult} from '../../DownloadReportResult';
import {HttpReportDownloader} from '../../HttpReportDownloader';
import {LoginResult} from '../../LoginResult';
import {PrivilegesChecker} from '../../PrivilegesChecker';
import {Report} from '../../Report';
import {ReportDownloaderProperties} from '../../ReportDownloaderProperties';
import {validateDownloader} from '../validateDownloader';
import * as MetaConstants from './MetaConstants';
import {GraphQLPersistedQuery, nextButtonXpath} from './MetaConstants';
import {MetaApp, MetaLegacyDashboardRevenue, MetaRealTimeRevenue, MetaReportFile} from './MetaInterfaces';
import {convertLegacyDashboardJSONtoLegacyCSV} from './MetaLegacyDashboard';
import {convertJSONtoLegacyCSV} from './MetaLivetimeDashboard';

enum MetaPlatformType {
    PC = 'PC',
    ANDROID_6DOF = 'ANDROID_6DOF',
    ANDROID = 'ANDROID' // unused, but this is Oculus Go platform
}

class CouldNotFindProperCookie extends CustomException {
    constructor() {
        super({
            message: 'Could not find a proper Cookie.',
            suggestedAction: 'Please setup your scraper again.',
            canBeFixedByRetry: false,
            errorType: errorTypes.CONFIGURATION_ISSUE
        });
    }
}

export class MetaReportDownloader implements HttpReportDownloader, PrivilegesChecker {
    private readonly login: string;
    private readonly password: string;
    private readonly browser: Browser;
    // IMPORTANT! Due to how the higher-level cookie scraping mechanism is implemented, subplatform can be undefined (it is not passed
    // if we just scrap credentials). Since we cannot enforce disabling passing it to vulnerable functions via strict null checking yet
    // (too many changes required) we have to just remember that in this class this.subplatform can be undefined and lead to errors later
    // on (e.g. while converting to display name).
    private readonly source: Source;

    constructor({login, password, browser, source}: ReportDownloaderProperties) {
        //For the purpose of validation the source is irrelevant
        validateDownloader(Source.META_RIFT_SALES, {browser, source});
        this.login = login!;
        this.password = password!;
        this.browser = browser!;
        this.source = source!;
    }

    public static manualLoginDetails: ManualLoginDetails = {
        url: MetaConstants.dashboardUrl,
        successSelector: {
            value: MetaConstants.userSettingsAvailableXPathSelector,
            type: 'xpath'
        }
    };

    public async downloadReport(startDate: moment.Moment, endDate: moment.Moment): Promise<DownloadReportResult> {
        const loginResult = await this.performLogin();
        if (loginResult.isFailedLogin()) {
            return DownloadReportResult.createLoginErrorResult(loginResult);
        }
        log.info('Already logged in, proceeding to data scraping...');

        const salesReports = await this.completeReportDownload(startDate, endDate);
        return DownloadReportResult.createReportResult(salesReports);
    }

    /**
     * @param code - the user input
     * Returns void if the code is valid, otherwise throws an exception
     */
    public async fillSecondAuthenticationFactor(code: string): Promise<void> {
        log.info('Filling confirmation code...');

        await this.browser.clearAndType(MetaConstants.loginConfirmationInputSelector, code);
        await this.browser.clickXpath(nextButtonXpath);

        if (await this.isInvalidCodeEntered()) {
            log.info('Invalid confirmation code entered!');
            await this.browser.clickXpath(nextButtonXpath);

            throw new Invalid2FACode();
        }

        log.info('Confirmation code successfully entered!');
    }

    public get dualAuthMaxAttempts(): number {
        return MetaConstants.dualAuthMaxAttempts;
    }

    public async performLogin(): Promise<LoginResult> {
        log.info('Opening a dashboard page...');
        await this.browser.goto(MetaConstants.dashboardLoginUrl, MetaReportDownloader.isOnErrorPage);

        await this.forceEnglishLanguage();

        await this.handleCookieContentsIfNecessary();

        if (await this.isLoginButtonVisible()) {
            await this.browser.click(MetaConstants.logInButtonSelector);
        }

        if (await this.isLoggedIn()) {
            if (!(await this.isAccountVerified())) {
                return LoginResult.createInsufficientPrivilegeLevelErrorResult('Your account is not verified. Please log in to your Meta account and verify it.');
            }
            return LoginResult.createSuccessfulLoginResult();
        } else if (isManualSession(this.login, this.password)) {
            return LoginResult.createExpiredSessionResult();
        }

        await this.handleCookieContentsIfNecessary();

        const xPathSelectorForUseAnotherAccount = '//span[. = "Use another account"]';
        if (await this.browser.xPathElementExistsAfterTime(xPathSelectorForUseAnotherAccount)) {
            await this.browser.clickXpath(xPathSelectorForUseAnotherAccount);
        }

        await this.handleCookieContentsIfNecessary();

        const usernameRememberedPopupIsVisibleXpath = '//*[starts-with(text(), "Log into another account")]';
        const checkIfUsernameRememberedPopupIsVisible = await this.browser.xPathElementExistsAfterTime(usernameRememberedPopupIsVisibleXpath);
        if (checkIfUsernameRememberedPopupIsVisible) {
            await this.browser.clickXpath(usernameRememberedPopupIsVisibleXpath);
        }
        const selectEmailLoginXpath = '//*[starts-with(text(), "Continue with email")]';
        if (await this.browser.xPathElementExistsAfterTime(selectEmailLoginXpath)) {
            log.info('Continuing with email');
            await this.browser.clickXpath(selectEmailLoginXpath);
            await this.handleCookieContentsIfNecessary();
        }

        log.info('Typing email');
        await this.browser.type(MetaConstants.emailSelector, this.login);
        await this.browser.getHandleAndClick(MetaConstants.loginContinueButtonXPathSelector, 500, true);
        if (await this.browser.xPathElementExistsAfterTime(MetaConstants.passwordLoginXPathSelector)) {
            await this.browser.clickXpath(MetaConstants.passwordLoginXPathSelector);
        }
        if (await this.browser.elementExists(MetaConstants.passwordSelector)) {
            log.info('Typing password');
            await this.browser.type(MetaConstants.passwordSelector, this.password);
            await this.browser.getHandleAndClick(MetaConstants.loginButtonXPathSelector, 500, true);
        } else {
            return LoginResult.createAccountNotFoundResult();
        }

        if (await this.browser.xPathElementExistsAfterTime(MetaConstants.saveYourLoginInfoTextSelector)) {
            await this.browser.clickXpath('//span[.="Save"]');
        }

        if (await this.browser.xPathElementExistsAfterTime('//span[.="Confirm your Meta account"]')) {
            await this.browser.clickXpath(nextButtonXpath);

            return LoginResult.create2FactorAuthResult();
        }

        if (await this.hasLoginFailed()) {
            return LoginResult.createAuthenticationExceptionResult('Login failed. Invalid credentials or too many login attempts in a row.');
        }

        if (await this.isOnLoginConfirmationPage()) {
            log.info('User is required to handle two factor authentication.');
            return LoginResult.create2FactorAuthResult();
        }

        if (!(await this.isAccountVerified())) {
            return LoginResult.createInsufficientPrivilegeLevelErrorResult('Your account is not verified. Please log in to your Meta account and verify it.');
        }

        if (await this.isLoggedIn()) {
            log.info('Logged in...');
            return LoginResult.createSuccessfulLoginResult();
        }

        return LoginResult.createAuthenticationExceptionResult('Login failed. Invalid credentials or too many login attempts in a row.');
    }

    public async validatePrivileges(): Promise<void> {
        const organizations = await this.getUserOrganizations();
        if (organizations.length > 1) {
            return;
        }

        await this.browser.goto(MetaConstants.dashboardUrl, MetaReportDownloader.isOnErrorPage);
        if (!(await this.isAccountVerified())) {
            throw new AccountIsNotVerifiedException();
        }

        if (organizations.length == 0 || !(await this.organizationHasApps(organizations[0]))) {
            throw new InsufficientPrivilegesLevelException();
        }
    }

    private async organizationHasApps(organization: SourceSideOrganization): Promise<boolean> {
        await this.changeOrganization(organization);
        return this.browser.xPathElementExistsAfterTime('//div/div/span[contains(text(),"Approved")]');
    }

    public async checkSession(): Promise<SourceIdentifier> {
        log.info('Opening a dashboard page...');
        await this.browser.goto(MetaConstants.dashboardLoginUrl, MetaReportDownloader.isOnErrorPage);
        if (!(await this.isLoggedIn())) {
            throw new LoginException(LoginExceptionType.SESSION_EXPIRED);
        }

        const id = await this.getCurrentUsername();

        try {
            await this.validatePrivileges();
        } catch {
            return {
                hasScrapeBlockingIssues: true,
                id
            };
        }

        return {
            id
        };
    }

    public async completeReportDownload(startDate: moment.Moment, endDate: moment.Moment): Promise<Report[]> {
        const organizations = await this.getUserOrganizations();
        const reports: MetaReportFile[] = [];

        if (await this.hasScrapingLimitErrorOccurred()) {
            log.info(
                'The portal is currently unavailable due to too many requests. This situation may last a few hours. The scraper will try to get the data with the next attempt.'
            );
            throw new TooManyRequestsException();
        }

        for (const organization of organizations) {
            startingDownloadForOrganization(organization.name);
            await this.changeOrganization(organization);
            const apps: MetaApp[] = await this.getAvailableApps(organization.id, this.source);
            for (const app of apps) {
                const report = await this.downloadReportForApp(app, startDate, endDate);
                if (report) {
                    reports.push(report);
                }
            }
            downloadForOrganizationFinished(organization.name);
        }

        if (!reports.length) {
            throw new NoProductsToDownloadException();
        }

        return this.prepareReportsToUpload(reports, startDate, endDate);
    }

    private async getUserOrganizations(): Promise<SourceSideOrganization[]> {
        log.info("Examining users' organizations");
        const result = await this.makeGraphqlQuery({
            variables: {shouldQueryRouteParamOrg: false, organizationID: ''},
            doc_id: MetaConstants.GraphQLPersistedQuery.GET_ORGANIZATIONS
        });
        const userId: string | undefined = _.get(result, 'viewer.user.id');
        await this.forceEnglishLanguage(userId);
        const organizations = _.get<any, string, SourceSideOrganization[] | undefined>(result, 'viewer.user.organizations.nodes', undefined);

        if (!organizations) {
            throw new Error('Could not find any organizations');
        }

        log.info(`Found ${organizations.length} organizations: ${organizations.map((org: SourceSideOrganization) => org.name)}`);

        foundMultipleOrganizations(organizations.map((org: SourceSideOrganization) => org.name));

        return organizations;
    }

    private async changeOrganization(organization: SourceSideOrganization): Promise<void> {
        log.info(`Changing organization to ${organization.name}`);
        await this.makeGraphqlQuery({
            variables: {input: {client_mutation_id: '1', organization_id: organization.id}},
            doc_id: MetaConstants.GraphQLPersistedQuery.CHANGE_ORGANIZATION
        });
    }

    private async forceEnglishLanguage(userId?: string): Promise<void> {
        log.info('Making sure English language is chosen');
        if (userId) {
            await this.makeGraphqlQuery({
                variables: {
                    input: {
                        client_mutation_id: '2',
                        actor_id: userId,
                        locale: 'en_US'
                    }
                },
                doc_id: MetaConstants.GraphQLPersistedQuery.CHANGE_LANGUAGE_LOGGED_IN
            });
        } else {
            await this.browser.post('https://graph.oculus.com/graphql', {
                variables: {
                    input: {
                        client_mutation_id: '1',
                        actor_id: '0',
                        locale: 'en_US',
                        site_type: 'OCULUS'
                    }
                },
                doc_id: MetaConstants.GraphQLPersistedQuery.CHANGE_LANGUAGE,
                access_token: MetaConstants.GraphQLAccessToken.LOGGED_OUT
            });
        }
        await this.browser.reloadPage();
    }

    private readonly getAvailableApps = async (organizationId: string, source: Source): Promise<MetaApp[]> => {
        log.info('Getting list of available apps');

        const edges: any[] = [];
        const platform = MetaReportDownloader.sourceToMetaPlatformType(source);
        let hasNextPage = false;
        let endCursor = '';

        const data = await this.makeGraphqlQuery<any>({
            variables: {
                orderby: 'DISPLAY_NAME',
                orgID: organizationId,
                platform: platform
            },
            doc_id: MetaConstants.GraphQLPersistedQuery.GET_AVAILABLE_APPS
        });

        if (!data.organization) {
            throw new CustomException({
                message: 'Invalid information obtained from graphql. More often than not it is the result of changing the doc_id (on metas side).',
                canBeFixedByRetry: false,
                errorType: errorTypes.TEMPORARY_PORTAL_ISSUE,
                suggestedAction: 'Please try again later. If the issue persists, please contact support.'
            });
        }

        const {
            organization: {applications}
        } = data;

        log.info(
            `Got ${applications.edges.length} apps from the first page: ${applications.edges.map((app: any) => `${app.node.display_name} (${app.node.id})`).join(', ')}`
        );

        edges.push(...applications.edges);

        hasNextPage = applications.page_info?.has_next_page;
        endCursor = applications.page_info?.end_cursor;

        const cursorSize = applications.edges.length;

        let page = 0;
        const MAX_PAGE_SAFETY_LIMIT = 100; // it means 2000 apps (20 apps per page by default), should be enough
        while (hasNextPage && page++ < MAX_PAGE_SAFETY_LIMIT) {
            log.info(`Getting ${page + 1} page of apps...`);
            const {node} = await this.makeGraphqlQuery<any>({
                variables: {
                    orderby: 'DISPLAY_NAME',
                    id: organizationId,
                    platform: platform,
                    after: endCursor,
                    first: cursorSize
                },
                doc_id: MetaConstants.GraphQLPersistedQuery.GET_AVAILABLE_APPS_NEXT_PAGE
            });

            log.info(
                `Got ${node.applications.edges.length} apps from the ${page + 1} page: ${node.applications.edges
                    .map((app: any) => `${app.node.display_name} (${app.node.id})`)
                    .join(', ')}`
            );

            edges.push(...node.applications.edges);

            hasNextPage = node.applications.page_info?.has_next_page;
            endCursor = node.applications.page_info?.end_cursor;
        }

        if (page >= MAX_PAGE_SAFETY_LIMIT) {
            log.warning('Reached the maximum number of pages. There might be a problem with the scraper.');
            log.debug(`[${this.source.toUpperCase()}] Reached the maximum number of pages`, {organizationId, source});
            throw new TooManyIterationsException();
        }

        const apps = edges.map((entry) => ({
            id: entry.node.id,
            name: entry.node.display_name.trim(),
            is_test: entry.node.is_test,
            release_date: entry.node.release_date,
            source
        }));

        log.info(`Got ${apps.length} apps in total`);
        log.debug(`[${this.source.toUpperCase()}] Got list of products`, {apps});

        return apps;
    };

    private readonly getCurrentUsername = async (): Promise<string> => {
        log.info('Getting current user name');
        const {
            viewer: {
                user: {display_name}
            }
        } = await this.makeGraphqlQuery<any>({
            doc_id: GraphQLPersistedQuery.GET_CURRENT_USER
        });

        return display_name;
    };

    private static sourceToMetaPlatformType(source: Source): MetaPlatformType {
        // NOTE I know it looks strange but this is how Meta handles its platform types. Copied from the dashboard source code
        switch (source) {
            case Source.META_RIFT_SALES:
                return MetaPlatformType.PC;
            case Source.META_QUEST_SALES:
                return MetaPlatformType.ANDROID_6DOF;
            default:
                throw new Error(`Invalid source: ${source} for Meta scraper`);
        }
    }

    private async makeGraphqlQuery<T>(payload: Record<string, unknown>): Promise<T> {
        const cookies = await this.browser.cookies();
        const cookieWithAccessToken = _.find(cookies, {name: 'oc_ac_at'});
        if (!cookieWithAccessToken) {
            throw new CouldNotFindProperCookie();
        }
        const data = await this.browser.post<any>('https://graph.oculus.com/graphql', {
            ...payload,
            access_token: cookieWithAccessToken.value
        });
        return data.data;
    }

    private static async isOnErrorPage(browser: Browser): Promise<boolean> {
        const values = await Promise.all([
            browser.elementExists(MetaConstants.facebookLogoSelector),
            browser.elementExists(MetaConstants.facebookPageLinkSelector),
            browser.xPathElementExistsAfterTime(MetaConstants.errorPageTitleXPathSelector)
        ]);
        const result = values.some((value) => value);
        if (result) {
            // TODO verify if this still happens globally (verify telemetry after a week or so)
            log.debug('Meta error page detected');
        }
        return result;
    }

    private async downloadReportForApp(app: MetaApp, startDate: moment.Moment, endDate: moment.Moment): Promise<MetaReportFile | null> {
        if (!(await this.isAppEligibleForReporting(app))) {
            log.info(`App ${app.name} (${app.id}) does not have a lifetime revenue. Skipping...`);
            return null;
        }

        log.info(`Downloading report for product: ${app.name} (${app.id}).`);
        const fileName = generateFileName(this.source, startDate, endDate, FileExtension.CSV, app.name);
        log.info(`The file will be saved with name ${fileName}.`);
        let noSales = false;

        if (startDate.isSameOrAfter(moment(MetaConstants.legacyDashboardDateCutoff, MetaConstants.timeFormat))) {
            try {
                const content = await this.getRealTimeRevenueForApp(app, startDate, endDate);
                noSales = content?.node?.entitlements?.nodes?.length === 0;
                const csv = convertJSONtoLegacyCSV(content);
                await saveFileToDownloads(fileName, csv);
            } catch (error) {
                log.info('Report download failed!');
                throw new UnableToDownloadReportException(error);
            }
        } else {
            log.info(
                `Downloading report from Meta legacy dashboard, because start date is after ${
                    MetaConstants.legacyDashboardDateCutoff
                }, affected range: ${startDate.format(MetaConstants.timeFormat)} - ${endDate.format(MetaConstants.timeFormat)}`
            );
            try {
                const content = await this.getLegacyDashboardRevenueForApp(app, startDate, endDate);
                noSales = (await this.getLegacyDashboardUnitsSoldForApp(app, startDate, endDate)) === 0;
                const csv = convertLegacyDashboardJSONtoLegacyCSV(content);
                await saveFileToDownloads(fileName, csv);
            } catch (error) {
                log.info('Report download failed!');
                throw new UnableToDownloadReportException(error);
            }
        }
        log.info('Report download successful!');
        return {
            fileName,
            productName: app.name,
            productId: app.id,
            noSales
        };
    }

    private async getLegacyDashboardRevenueForApp(app: MetaApp, startDate: moment.Moment, endDate: moment.Moment): Promise<MetaLegacyDashboardRevenue> {
        return this.makeGraphqlQuery<MetaLegacyDashboardRevenue>({
            variables: {
                applicationID: app.id,
                dateRange: {
                    end_date: endDate.format(MetaConstants.timeFormat),
                    start_date: startDate.format(MetaConstants.timeFormat)
                },
                countryBreakdown: true,
                filterCountries: []
            },
            doc_id: MetaConstants.GraphQLPersistedQuery.GET_APP_REVENUE_LEGACY_DASHBOARD
        });
    }

    private async getLegacyDashboardUnitsSoldForApp(app: MetaApp, startDate: moment.Moment, endDate: moment.Moment): Promise<number> {
        const data = await this.makeGraphqlQuery<any>({
            variables: {
                applicationID: app.id,
                dateRange: {
                    end_date: endDate.format(MetaConstants.timeFormat),
                    start_date: startDate.format(MetaConstants.timeFormat)
                }
            },
            doc_id: MetaConstants.GraphQLPersistedQuery.GET_APP_UNITS_SOLD_LEGACY_DASHBOARD
        });
        return data?.app_store_item?.revenue_analytics_data?.app_copies_sold?.total_copies_sold ?? 0;
    }

    private async getRealTimeRevenueForApp(app: MetaApp, startDate: moment.Moment, endDate: moment.Moment): Promise<MetaRealTimeRevenue> {
        return this.makeGraphqlQuery<MetaRealTimeRevenue>({
            variables: {
                applicationID: app.id,
                end_time: endDate.format(MetaConstants.timeFormat),
                start_time: startDate.format(MetaConstants.timeFormat),
                revenueGroupByDimensions: ['COUNTRY_CODE'],
                entitlementsGroupByDimensions: ['COUNTRY_CODE'],
                filter_by_country_code: [],
                filter_by_entity_sub_type: ['APP'],
                filter_by_grant_reason: ['PAID_OFFER'],
                timeOverTimeEndTime: endDate.format(MetaConstants.timeFormat)
            },
            doc_id: MetaConstants.GraphQLPersistedQuery.GET_APP_REAL_TIME
        });
    }

    private async prepareReportsToUpload(reports: MetaReportFile[], startDate: moment.Moment, endDate: moment.Moment): Promise<Report[]> {
        log.debug('Preparing reports for upload.');
        if (!reports || _.isEmpty(reports)) {
            return [];
        }
        const noSalesInAllReports = reports.every((report) => report.noSales);
        const filenames = reports.map((report) => report.fileName);
        log.info('Preparing manifest file');
        const metadata = {
            fileMetaData: reports.reduce(
                (acc, report) => ({
                    ...acc,
                    [report.fileName]: {
                        skuId: report.productId,
                        humanName: report.productName,
                        platform: this.getPlatform()
                    }
                }),
                {} as Record<string, string>
            )
        };

        return prepareZipReports(startDate, endDate, filenames, this.source, log.info, metadata, noSalesInAllReports);
    }

    private getPlatform(): string {
        return this.source === Source.META_RIFT_SALES ? 'rift' : 'quest';
    }

    private async isLoginButtonVisible(): Promise<boolean> {
        return this.browser.elementExists(MetaConstants.logInButtonSelector);
    }

    private async isLoggedIn(): Promise<boolean> {
        log.info('Checking if user is required to log in');
        return this.browser.xPathElementExistsAfterTime(MetaConstants.userSettingsAvailableXPathSelector);
    }

    private async isAccountVerified(): Promise<boolean> {
        log.info('Checking if an account is verified...');
        const {viewer} = await this.makeGraphqlQuery<any>({
            doc_id: GraphQLPersistedQuery.GET_USER_METADATA
        });

        return viewer.user.is_verified;
    }

    private async isOnLoginConfirmationPage(): Promise<boolean> {
        log.info('Checking for two factor authentication...');
        return this.browser.xPathElementExistsAfterTime(MetaConstants.loginConfirmationFormXPathSelector);
    }

    private async hasLoginFailed(): Promise<boolean> {
        log.info('Looking for authentication error...');
        return this.browser.xPathElementExistsAfterTime(MetaConstants.loginErrorXPathSelector);
    }

    private async isInvalidCodeEntered(): Promise<boolean> {
        const incorrectLoginCode = this.browser.xPathElementExistsAfterTime('//span[contains(.,"Incorrect Login Code")]');
        const nonNumbersEntered = this.browser.xPathElementExistsAfterTime('//span[contains(.,"You can only include numbers in this field.")]');
        const notEnoughDigitsEntered = this.browser.xPathElementExistsAfterTime('//span[contains(.,"The confirmation code should have 6 digits.")]');
        return Promise.race([incorrectLoginCode, nonNumbersEntered, notEnoughDigitsEntered]).catch(() => false);
    }

    private async isAppEligibleForReporting(app: MetaApp): Promise<boolean> {
        log.debug(`Checking if app ${app.name} (${app.id}) is eligible for reporting`);

        const data = await this.makeGraphqlQuery<any>({
            variables: {
                applicationID: app.id
            },
            doc_id: MetaConstants.GraphQLPersistedQuery.GET_APP_LIFETIME_REVENUE
        });

        return data?.app_store_item?.overview_analytics_lifetime_data?.lifetime_revenue;
    }

    private async hasScrapingLimitErrorOccurred(): Promise<boolean> {
        return !(await this.browser.xPathElementExistsAfterTime(MetaConstants.accessibleAppsXPathSelector));
    }

    private async handleCookieContentsIfNecessary(): Promise<void> {
        log.info('Checking if there is a cookie consent popup');
        await Promise.all([this.handleEssentialCookiesPopup(), this.handleCookieConsentPopup(), this.handleAllowAllCookiesConsentPopup()]);
    }

    private async handleEssentialCookiesPopup() {
        if (await this.browser.xPathElementExistsAfterTime(MetaConstants.essentialCookiesAcceptButtonXPathSelector)) {
            log.info('Accepting essential cookies popup');
            await this.browser.getHandleAndClick(MetaConstants.essentialCookiesAcceptButtonXPathSelector, MetaConstants.cookiesButtonTimeout, true);
            await this.browser.waitForNavigation();
        }
    }

    private async handleCookieConsentPopup() {
        if (await this.browser.xPathElementExistsAfterTime(MetaConstants.cookiesAcceptButtonXPathSelector)) {
            log.info('Accepting cookies');
            await this.browser.getHandleAndClick(MetaConstants.cookiesAcceptButtonXPathSelector, MetaConstants.cookiesButtonTimeout, true);
            // NOTE It looks strange but Meta automatically refreshes dashboard when you click "Accept cookies"
            await this.browser.waitForNavigation();
        }
    }

    private async handleAllowAllCookiesConsentPopup() {
        log.info('Allow all cookies popup');
        if (await this.browser.xPathElementExistsAfterTime(MetaConstants.allowAllCookiesButton)) {
            await this.browser.clickAllXPathMatchingSelector(MetaConstants.allowAllCookiesButton);
        }
    }

    public async getSourceSideOrganizations(): Promise<SourceSideOrganization[]> {
        await this.browser.goto(MetaConstants.dashboardLoginUrl, MetaReportDownloader.isOnErrorPage);
        const orgs = await this.getUserOrganizations();
        return orgs.length ? orgs : [defaultSourceSideOrganization];
    }
}
