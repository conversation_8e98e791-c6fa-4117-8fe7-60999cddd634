import * as fse from 'fs-extra';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, BrowserV2, isValidBrowserSession} from '../browserV2';
import {browserEmergencyCloseHandler, dumpHAR, httpClientEmergencyCloseHandler} from '../browserV2/debugHelpers';
import {printTrace, printTraceWithError} from '../cli/messaging';
import {getDumpDirPath} from '../config/ConfigService';
import {HTTPClient} from '../utils/http';
import {log} from '../utils/logger';
import {ProgressCallback} from './ProgressCallback';
import {FeatureFlag, ScraperParams, Session} from './Scraper';

export class ScraperContext<T extends ScraperParams = ScraperParams, S extends Session = Session> {
    // NOTE Do not use "this" in functions that are not arrow functions.
    //
    // ScraperContext is often destructured when given as a parameter to scraper functions.
    // This means that functions can be returned unbound from the rest of the class, meaning you cannot use "this" inside the functions.
    // To make sure that `this` always works as expected, use arrow functions.
    private browser?: BrowserV2;

    private httpClient?: HTTPClient;

    constructor(public readonly progress: ProgressCallback, public readonly params: T, public session: S, private proxyUrl?: string) {}

    public getHttpClient = (): HTTPClient => {
        if (!this.httpClient) {
            this.httpClient = new HTTPClient(isValidBrowserSession(this.session) ? this.session : undefined, this.proxyUrl);
        }

        return this.httpClient;
    };
    public getPage = async (): Promise<BrowserPage> => {
        return (await this.getBrowser()).page;
    };

    private getBrowser = async (): Promise<BrowserV2> => {
        if (!this.browser) {
            this.browser = await BrowserV2.launch(this.session);
        }
        return this.browser;
    };

    public isFeatureEnabled = (feature: FeatureFlag): boolean => {
        return !!this.params.featureFlags?.length && this.params.featureFlags.includes(feature);
    };

    public updateSession = (session: S) => {
        this.session = session;
        if (this.httpClient && isValidBrowserSession(session)) {
            this.httpClient.overrideCookies(session.cookies);
        }
    };

    public close = async () => {
        if (this.browser) {
            await this.browser.close();
            delete this.browser;
        }
    };

    public emergencyClose = async () => {
        this.progress('Emergency close');
        const dumpDirPath = getDumpDirPath();

        if (dumpDirPath) {
            try {
                await fse.ensureDir(dumpDirPath);
            } catch (error) {
                printTrace(`Error while creating dump directory: ${dumpDirPath}`);
                printTraceWithError(error);
            }

            if (this.browser) {
                await browserEmergencyCloseHandler(this.browser.page.page, dumpDirPath);
                this.progress('Browser dump created');
            }

            if (this.httpClient) {
                const cache = this.httpClient.getDebugCache();
                if (cache) {
                    await httpClientEmergencyCloseHandler(cache, dumpDirPath);
                    this.progress('HTTP client dump created');
                }

                if (this.isFeatureEnabled('full-history-dump')) {
                    const har = this.httpClient.getHAR();
                    if (har) {
                        await dumpHAR(har, dumpDirPath);
                    }
                }
            }
        } else if (this.isFeatureEnabled('full-history-dump')) {
            log.warning('Full history dump enabled, but dump directory not set');
        }

        await this.close();
    };
}
