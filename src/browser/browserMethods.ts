import {TimeoutError} from 'puppeteer';
import {El<PERSON><PERSON><PERSON><PERSON>, Frame, PuppeteerPage} from '../puppeteer';
import {<PERSON><PERSON><PERSON>} from './Browser';
import {IFrame} from './IFrame';

const browseWaitForFunctionError = 'waitForFunction failed';

export const isHeadlessBrowser = (userAgent: string): boolean => userAgent.includes('HeadlessChrome');

/** @deprecated */
export const elementExists = async (browserOrIFrame: Browser | IFrame, selector: string, timeout: number, visible = true): Promise<boolean> => {
    try {
        await browserOrIFrame.waitForSelector(selector, timeout, visible);
        return true;
    } catch (error) {
        if (error instanceof TimeoutError || error.toString().includes(browseWaitForFunctionError)) {
            return false;
        }
        throw error;
    }
};

/** @deprecated */
export const xPathElementExistsAfterTime = async (pageOrFrame: Frame | PuppeteerPage, xPathSelector: string, timeout: number): Promise<boolean> => {
    try {
        await pageOrFrame.waitForSelector(`::-p-xpath(${xPathSelector})`, {timeout});
        return true;
    } catch (error) {
        if (error instanceof TimeoutError || error.toString().includes(browseWaitForFunctionError)) {
            return false;
        }
        throw error;
    }
};

/** @deprecated */
export const querySelector = async (pageOrFrame: Frame | PuppeteerPage, selector: string): Promise<ElementHandle | null> => pageOrFrame.$(selector);

/** @deprecated */
export const type = async (browserOrIFrame: Browser | IFrame, pageOrFrame: Frame | PuppeteerPage, selector: string, input: string): Promise<void> => {
    await browserOrIFrame.waitForSelector(selector);
    return pageOrFrame.type(selector, input);
};

/** @deprecated */
export const clearAndType = async (browserOrIFrame: Browser | IFrame, pageOrFrame: Frame | PuppeteerPage, selector: string, input: string): Promise<void> => {
    await pageOrFrame.click(selector, {clickCount: 3});
    return browserOrIFrame.type(selector, input);
};

/** @deprecated */
export const click = async (browserOrIFrame: Browser | IFrame, pageOrFrame: Frame | PuppeteerPage, selector: string): Promise<void> => {
    await browserOrIFrame.waitForSelector(selector);
    return pageOrFrame.click(selector);
};

export interface WaitForAnyOptions {
    cssSelectors?: string[];
    xPaths?: string[];
    timeout?: number;
    visible?: boolean;
}

export type SelectorMatch = [string, ElementHandle];

export async function waitForAnySelector(
    {cssSelectors = [], xPaths = [], timeout = Browser.defaultSelectorTimeout, visible = true}: WaitForAnyOptions,
    page: PuppeteerPage
): Promise<SelectorMatch> {
    const selectorTasks = cssSelectors.map(
        async (selector) =>
            <SelectorMatch>[
                selector,
                await page.waitForSelector(selector, {
                    timeout,
                    visible
                })
            ]
    );
    const xPathTasks = xPaths.map(
        async (xPath) =>
            <SelectorMatch>[
                xPath,
                await page.waitForSelector(`::-p-xpath(${xPath})`, {
                    timeout,
                    visible
                })
            ]
    );

    return Promise.race([...selectorTasks, ...xPathTasks]);
}
