import {adaCase, camelCase, capitalCase, cobolCase, constantCase, dotNotation, kebabCase, lowerCase, pascalCase, snakeCase, trainCase, upperCase} from 'case-anything';
import * as stringify from 'json-stringify-safe';

/**
 * Any changes here should also be made in ScraperLib and Scrapers-py!!
 * This list will be converted to various cases and used to filter sensitive fields.
 */
const sensitiveFields = [
    'credentials',
    'apiToken',
    'password',
    'apiSetupData',
    'clientId',
    'tenantId',
    'totpSecret',
    'cloudStorageBucket',
    'token',
    'cookie',
    'set-cookie',
    'cookies',
    'clientSecret',
    'accessToken',
    'xsrfCookieName'
];

const isObject = (value: any): boolean => {
    return value !== null && typeof value === 'object' && !Array.isArray(value);
};

const isPropertyNameSensitive = (propertyName: string): boolean => {
    return sensitiveFieldsArray.includes(propertyName);
};

// https://levelup.gitconnected.com/beware-of-using-json-stringify-for-logging-933f18626d51
const replaceNonEnumerableProperties = (_key: string, value: any) => {
    if (value instanceof Error) {
        return Object.getOwnPropertyNames(value).reduce(
            (obj, propName) => {
                obj[propName] = value[propName];
                return obj;
            },
            {name: value.name}
        );
    }
    return value;
};

const stringifyCircularObject = (obj: any): string => {
    return stringify(obj, replaceNonEnumerableProperties);
};

const transformations = [
    (field) => field,
    lowerCase,
    upperCase,
    kebabCase,
    snakeCase,
    pascalCase,
    camelCase,
    constantCase,
    trainCase,
    adaCase,
    cobolCase,
    dotNotation,
    capitalCase
];

const sensitiveFieldsArray = sensitiveFields.flatMap((field) => transformations.map((transform) => transform(field)));

export const stringifyAndFilterSensitiveFields = (obj: any): string => {
    return stringifyCircularObject(filterSensitiveFields(obj));
};

export const filterSensitiveFields = <T>(input?: T): T => {
    if (!input) {
        return {} as T;
    }

    // get rid of circular references which might exists in some error objects like AxiosError
    const redacted = JSON.parse(stringifyCircularObject(input));

    for (const property in redacted) {
        if (isPropertyNameSensitive(property)) {
            delete redacted[property];
        }
        if (Array.isArray(redacted[property])) {
            redacted[property] = redacted[property].map(filterSensitiveFields);
        } else if (isObject(redacted[property])) {
            redacted[property] = filterSensitiveFields(redacted[property]);
        }
    }

    return redacted;
};
