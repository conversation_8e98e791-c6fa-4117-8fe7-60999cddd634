import {expect} from 'chai';
import * as moment from 'moment';
import apiTimeFormatter from '../../../src/utils/dates/apiTimeFormatter';

describe('apiTimeFormatter should', () => {
    const expectedResultDateRegex = /^2020-01-01T[0-9]{2}:[0-9]{2}:[0-9]{2}\.000Z/;

    it('return string date in specific format when input is not in utc', () => {
        expect(apiTimeFormatter(moment('2020-01-01'))).to.match(expectedResultDateRegex);
    });

    ['2020-01-01T23:59:00.000+12:00', '2020-01-01T00:23:59.000-12:00', '2020-01-01T00:00:00.000-11:00'].forEach((date) => {
        it('return string date in specific format when input is not in utc AAA', () => {
            expect(apiTimeFormatter(moment(date))).to.match(expectedResultDateRegex);
        });
    });
});
