{"calls": {"https://partner.steampowered.com/login": [{"body": {"data": "https_partner.steampowered.com/login", "encoding": "file"}, "headers": {"content-security-policy": "default-src blob: data: https: 'unsafe-inline' 'unsafe-eval'; connect-src 'self' https://community.akamai.steamstatic.com/ https://store.steampowered.com/ wss://community.steam-api.com/websocket/ https://api.steampowered.com/ https://login.steampowered.com/ https://help.steampowered.com/ https://*.valvesoftware.com https://*.steambeta.net https://*.steamcontent.com; report-uri /actions/CSPReport"}}, {"status": 302, "req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561198803773815%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.5kTY0H70cIF6Nwc9Svl77ufsMTEZw8fG4RuRlzw68VAB2T0xyfG_uW2QrO69qpzNwXs0X461zxX4rzezsGX8CA; sessionid=94711948950ebfd6238c9442;"}, "headers": {"content-security-policy": "default-src blob: data: https: 'unsafe-inline' 'unsafe-eval'; connect-src 'self' https://community.akamai.steamstatic.com/ https://store.steampowered.com/ wss://community.steam-api.com/websocket/ https://api.steampowered.com/ https://login.steampowered.com/ https://help.steampowered.com/ https://*.valvesoftware.com https://*.steambeta.net https://*.steamcontent.com; report-uri /actions/CSPReport", "location": "https://partner.steampowered.com/", "content-encoding": "", "vary": ""}}], "https://scraper-api.indiebi.dev/1/auth-code/steam_sales": [{"req_headers": {"authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************.Ok_Yp2boUYwE_hZAnFmzGkM6ogm1HgsOU2gn-4tKIdU", "host": "scraper-api.indiebi.dev", "connection": "keep-alive"}, "body": "{\"authCode\":null}", "headers": {"content-type": "application/json; charset=utf-8", "connection": "keep-alive", "request-context": "appId=cid-v1:1ae09687-f2ab-424c-b8e7-c482afdc19c9", "x-powered-by": "Express", "vary": "Origin", "access-control-allow-credentials": "true", "etag": "W/\"11-CSaGiLTQJ36vkT+ep4rzOLCKeXw\"", "strict-transport-security": "max-age=15724800; includeSubDomains", "server": "", "content-security-policy": "", "content-encoding": ""}}, {"req_headers": {"authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************.Ok_Yp2boUYwE_hZAnFmzGkM6ogm1HgsOU2gn-4tKIdU", "host": "scraper-api.indiebi.dev", "connection": "keep-alive"}, "body": "{\"authCode\":\"2K6G9\"}", "headers": {"content-type": "application/json; charset=utf-8", "connection": "keep-alive", "request-context": "appId=cid-v1:1ae09687-f2ab-424c-b8e7-c482afdc19c9", "x-powered-by": "Express", "vary": "Origin", "access-control-allow-credentials": "true", "etag": "W/\"14-TQGU0zqVSL0gzCkklbno5OOTy2g\"", "strict-transport-security": "max-age=15724800; includeSubDomains", "server": "", "content-security-policy": "", "content-encoding": ""}}], "https://partner.steampowered.com/": [{"req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561198803773815%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.5kTY0H70cIF6Nwc9Svl77ufsMTEZw8fG4RuRlzw68VAB2T0xyfG_uW2QrO69qpzNwXs0X461zxX4rzezsGX8CA; sessionid=94711948950ebfd6238c9442;"}, "body": {"data": "https_partner.steampowered.com/index.html", "encoding": "file"}, "headers": {"set-cookie": "dateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:39:05 GMT; Max-Age=10800; path=/, dateEnd=2023-08-08; expires=Tue, 08-Aug-2023 15:39:05 GMT; Max-Age=10800; path=/, priorDateStart=2023-08-07; expires=Tue, 08-Aug-2023 15:39:05 GMT; Max-Age=10800; path=/, priorDateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:39:05 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}, {"req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561198803773815%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.AuAY3AMhZh1pzBE2mqBSYQxdd-z0SHzSL07STra4gw-ZzZs4fpHx75oMDPoIcYe6W275uFZ0nT019igJO7skAg; dateStart=2023-08-08; steamworksRunas=3204;"}, "body": {"data": "https_partner.steampowered.com/index_1044f1a6fd5cccb0ea30bdfc2247273c.html", "encoding": "file"}, "headers": {"set-cookie": "dateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:39:08 GMT; Max-Age=10800; path=/, dateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:39:08 GMT; Max-Age=10800; path=/, priorDateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:39:08 GMT; Max-Age=10800; path=/, priorDateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:39:08 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/nav_games.php": [{"req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561198803773815%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.5kTY0H70cIF6Nwc9Svl77ufsMTEZw8fG4RuRlzw68VAB2T0xyfG_uW2QrO69qpzNwXs0X461zxX4rzezsGX8CA; sessionid=94711948950ebfd6238c9442; dateStart=2023-08-08;"}, "body": {"data": "https_partner.steampowered.com/nav_games.php", "encoding": "file"}, "headers": {"set-cookie": "dateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:39:06 GMT; Max-Age=10800; path=/, dateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:39:06 GMT; Max-Age=10800; path=/, priorDateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:39:06 GMT; Max-Age=10800; path=/, priorDateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:39:06 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}, {"req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561198803773815%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.AuAY3AMhZh1pzBE2mqBSYQxdd-z0SHzSL07STra4gw-ZzZs4fpHx75oMDPoIcYe6W275uFZ0nT019igJO7skAg;"}, "body": {"data": "https_partner.steampowered.com/nav_games_89b9384cf1f2e6e5b2259b75216edf37.php", "encoding": "file"}, "headers": {"set-cookie": "dateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:39:08 GMT; Max-Age=10800; path=/, dateEnd=2023-08-08; expires=Tue, 08-Aug-2023 15:39:08 GMT; Max-Age=10800; path=/, priorDateStart=2023-08-07; expires=Tue, 08-Aug-2023 15:39:08 GMT; Max-Age=10800; path=/, priorDateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:39:08 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/dir.php": [{"req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561198803773815%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.AuAY3AMhZh1pzBE2mqBSYQxdd-z0SHzSL07STra4gw-ZzZs4fpHx75oMDPoIcYe6W275uFZ0nT019igJO7skAg; dateStart=2023-08-08; steamworksRunas=3204;"}, "body": {"data": "https_partner.steampowered.com/dir.php", "encoding": "file"}, "headers": {"set-cookie": "dateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:39:10 GMT; Max-Age=10800; path=/, dateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:39:10 GMT; Max-Age=10800; path=/, priorDateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:39:10 GMT; Max-Age=10800; path=/, priorDateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:39:10 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=50666": [{"req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561198803773815%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.AuAY3AMhZh1pzBE2mqBSYQxdd-z0SHzSL07STra4gw-ZzZs4fpHx75oMDPoIcYe6W275uFZ0nT019igJO7skAg; dateStart=2023-08-08; steamworksRunas=3204;"}, "body": {"data": "https_partner.steampowered.com/report_csv.php", "encoding": "file"}, "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\"", "content-encoding": "", "vary": ""}}], "https://partner.steampowered.com/package/details/50666/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561198803773815%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.AuAY3AMhZh1pzBE2mqBSYQxdd-z0SHzSL07STra4gw-ZzZs4fpHx75oMDPoIcYe6W275uFZ0nT019igJO7skAg; dateStart=2023-08-08; steamworksRunas=3204;"}, "body": {"data": "https_partner.steampowered.com/package_details_50666_", "encoding": "file"}, "headers": {"set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:39:10 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:39:10 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:39:10 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:39:10 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/package/details/165761/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_165761_", "encoding": "file"}, "headers": {"set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:39:11 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:39:11 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:39:11 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:39:11 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=165761": [{"body": {"data": "https_partner.steampowered.com/report_csv_b730ba34fee257ece2e37dd02ebc78c5.php", "encoding": "file"}, "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\"", "transfer-encoding": "chunked", "connection": "close, Transfer-Encoding", "content-encoding": "", "vary": ""}}], "https://partner.steampowered.com/package/details/197766/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_197766_", "encoding": "file"}, "headers": {"set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:39:12 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:39:12 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:39:12 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:39:12 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=197766": [{"body": {"data": "https_partner.steampowered.com/report_csv_9762206d58f44186e3f0f1b88cb8563f.php", "encoding": "file"}, "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\"", "transfer-encoding": "chunked", "connection": "close, Transfer-Encoding", "content-encoding": "", "vary": ""}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=894193": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\"", "content-encoding": "", "vary": ""}}], "https://partner.steampowered.com/package/details/894193/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_894193_", "encoding": "file"}, "headers": {"set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:39:13 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:39:13 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:39:13 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:39:13 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=886309": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\"", "content-encoding": "", "vary": ""}}], "https://partner.steampowered.com/package/details/886309/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_886309_", "encoding": "file"}, "headers": {"set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:39:13 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:39:13 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:39:13 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:39:13 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=894194": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\"", "content-encoding": "", "vary": ""}}], "https://partner.steampowered.com/package/details/894194/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_894194_", "encoding": "file"}, "headers": {"set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:39:18 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:39:18 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:39:18 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:39:18 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter%5EpkgID=894195": [{"body": "sep=,\nSteam Sales data for SUPERHOT Sp. z o.o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\"", "content-encoding": "", "vary": ""}}], "https://partner.steampowered.com/package/details/894195/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"body": {"data": "https_partner.steampowered.com/package_details_894195_", "encoding": "file"}, "headers": {"set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:39:19 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:39:19 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:39:19 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:39:19 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}]}, "defaults": {"request_headers": {"accept": "application/json, text/plain, */*", "cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561198803773815%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.AuAY3AMhZh1pzBE2mqBSYQxdd-z0SHzSL07STra4gw-ZzZs4fpHx75oMDPoIcYe6W275uFZ0nT019igJO7skAg; dateStart=2021-11-01; steamworksRunas=3204;", "user-agent": "axios/0.27.2", "host": "partner.steampowered.com", "connection": "close"}, "headers": {"server": "nginx", "content-type": "text/html; charset=UTF-8", "content-security-policy": "script-src 'self' 'unsafe-inline' 'unsafe-eval'; object-src 'none'; connect-src 'self'; child-src 'self'; report-uri /actions/cspreport", "content-encoding": "gzip", "vary": "Accept-Encoding", "connection": "close"}}}