import {join} from 'path';
import * as adm from 'adm-zip';
import {expect} from 'chai';
import * as fse from 'fs-extra';
import * as moment from 'moment';
import {downloadDirPath} from '../../../src/config/ConfigService';
import {Source} from '../../../src/dataTypes';
import {FileExtension} from '../../../src/utils/files/FileExtension';
import {generateFileName} from '../../../src/utils/files/fileUtils';
import {createCheckSessionTest} from '../checkSessionTestGenerator';
import {createGetSourceSideOrgTest} from '../getSourceSideOrganizationTestGenerator';
import {createScrapeTest} from '../scrapeTestGenerator';
import {LOGIN_ARGS, getScrapeArgs, getScrapeArgsUtc} from '../utils/ArgsProvider';
import {listZipContentNames, removeIfFilesExist, runCliMainWithConvertedArgs} from '../utils/Utils';

describe.skip('Epic runner:', () => {
    const testOrganizationSlug = 'superhot-sp-z-o-o';
    const defaultStartDateString = '2020-01-01';
    const defaultEndDateString = '2020-02-01';
    const expectedHistoricalCSVName = generateFileName(
        Source.EPIC_SALES,
        moment.utc(defaultStartDateString),
        moment.utc(defaultEndDateString),
        FileExtension.CSV,
        testOrganizationSlug
    );
    const extractedHistoricalCSVFilePath = join(downloadDirPath(), expectedHistoricalCSVName);

    const recentStartDate = moment.utc().subtract(9, 'days');
    const recentEndDate = moment.utc().subtract(2, 'days');
    const expectedRecentCSVName = generateFileName(Source.EPIC_SALES, recentStartDate, recentEndDate, FileExtension.CSV, testOrganizationSlug);
    const extractedRecentCSVFilePath = join(downloadDirPath(), expectedRecentCSVName);

    const loginArgs = LOGIN_ARGS.for_epic;
    loginArgs.credentials = {...loginArgs.credentials, loginWith: 'steam'};
    const historicalScrapeArgs = getScrapeArgs(loginArgs, defaultStartDateString, defaultEndDateString, FileExtension.ZIP);
    const recentScrapeArgs = getScrapeArgsUtc(loginArgs, recentStartDate, recentEndDate, FileExtension.ZIP);

    const cleanFiles = async () =>
        await removeIfFilesExist(historicalScrapeArgs.testedFilePath!, extractedHistoricalCSVFilePath, recentScrapeArgs.testedFilePath!, extractedRecentCSVFilePath);

    before(async () => {
        await cleanFiles();
        await runCliMainWithConvertedArgs(loginArgs);
    });

    createCheckSessionTest(loginArgs, {id: 'supersuperdata', hasScrapeBlockingIssues: false}, true);

    createGetSourceSideOrgTest(loginArgs, [{name: 'SUPERHOT Sp. z.o.o', id: 'o-yagrrjhn9gf4ul3h4a5vuaxh29v8qd', hasScrapeBlockingIssues: undefined}], true);

    it('should download EPIC csv file with historical data', async () => {
        await runCliMainWithConvertedArgs(historicalScrapeArgs);

        expect(listZipContentNames(historicalScrapeArgs.testedFilePath!)).to.be.deep.equal([expectedHistoricalCSVName, 'manifest.json']);

        const zip = new adm(historicalScrapeArgs.testedFilePath);
        await zip.extractEntryTo(expectedHistoricalCSVName, downloadDirPath());
        const expectedEntries = 78;
        const content = (await fse.readFile(extractedHistoricalCSVFilePath, 'utf-8')).split('\n');
        expect(content).to.have.lengthOf(expectedEntries);
    });

    createScrapeTest('should download EPIC csv file with recent data', recentScrapeArgs, [expectedRecentCSVName]);
});
