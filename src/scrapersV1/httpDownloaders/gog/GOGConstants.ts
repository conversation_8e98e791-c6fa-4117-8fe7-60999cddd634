import {Moment} from 'moment';

export const mailCodeInputSelector = '#second_step_authentication_token_letter_1';
export const mailCodeConfirmationButtonSelector = '#second_step_authentication_send';
export const loginPopupButton = '#login';
export const usernameInputSelector = '#login_username';
export const passwordInputSelector = '#login_password';
export const loginButtonSelector = '#login_login';
export const logoutButton = 'a[href="https://partners.gog.com/logout"]';
export const incorrectPasswordXPathSelector = 'li.field--error > span.field__msg';
export const loginCaptchaSelector = 'form[name="login"] .g-recaptcha';
export const loginIFrameSelector = '#GalaxyAccountsFrame';
export const hasPermissionSelector = 'a[href="https://partners.gog.com/charts/sales-summary"]';
export const salesSummaryReportPageUrl = 'https://partners.gog.com/charts/sales-summary';
export const countrySalesReportPage = 'https://partners.gog.com/charts/country-sales';
export const dateFormat = 'YYYY-MM-DD';
export const gogWaitTimeout = 10000;
export const secondFactorAuthenticationCodeLength = 4;
export const dualAuthMaxAttempts = 5; // NOT confirmed. TODO: confirm.

export interface Data {
    rows: (string | number)[][];
    cols: {
        display_name: string;
        source: string;
        field_ref: (string | {'base-type': string})[];
        name: string;
        base_type: string;
        effective_type: string;
    }[];
    insights: {
        'previous-value': number;
        unit: string;
        offset: number;
        'last-change': number;
        col: string;
        slope: number;
        'last-value': number;
        'best-fit': (string | number)[];
    }[];
    requested_timezone: string;
    results_timezone: string;
}

export interface JsonQuery {
    parameters: {
        type: string;
        slug: string;
        id: string;
        value?: string[] | number[];
        target: (string | {'base-type': string})[];
    }[];
}

export interface DashboardQueryResult {
    data: Data;
    json_query: JsonQuery;
    status: string;
}

export interface DashCard {
    size_x: number;
    dashboard_tab_id: null;
    series: any[];
    card: {
        id: number;
        name: string;
        description: null;
        display: string;
        visualization_settings: any;
        parameters: any[];
        dataset_query: any;
    };
    col: number;
    id: number;
    parameter_mappings: any[];
    card_id: number;
    visualization_settings: any;
    size_y: number;
    dashboard_id: number;
    row: number;
}

export interface Parameter {
    name: string;
    slug: string;
    id: string;
    type: string;
    sectionId: string;
    default?: string;
    required?: boolean;
    values_query_type?: string;
    values_source_type?: string;
    values_source_config?: {
        values: string[];
    };
    filteringParameters?: string[];
}

export interface DashboardMetadata {
    description: null;
    dashcards: DashCard[];
    param_values: any;
    tabs: any[];
    name: string;
    width: string;
    id: number;
    param_fields: any;
    parameters: Parameter[];
    auto_apply_filters: boolean;
}

export interface ProductsResponse {
    has_more_values: boolean;
    values: string[];
}

export function getDashboardMetadataQueryUrl(jwt: string) {
    return `https://metabase-public.gog.com/api/embed/dashboard/${jwt}`;
}

export function getProductsListQueryUrl(jwt: string, parameter: Parameter) {
    return `https://metabase-public.gog.com/api/embed/dashboard/${jwt}/params/${parameter.id}/values`;
}

export function getSalesDataQueryUrl(jwt: string, dashcard: DashCard, startDate: Moment, endDate: Moment, product: string) {
    return `https://metabase-public.gog.com/api/embed/dashboard/${jwt}/dashcard/${dashcard.id}/card/${dashcard.card.id}?date=${startDate.format(
        dateFormat
    )}~${endDate.format(dateFormat)}&interval=Day&unit_type=&countries=&promotions=&products=${encodeURIComponent(product)}`;
}
