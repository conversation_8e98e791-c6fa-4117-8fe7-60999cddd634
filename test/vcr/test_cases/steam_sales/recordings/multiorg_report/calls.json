{"calls": {"https://partner.steampowered.com/login": [{"body": {"data": "https_partner.steampowered.com/login", "encoding": "file"}, "headers": {"content-security-policy": "default-src blob: data: https: 'unsafe-inline' 'unsafe-eval'; connect-src 'self' https://community.akamai.steamstatic.com/ https://store.steampowered.com/ wss://community.steam-api.com/websocket/ https://api.steampowered.com/ https://login.steampowered.com/ https://help.steampowered.com/ https://*.valvesoftware.com https://*.steambeta.net https://*.steamcontent.com; report-uri /actions/CSPReport"}}, {"status": 302, "req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561199137521617%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ShUe6JByJ1B-io0U5Lf5N0XGiklJfFQN9E3u1JTFYTdTJ9Ofx41rleZOHNEOzIdphksjVq-gco9UbL7rG3qKBg; sessionid=c841d3c5b6838cf2af3e4654;"}, "headers": {"content-security-policy": "default-src blob: data: https: 'unsafe-inline' 'unsafe-eval'; connect-src 'self' https://community.akamai.steamstatic.com/ https://store.steampowered.com/ wss://community.steam-api.com/websocket/ https://api.steampowered.com/ https://login.steampowered.com/ https://help.steampowered.com/ https://*.valvesoftware.com https://*.steambeta.net https://*.steamcontent.com; report-uri /actions/CSPReport", "location": "https://partner.steampowered.com/", "content-encoding": "", "vary": ""}}], "https://scraper-api.indiebi.dev/1/auth-code/steam_sales": [{"req_headers": {"authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************.RMr0lnl9U6WGDgJyRqyc-7lXaN0qqcHET-tjHitYD9U", "host": "scraper-api.indiebi.dev", "connection": "keep-alive"}, "body": "{\"authCode\":null}", "headers": {"content-type": "application/json; charset=utf-8", "connection": "keep-alive", "request-context": "appId=cid-v1:1ae09687-f2ab-424c-b8e7-c482afdc19c9", "x-powered-by": "Express", "vary": "Origin", "access-control-allow-credentials": "true", "etag": "W/\"11-CSaGiLTQJ36vkT+ep4rzOLCKeXw\"", "strict-transport-security": "max-age=15724800; includeSubDomains", "server": "", "content-security-policy": "", "content-encoding": ""}}, {"req_headers": {"authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************.RMr0lnl9U6WGDgJyRqyc-7lXaN0qqcHET-tjHitYD9U", "host": "scraper-api.indiebi.dev", "connection": "keep-alive"}, "body": "{\"authCode\":\"5JNRX\"}", "headers": {"content-type": "application/json; charset=utf-8", "connection": "keep-alive", "request-context": "appId=cid-v1:1ae09687-f2ab-424c-b8e7-c482afdc19c9", "x-powered-by": "Express", "vary": "Origin", "access-control-allow-credentials": "true", "etag": "W/\"14-1h2bPe3ypfIhI7561sxMTst/YyQ\"", "strict-transport-security": "max-age=15724800; includeSubDomains", "server": "", "content-security-policy": "", "content-encoding": ""}}], "https://partner.steampowered.com/": [{"req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561199137521617%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ShUe6JByJ1B-io0U5Lf5N0XGiklJfFQN9E3u1JTFYTdTJ9Ofx41rleZOHNEOzIdphksjVq-gco9UbL7rG3qKBg; sessionid=c841d3c5b6838cf2af3e4654;"}, "body": {"data": "https_partner.steampowered.com/index.html", "encoding": "file"}, "headers": {"set-cookie": "dateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:34:45 GMT; Max-Age=10800; path=/, dateEnd=2023-08-08; expires=Tue, 08-Aug-2023 15:34:45 GMT; Max-Age=10800; path=/, priorDateStart=2023-08-07; expires=Tue, 08-Aug-2023 15:34:45 GMT; Max-Age=10800; path=/, priorDateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:34:45 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}, {"req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561199137521617%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.1ha6Zb0Z3Pif-8v0gqKB1YV0M_T_kyDf6nkoOowynzgF2qozG7Pcad1Qzm0lS-UzUZ-VYC5qWIeQG5PlsOYdBA; dateStart=2023-08-08; steamworksRunas=3204;"}, "body": {"data": "https_partner.steampowered.com/index_ee5a6e79190301a9700053cc11aab6d2.html", "encoding": "file"}, "headers": {"set-cookie": "dateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:34:48 GMT; Max-Age=10800; path=/, dateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:34:48 GMT; Max-Age=10800; path=/, priorDateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:34:48 GMT; Max-Age=10800; path=/, priorDateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:34:48 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}, {"req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561199137521617%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.1ha6Zb0Z3Pif-8v0gqKB1YV0M_T_kyDf6nkoOowynzgF2qozG7Pcad1Qzm0lS-UzUZ-VYC5qWIeQG5PlsOYdBA; dateStart=2023-08-08; steamworksRunas=123147;"}, "body": {"data": "https_partner.steampowered.com/index_bfc67c636245532ebe02297c600bb260.html", "encoding": "file"}, "headers": {"set-cookie": "dateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:34:49 GMT; Max-Age=10800; path=/, dateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:34:49 GMT; Max-Age=10800; path=/, priorDateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:34:49 GMT; Max-Age=10800; path=/, priorDateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:34:49 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/nav_games.php": [{"req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561199137521617%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ShUe6JByJ1B-io0U5Lf5N0XGiklJfFQN9E3u1JTFYTdTJ9Ofx41rleZOHNEOzIdphksjVq-gco9UbL7rG3qKBg; sessionid=c841d3c5b6838cf2af3e4654; dateStart=2023-08-08;"}, "body": {"data": "https_partner.steampowered.com/nav_games.php", "encoding": "file"}, "headers": {"set-cookie": "dateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:34:46 GMT; Max-Age=10800; path=/, dateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:34:46 GMT; Max-Age=10800; path=/, priorDateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:34:46 GMT; Max-Age=10800; path=/, priorDateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:34:46 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}, {"req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561199137521617%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.1ha6Zb0Z3Pif-8v0gqKB1YV0M_T_kyDf6nkoOowynzgF2qozG7Pcad1Qzm0lS-UzUZ-VYC5qWIeQG5PlsOYdBA;"}, "body": {"data": "https_partner.steampowered.com/nav_games_5a01a584c11b1bfa8f10793fe9df8125.php", "encoding": "file"}, "headers": {"set-cookie": "dateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:34:48 GMT; Max-Age=10800; path=/, dateEnd=2023-08-08; expires=Tue, 08-Aug-2023 15:34:48 GMT; Max-Age=10800; path=/, priorDateStart=2023-08-07; expires=Tue, 08-Aug-2023 15:34:48 GMT; Max-Age=10800; path=/, priorDateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:34:48 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/?dateStart=2021-11-01&dateEnd=2021-11-30": [{"req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561199137521617%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.1ha6Zb0Z3Pif-8v0gqKB1YV0M_T_kyDf6nkoOowynzgF2qozG7Pcad1Qzm0lS-UzUZ-VYC5qWIeQG5PlsOYdBA; dateStart=2023-08-08; steamworksRunas=3204;"}, "body": {"data": "https_partner.steampowered.com/index_18bd7d91e6309b43fe914ccfa947f2db.html", "encoding": "file"}, "headers": {"set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:34:51 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:34:51 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:34:51 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:34:51 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}, {"req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561199137521617%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.1ha6Zb0Z3Pif-8v0gqKB1YV0M_T_kyDf6nkoOowynzgF2qozG7Pcad1Qzm0lS-UzUZ-VYC5qWIeQG5PlsOYdBA; dateStart=2021-11-01; steamworksRunas=123147;"}, "body": {"data": "https_partner.steampowered.com/index_a831fe7830091b49992083b2cea810b8.html", "encoding": "file"}, "headers": {"set-cookie": "dateStart=2021-11-01; expires=Tue, 08-Aug-2023 15:34:52 GMT; Max-Age=10800; path=/, dateEnd=2021-11-30; expires=Tue, 08-Aug-2023 15:34:52 GMT; Max-Age=10800; path=/, priorDateStart=2021-10-02; expires=Tue, 08-Aug-2023 15:34:52 GMT; Max-Age=10800; path=/, priorDateEnd=2021-10-31; expires=Tue, 08-Aug-2023 15:34:52 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/report_csv.php?file=x&params=query=QueryPackageSalesForCSV%5EdateStart=2021-11-01%5EdateEnd=2021-11-30%5EHasDivisions=0%5Einterpreter=PartnerSalesReportInterpreter": [{"req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561199137521617%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.1ha6Zb0Z3Pif-8v0gqKB1YV0M_T_kyDf6nkoOowynzgF2qozG7Pcad1Qzm0lS-UzUZ-VYC5qWIeQG5PlsOYdBA; dateStart=2023-08-08; steamworksRunas=3204;"}, "body": {"data": "https_partner.steampowered.com/report_csv.php", "encoding": "file"}, "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\"", "transfer-encoding": "chunked", "connection": "close, Transfer-Encoding", "content-encoding": "", "vary": ""}}, {"req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561199137521617%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.1ha6Zb0Z3Pif-8v0gqKB1YV0M_T_kyDf6nkoOowynzgF2qozG7Pcad1Qzm0lS-UzUZ-VYC5qWIeQG5PlsOYdBA; dateStart=2021-11-01; steamworksRunas=123147;"}, "body": "sep=,\nSteam Sales data for straka.studio s. r. o.: 2021-11-01 - 2021-11-30\n\nDate, Bundle(ID#),Bundle Name,Product(ID#),Product Name,Type,Game,Platform,Country Code,Country,Region,Gross Units Sold,Chargeback/Returns,Net Units Sold,Base Price,Sale Price,Currency,Gross Steam Sales (USD),Chargeback/Returns (USD),VAT/Tax (USD),Net Steam Sales (USD),Tag\n2021-11-04,-1,Direct Package Sale,526742,\"Loot River for Beta Testing (retail)\",Retail,\"Loot River\",Windows,US,United States,North America,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"DEMO\"\n2021-11-06,-1,Direct Package Sale,526742,\"Loot River for Beta Testing (retail)\",Retail,\"Loot River\",Windows,SG,Singapore,South East Asia,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"DEMO\"\n2021-11-06,-1,Direct Package Sale,526742,\"Loot River for Beta Testing (retail)\",Retail,\"Loot River\",Windows,US,United States,North America,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"DEMO\"\n2021-11-07,-1,Direct Package Sale,526742,\"Loot River for Beta Testing (retail)\",Retail,\"Loot River\",Windows,US,United States,North America,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"DEMO\"\n2021-11-12,-1,Direct Package Sale,526742,\"Loot River for Beta Testing (retail)\",Retail,\"Loot River\",Windows,FI,Finland,Western Europe,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"DEMO\"\n2021-11-12,-1,Direct Package Sale,526742,\"Loot River for Beta Testing (retail)\",Retail,\"Loot River\",Windows,MY,Malaysia,South East Asia,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"DEMO\"\n2021-11-13,-1,Direct Package Sale,526742,\"Loot River for Beta Testing (retail)\",Retail,\"Loot River\",Windows,US,United States,North America,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"DEMO\"\n2021-11-14,-1,Direct Package Sale,526742,\"Loot River for Beta Testing (retail)\",Retail,\"Loot River\",Windows,DE,Germany,Western Europe,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"DEMO\"\n2021-11-15,-1,Direct Package Sale,526742,\"Loot River for Beta Testing (retail)\",Retail,\"Loot River\",Windows,US,United States,North America,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"DEMO\"\n2021-11-16,-1,Direct Package Sale,526742,\"Loot River for Beta Testing (retail)\",Retail,\"Loot River\",Windows,US,United States,North America,2,0,2,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"DEMO\"\n2021-11-17,-1,Direct Package Sale,526742,\"Loot River for Beta Testing (retail)\",Retail,\"Loot River\",Windows,US,United States,North America,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"DEMO\"\n2021-11-18,-1,Direct Package Sale,323842,\"Euclidean Skies\",Steam,\"Euclidean Skies\",Windows,US,United States,North America,1,0,1,4.99,4.99,USD,5.3200,0.0000,-0.3300,4.9900,\"\"\n2021-11-22,-1,Direct Package Sale,656708,\"Loot River Demo for Beta Testing (retail)\",Retail,\"Loot River Demo\",Windows,AT,Austria,Western Europe,1,0,1,0.00,0.00,USD,0.0000,0.0000,0.0000,0.0000,\"DEMO\"\n2021-11-27,-1,Direct Package Sale,323842,\"Euclidean Skies\",Steam,\"Euclidean Skies\",Windows,GB,United Kingdom,Western Europe,1,0,1,3.99,3.99,GBP,5.3188,0.0000,-0.8931,4.4257,\"\"\n", "headers": {"content-type": "application/octet-stream", "expires": "Mon, 26 Jul 1997 05:00:00 GMT", "cache-control": "maxage=1", "pragma": "public", "content-disposition": "attachment; filename=\"x.csv\"", "content-encoding": "", "vary": ""}}]}, "defaults": {"request_headers": {"accept": "application/json, text/plain, */*", "user-agent": "axios/0.27.2", "host": "partner.steampowered.com", "connection": "close"}, "headers": {"server": "nginx", "content-type": "text/html; charset=UTF-8", "content-security-policy": "script-src 'self' 'unsafe-inline' 'unsafe-eval'; object-src 'none'; connect-src 'self'; child-src 'self'; report-uri /actions/cspreport", "content-encoding": "gzip", "vary": "Accept-Encoding", "connection": "close"}}}