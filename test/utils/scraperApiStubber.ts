import * as moment from 'moment';
import * as sinon from 'sinon';
import {scrapDatesUrl} from '../../src/apiCommunication/apiUrls';
import * as axios from '../../src/apiCommunication/axios';

export function stubNextScrapDateResponse(datesStrings: string[]): sinon.SinonStub {
    const stubbed = sinon.stub(axios, 'get');
    const invocation = stubbed.withArgs(sinon.match(scrapDatesUrl), sinon.match.any);
    datesStrings.forEach((dateString, i) => {
        invocation.onCall(i).resolves(<any>{
            data: [
                {
                    dateFrom: moment(dateString),
                    dateTo: moment(dateString).add(1, 'day')
                }
            ]
        });
    });
    invocation.onCall(datesStrings.length).resolves(<any>{data: [null]});
    stubbed.resolves(<any>{data: [null]});
    return stubbed;
}
