#!/bin/bash

# If parameters not provided, print usage info
if [ $# -eq 0 ]; then
  echo "Usage: ./send_2fa_code.sh <source> <authCode>"
  exit 1
fi

# Load variables from .env file
if [ -f .env ]; then
  source .env
else
  echo "Error: .env file not found. Please provide a .env file with SCRAPER_API_URL and INDIEBI_JWT variables."
  exit 1
fi

SOURCE=$1
AUTH_CODE=$2

echo "Sending 2FA code for source: $SOURCE, code: $AUTH_CODE"

curl --location --request POST "$SCRAPER_API_URL/1/auth-code/$SOURCE" \
  --header "Authorization: Bearer $INDIEBI_JWT" \
  --header 'Content-Type: application/json' \
  --data-raw "{
    \"authCode\": \"$AUTH_CODE\"
}"

echo -e "\n2FA code sent successfully"
