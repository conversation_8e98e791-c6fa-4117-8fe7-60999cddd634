### Speed Profiles

_Please note that this option is a debug option and should not be used in production packages!_

If you want to limit the speed of your browser during tests, you can do it by changing the value of `"debugOptions.speedProfileName"`. The following options are available:

-   `OFFLINE` - no internet, scraper will not work with this option
-   `GPRS` - really slow, scraper will not work with this option
-   `REGULAR2G` - slow, scraper will not work with this option
-   `REGULAR3G`
-   `REGULAR4G`
-   `WIFI` - fast up to 30Mb/s download up to 15 Mb/s upload

If you want to use some different speed settings there is also `CUSTOM` profile. Configuration using CUSTOM profile would look like this:

```
  "debugOptions.speedProfileName": "CUSTOM",
  "debugOptions.speedProfile": {
  	"latency": 15,
  	"downloadSpeed": 50,
  	"uploadSpeed": 50
  },
```

Please provide download and upload speed in B/s (Bytes per second) and latency in milliseconds.

## Support

Currently, the agreed minimal supported speed is REGULAR3G, anything slower than that speed can (and probably will) fail.
At the moment this limitation is NOT enforced by the app itself.
