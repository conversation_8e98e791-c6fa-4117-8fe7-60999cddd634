import * as fs from 'fs';
import {expect} from 'chai';
import * as mockfs from 'mock-fs';
import * as td from 'testdouble';
import {DiscountManagementEntry} from '../../../../src/scrapersV2/steam/games/DiscountManagementData';
import {convertToCSVsFiles} from '../../../../src/scrapersV2/steam/games/steamDiscountsJson2CsvConverters';
import * as fileUtils from '../../../../src/utils/files/fileUtils';

describe('basePricesToCsv', () => {
    const sampleExcludedContentDescriptors = [
        {
            contentDescriptorid: 3,
            timestampAdded: 0
        },
        {
            contentDescriptorid: 4,
            timestampAdded: 0
        },
        {
            contentDescriptorid: 1,
            timestampAdded: 0
        }
    ];

    const discountManagementEntries: DiscountManagementEntry[] = [
        {
            basePrices: {
                '165761': {
                    USD: 2499,
                    GBP: 1999
                },
                '197766': {
                    USD: 2499,
                    GBP: 1799
                }
            },
            discountEvents: [
                {
                    name: 'Steam Winter Sale 2023',
                    startDate: **********,
                    endDate: **********,
                    description: '#discount_desc_preset_special',
                    collisionType: 'unique',
                    event: '1',
                    header: 'Steam Winter Sale 2023',
                    tooltip: 'SaleEvent_DurationDiscount_Tooltip',
                    type: 'discount',
                    preventWeeklong: 'on',
                    appids: [],
                    id: '8758'
                },
                {
                    name: 'Steam Autumn Sale 2023',
                    startDate: 1700589600,
                    endDate: 1701194400,
                    description: '#discount_desc_preset_special',
                    collisionType: 'unique',
                    event: '1',
                    header: 'Steam Autumn Sale 2023',
                    tooltip: 'SaleEvent_DurationDiscount_Tooltip',
                    type: 'discount',
                    preventWeeklong: 'on',
                    appids: [],
                    id: '8757'
                }
            ],
            maxDiscountPercentages: {'165761': {}, '197766': {}},
            packageDiscounts: [
                {
                    nDiscountID: 1133440,
                    packageID: 165761,
                    nDiscountPct: 20,
                    strDiscountName: 'Launch Discount',
                    strDiscountDescription: '#discount_desc_preset_special',
                    rtStartDate: 1495716786,
                    rtEndDate: 1496336406,
                    discountEventID: ''
                },
                {
                    nDiscountID: 1150507,
                    packageID: 165761,
                    nDiscountPct: 20,
                    strDiscountName: '2017 Steam Summer Sale',
                    strDiscountDescription: '#discount_desc_preset_summer',
                    rtStartDate: **********,
                    rtEndDate: **********,
                    discountEventID: ''
                }
            ],
            packageIds: [197766, 165761],
            priceIncreaseTimes: {'165761': **********, '197766': **********},
            publisherid: 3204,
            userinfo: {
                loggedIn: true,
                steamid: '*****************',
                accountid: **********,
                accountName: 'supersuperdata_superhotonly',
                isSupport: false,
                isLimited: false,
                isPartnerMember: true,
                countryCode: 'PL',
                excludedContentDescriptors: sampleExcludedContentDescriptors
            }
        },
        {
            basePrices: {
                '323842': {
                    USD: 499,
                    GBP: 399
                },
                '526743': {
                    USD: 2499,
                    GBP: 1999
                }
            },
            discountEvents: [
                {
                    name: 'Steam Winter Sale 2023',
                    startDate: **********,
                    endDate: **********,
                    description: '#discount_desc_preset_special',
                    collisionType: 'unique',
                    event: '1',
                    header: 'Steam Winter Sale 2023',
                    tooltip: 'SaleEvent_DurationDiscount_Tooltip',
                    type: 'discount',
                    preventWeeklong: 'on',
                    appids: [],
                    id: '8758'
                },
                {
                    name: 'Steam Autumn Sale 2023',
                    startDate: 1700589600,
                    endDate: 1701194400,
                    description: '#discount_desc_preset_special',
                    collisionType: 'unique',
                    event: '1',
                    header: 'Steam Autumn Sale 2023',
                    tooltip: 'SaleEvent_DurationDiscount_Tooltip',
                    type: 'discount',
                    preventWeeklong: 'on',
                    appids: [],
                    id: '8757'
                }
            ],
            maxDiscountPercentages: {'323842': {}, '526743': {}},
            packageDiscounts: [
                {
                    nDiscountID: 1546246,
                    packageID: 323842,
                    nDiscountPct: 30,
                    strDiscountName: 'Launch Discount',
                    strDiscountDescription: '#discount_desc_preset_special',
                    rtStartDate: 1549385978,
                    rtEndDate: 1549994438,
                    discountEventID: ''
                },
                {
                    nDiscountID: 1832383,
                    packageID: 323842,
                    nDiscountPct: 75,
                    strDiscountName: '2020 - Lunar New Year Sale',
                    strDiscountDescription: '#discount_desc_preset_special',
                    rtStartDate: **********,
                    rtEndDate: **********,
                    discountEventID: '424'
                }
            ],
            packageIds: [323842, 526743],
            priceIncreaseTimes: {'323842': **********, '526743': 0},
            publisherid: 123147,
            userinfo: {
                loggedIn: true,
                steamid: '*****************',
                accountid: **********,
                accountName: 'supersuperdata_superhotonly',
                isSupport: false,
                isLimited: false,
                isPartnerMember: true,
                countryCode: 'PL',
                excludedContentDescriptors: sampleExcludedContentDescriptors
            }
        }
    ];

    const discountInfoHistory = [
        {
            name: 'Weekend Deal - 10/12/2023 - THE COMPLETE SUPERHOT BUNDLE',
            description: '#discount_desc_preset_weekend',
            startDate: **********,
            dateStr: '10/12/23 10:00',
            endDate: **********,
            endDateStr: '10/19/23 10:00',
            percent: 70,
            quantity: 1,
            amount: 1750,
            id: 3510964,
            group: '9405',
            class: 'released',
            productId: 427502,
            publisherId: 3204
        },
        {
            name: 'Custom Discount',
            description: '#discount_desc_preset_special',
            startDate: **********,
            dateStr: '08/27/23 10:00',
            endDate: 1694365200,
            endDateStr: '09/10/23 10:00',
            percent: 70,
            quantity: 1,
            amount: 1750,
            id: 3441135,
            group: false,
            class: 'released',
            productId: 427502,
            publisherId: 3204
        }
    ];

    let cvsFilenames: string[];

    beforeEach(async () => {
        mockfs();
        td.replace(fileUtils, 'buildFilePath', (filename: string) => filename);
        cvsFilenames = await convertToCSVsFiles(discountManagementEntries, discountInfoHistory);
    });
    afterEach(() => {
        mockfs.restore();
        td.reset();
    });

    it('should convert basePrices to csv', () => {
        const csvData = fs.readFileSync('basePrices.csv').toString();
        expect(cvsFilenames).contains('basePrices.csv');
        expect(csvData).to.equal('publisherId,packageId,USD,GBP\r\n3204,165761,2499,1999\r\n3204,197766,2499,1799\r\n123147,323842,499,399\r\n123147,526743,2499,1999');
    });

    it('should convert discountEvents to csv', () => {
        const csvData = fs.readFileSync('discountEvents.csv').toString();
        expect(cvsFilenames).contains('discountEvents.csv');
        expect(csvData).to.equal(
            'publisherId,name,startDate,endDate,description,collisionType,event,header,tooltip,type,preventWeeklong,appids,id\r\n' +
                '3204,Steam Winter Sale 2023,**********,**********,#discount_desc_preset_special,unique,1,Steam Winter Sale 2023,SaleEvent_DurationDiscount_Tooltip,discount,on,,8758\r\n' +
                '3204,Steam Autumn Sale 2023,1700589600,1701194400,#discount_desc_preset_special,unique,1,Steam Autumn Sale 2023,SaleEvent_DurationDiscount_Tooltip,discount,on,,8757\r\n' +
                '123147,Steam Winter Sale 2023,**********,**********,#discount_desc_preset_special,unique,1,Steam Winter Sale 2023,SaleEvent_DurationDiscount_Tooltip,discount,on,,8758\r\n' +
                '123147,Steam Autumn Sale 2023,1700589600,1701194400,#discount_desc_preset_special,unique,1,Steam Autumn Sale 2023,SaleEvent_DurationDiscount_Tooltip,discount,on,,8757'
        );
    });

    it('should convert packageDiscounts to csv', () => {
        const csvData = fs.readFileSync('packageDiscounts.csv').toString();
        expect(cvsFilenames).contains('packageDiscounts.csv');
        expect(csvData).to.equal(
            'publisherId,nDiscountID,packageID,nDiscountPct,strDiscountName,strDiscountDescription,rtStartDate,rtEndDate,discountEventID\r\n' +
                '3204,1133440,165761,20,Launch Discount,#discount_desc_preset_special,1495716786,1496336406,\r\n' +
                '3204,1150507,165761,20,2017 Steam Summer Sale,#discount_desc_preset_summer,**********,**********,\r\n' +
                '123147,1546246,323842,30,Launch Discount,#discount_desc_preset_special,1549385978,1549994438,\r\n' +
                '123147,1832383,323842,75,2020 - Lunar New Year Sale,#discount_desc_preset_special,**********,**********,424'
        );
    });

    it('should convert packageIds to csv', () => {
        const csvData = fs.readFileSync('packageIds.csv').toString();
        expect(cvsFilenames).contains('packageIds.csv');
        expect(csvData).to.equal('publisherId,packageId\r\n' + '3204,197766\r\n' + '3204,165761\r\n' + '123147,323842\r\n' + '123147,526743');
    });

    it('should convert priceIncreaseTimes to csv', () => {
        const csvData = fs.readFileSync('priceIncreaseTimes.csv').toString();
        expect(cvsFilenames).contains('priceIncreaseTimes.csv');
        expect(csvData).to.equal(
            'publisherId,packageId,priceIncreaseTime\r\n' +
                '3204,165761,**********\r\n' +
                '3204,197766,**********\r\n' +
                '123147,323842,**********\r\n' +
                '123147,526743,0'
        );
    });

    it('should convert userinfo to csv', () => {
        const csvData = fs.readFileSync('userinfo.csv').toString();
        expect(cvsFilenames).contains('userinfo.csv');
        expect(csvData).to.equal(
            'publisherId,loggedIn,steamid,accountid,accountName,isSupport,isLimited,isPartnerMember,countryCode,excludedContentDescriptors\r\n' +
                '3204,true,*****************,**********,supersuperdata_superhotonly,false,false,true,PL,"[{""contentDescriptorid"":3,""timestampAdded"":0},{""contentDescriptorid"":4,""timestampAdded"":0},{""contentDescriptorid"":1,""timestampAdded"":0}]"\r\n' +
                '123147,true,*****************,**********,supersuperdata_superhotonly,false,false,true,PL,"[{""contentDescriptorid"":3,""timestampAdded"":0},{""contentDescriptorid"":4,""timestampAdded"":0},{""contentDescriptorid"":1,""timestampAdded"":0}]"'
        );
    });

    it('should convert discountHistory to csv', () => {
        const csvData = fs.readFileSync('discountHistory.csv').toString();
        expect(cvsFilenames).contains('discountHistory.csv');
        expect(csvData).to.equal(
            'name,description,startDate,dateStr,endDate,endDateStr,percent,quantity,amount,id,group,class,productId,publisherId\r\n' +
                'Weekend Deal - 10/12/2023 - THE COMPLETE SUPERHOT BUNDLE,#discount_desc_preset_weekend,**********,10/12/23 10:00,**********,10/19/23 10:00,70,1,1750,3510964,9405,released,427502,3204\r\n' +
                'Custom Discount,#discount_desc_preset_special,**********,08/27/23 10:00,1694365200,09/10/23 10:00,70,1,1750,3441135,false,released,427502,3204'
        );
    });

    it('should not propagate exception if any CSV conversion fails', async () => {
        expect(await convertToCSVsFiles(undefined as any, undefined as any)).to.be.deep.equal([]);
    });
});
