import * as moment from 'moment';
import {BrowserSession} from '../../../browserV2';
import {CustomException} from '../../../error/CustomException';
import {errorTypes} from '../../../error/errorTypes';
import {HTTPClient} from '../../../utils/http';
import {ProgressCallback} from '../../ProgressCallback';
import {GetSourceSideOrganizationsFunction} from '../../Scraper';
import {SteamParams} from '../common/login';
import {SteamOrganization, SteamSession} from '../common/SteamSession';
import {checkSteamPoweredLogin} from './checkSession';
import {detectLowPermission} from './lowPermissions';
import {steamPoweredCheerioRequest} from './steamPoweredRequest';

export async function retrieveSourceOrgs(http: HTTPClient): Promise<SteamOrganization[]> {
    // use /nav_games.php instead of the home page, since it works the same for full permission
    // accounts and low permission accounts - without redirects.
    const $ = await steamPoweredCheerioRequest('/nav_games.php', http);

    return $('select[name="runasPubid"] option')
        .map((_i, el) => ({id: $(el).val() as string, name: $(el).text() as string}))
        .toArray()
        .filter((org) => org.id != '-1');
}

export async function refreshSourceOrgsInSession(progress: ProgressCallback, session: SteamSession, http: HTTPClient): Promise<SteamOrganization[]> {
    if (session.orgs && session.orgsLastUpdated) {
        const expireTime = moment(session.orgsLastUpdated).add(24, 'h');
        if (expireTime > moment()) {
            progress(`Using ${session.orgs.length} orgs from cache.`);
            return session.orgs;
        }
    }
    session.orgs = await retrieveSourceOrgs(http);
    if (!session.orgs || session.orgs.length == 0) {
        throw new CustomException({message: 'No organizations are available using this Steam account.', errorType: errorTypes.CONFIGURATION_ISSUE});
    }
    progress(`Retrieved ${session.orgs.length} orgs from portal.`);
    for (const org of session.orgs) {
        progress(`Detecting account permissions for ${org.name} (${org.id})`);
        org.isLowPermission = await detectLowPermission(http, org);
        progress(`Account permissions for ${org.id} - ${org.isLowPermission ? 'low' : 'high'}`);
    }
    session.orgsLastUpdated = moment().toISOString();
    return session.orgs;
}

export const getSteamPoweredSourceSideOrganizations: GetSourceSideOrganizationsFunction<SteamParams, BrowserSession> = async ({getHttpClient}) => {
    const http = getHttpClient();
    await checkSteamPoweredLogin(http);

    const orgs = await retrieveSourceOrgs(http);
    return orgs.map(({name, id}) => ({name, id}));
};
