import * as chai from 'chai';
import * as chaiAsPromised from 'chai-as-promised';
import * as td from 'testdouble';
import {InsufficientPrivilegesLevelException, LoginException, LoginExceptionType} from '../../../../../src/error/exceptions';
import {checkSteamPoweredLogin} from '../../../../../src/scrapersV2/steam/powered/checkSession';
import * as steamPoweredRequest from '../../../../../src/scrapersV2/steam/powered/steamPoweredRequest';
import {HTTPClient} from '../../../../../src/utils/http';

chai.use(chaiAsPromised);
const {expect} = chai;

describe('checkSteamPoweredLogin', () => {
    it('should return true if the response status is 200 and the url is the home page', async () => {
        const steamPoweredRequestMock = td.replace(steamPoweredRequest, 'steamPoweredRequest');
        const httpClient = td.object<HTTPClient>();

        const response = {
            status: 200,
            config: {
                url: `${steamPoweredRequest.steamPoweredUrl}/`
            }
        };

        td.when(steamPoweredRequestMock('/login', td.matchers.anything())).thenResolve(response as any);

        const result = await checkSteamPoweredLogin(httpClient);
        expect(result).to.be.true;
    });

    it('should return true if the response status is 200 and the url is the nav_games.php page (dashboard)', async () => {
        const steamPoweredRequestMock = td.replace(steamPoweredRequest, 'steamPoweredRequest');
        const httpClient = td.object<HTTPClient>();

        const response = {
            status: 200,
            config: {
                url: `${steamPoweredRequest.steamPoweredUrl}/nav_games.php`
            }
        };

        td.when(steamPoweredRequestMock('/login', td.matchers.anything())).thenResolve(response as any);

        const result = await checkSteamPoweredLogin(httpClient);
        expect(result).to.be.true;
    });

    it('should throw SESSION_EXPIRED exception if the response status is not 200', async () => {
        const steamPoweredRequestMock = td.replace(steamPoweredRequest, 'steamPoweredRequest');
        const httpClient = td.object<HTTPClient>();

        const response = {
            status: 302,
            config: {
                url: `${steamPoweredRequest.steamPoweredUrl}/`
            }
        };

        td.when(steamPoweredRequestMock('/login', td.matchers.anything())).thenResolve(response as any);

        await expect(checkSteamPoweredLogin(httpClient)).to.be.rejectedWith(new LoginException(LoginExceptionType.SESSION_EXPIRED).message);
    });

    it('should throw SESSION_EXPIRED exception if the response status is 200 and the url is not the home page or the nav games page', async () => {
        const steamPoweredRequestMock = td.replace(steamPoweredRequest, 'steamPoweredRequest');
        const httpClient = td.object<HTTPClient>();

        const response = {
            status: 200,
            config: {
                url: 'https://partner.steampowered.com/login' // redirected to login page
            }
        };

        td.when(steamPoweredRequestMock('/login', td.matchers.anything())).thenResolve(response as any);

        await expect(checkSteamPoweredLogin(httpClient)).to.be.rejectedWith(new LoginException(LoginExceptionType.SESSION_EXPIRED).message);
    });

    it('should throw InsufficientPrivilegesLevelException if the response is a page with specific error message', async () => {
        const steamPoweredRequestMock = td.replace(steamPoweredRequest, 'steamPoweredRequest');
        const httpClient = td.object<HTTPClient>();

        const response = {
            status: 200,
            config: {
                url: 'https://partner.steampowered.com/login'
            },
            data: '<div id="error_display">The account you logged in to does not have access to this site.</div>'
        };

        td.when(steamPoweredRequestMock('/login', td.matchers.anything())).thenResolve(response as any);

        await expect(checkSteamPoweredLogin(httpClient)).to.be.rejectedWith(
            InsufficientPrivilegesLevelException,
            'The account you logged in to does not have access to this site.'
        );
    });
});
