import * as moment from 'moment';
import {Source} from '../dataTypes';
import {CustomException} from '../error/CustomException';
import {errorTypes} from '../error/errorTypes';

export class DateIsNotUTCException extends CustomException {
    constructor() {
        super({
            message: 'Provided date was non UTC. Using non UTC format dates is forbidden as it can lead to coverage issues',
            suggestedAction: 'Please provide date in UTC format',
            errorType: errorTypes.INTERNAL_SCRAPER_ISSUE
        });
    }
}

export class Report {
    readonly source: Source;
    readonly reportFileName: string;
    readonly startDate: moment.Moment;
    readonly endDate: moment.Moment;
    readonly noData: boolean;

    constructor(source: Source, reportFileName: string, startDate: moment.Moment, endDate: moment.Moment, noData = false) {
        if (!startDate.isUTC()) {
            throw new DateIsNotUTCException();
        }
        if (!endDate.isUTC()) {
            throw new DateIsNotUTCException();
        }

        this.reportFileName = reportFileName;
        this.startDate = startDate;
        this.endDate = endDate;
        this.source = source;
        this.noData = noData;
    }
}
