import json
import logging
from pathlib import Path
import pytest
import time
import httpx

from scrapers_vcr.vcr_proxy import VCRProxy
from scrapers_vcr.util.scraper_runner import ScraperRunConfig, default_executable

from cryptography.fernet import Fernet
import os

pytest.register_assert_rewrite("vcr.util.scraper_runner", "vcr.util.testing")


@pytest.fixture(scope="session")
def proxy_server(pytestconfig: pytest.Config):
    mode: str = pytestconfig.getoption("--vcr-mode", "playback")  # type: ignore
    proxy = VCRProxy(mode)
    with proxy.run_server():
        yield proxy


@pytest.fixture(scope="session")
def api_info():
    credentials_file = Path(__file__).parent / "credentials_encrypted.json"
    with open(credentials_file, "rb") as f:
        encrypted_data = f.read()
        client = httpx.AsyncClient()
        fernet = Fernet(os.environ["SCP_CREDENTIALS_ENCRYPTION_KEY"])
        decrypted_data = json.loads(fernet.decrypt(encrypted_data))
        api_info = decrypted_data["indie_bi_api"]

        response = httpx.post(api_info["url"] + "/1/login", data={"email": api_info["email"], "password": api_info["password"]})
        return {
            "api_url": api_info["url"],
            "api_token": response.json()["jwt"],
        }


@pytest.fixture(autouse=True)
def log_start(request: pytest.FixtureRequest):
    start = time.perf_counter()
    logging.info(f"{request.node.name} test start")
    yield
    time_elapsed = time.perf_counter() - start
    logging.info(f"{request.node.name} test end, took {time_elapsed:2f}s")


@pytest.fixture(autouse=True)
def proxy(proxy_server: VCRProxy, request: pytest.FixtureRequest):

    no_live = getattr(request.function, "__nolive__", None)
    no_record = getattr(request.function, "__norecord__", None)
    if no_live and proxy_server.mode != "playback":
        pytest.skip()
    if no_record and proxy_server.mode == "record":
        pytest.skip()
    base_path = os.path.join(os.path.dirname(request.path), "recordings")
    recording_dir = getattr(request.function, "__recording_dir__", None)
    if recording_dir:
        proxy_server.use(recording_dir, "config.yaml", base_path)
    yield proxy_server
    if proxy_server.vcr:
        proxy_server.save()


@pytest.fixture
def credentials(request: pytest.FixtureRequest):
    credentials_file = Path(__file__).parent / "credentials_encrypted.json"
    with open(credentials_file, "rb") as f:
        encrypted_data = f.read()

        fernet = Fernet(os.environ["SCP_CREDENTIALS_ENCRYPTION_KEY"])
        decrypted_data = fernet.decrypt(encrypted_data)
        credentials = json.loads(decrypted_data)

        credentials_key = getattr(request.function, "__credentials__", None)

        if credentials_key:
            return credentials[credentials_key]
        else:
            return {}


def pytest_addoption(parser: pytest.Parser):
    parser.addoption("--vcr-mode", choices=["playback", "record", "live"], action="store", default="playback", help="mode of the VCR proxy to use.")


@pytest.fixture
def default_scraper(tmp_path, credentials, api_info):
    src = ScraperRunConfig(
        api_info=api_info,
        source=None,
        date_from="2021-11-01",
        date_to="2021-11-30",
        report_path=str(tmp_path),
        credentials=credentials,
        out_session_file=str(tmp_path / "out_session.json"),
    )
    src.executable = default_executable(current_file=__file__, levels_to_up=3)
    return src
