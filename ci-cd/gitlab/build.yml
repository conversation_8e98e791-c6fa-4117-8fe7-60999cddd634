build:
    cache:
        key:
            files:
                - package-lock.json
        paths:
            - .npm/
    variables:
        PUPPETEER_CHROME_SKIP_DOWNLOAD: 1
    stage: build
    interruptible: true
    # build and test jobs are merged to minimize time spent
    # on switching jobs and uploading/downloading intermediate artifacts.
    script:
        # build
        - npm config set '//gitlab.com/api/v4/packages/npm/:_authToken' "${CI_JOB_TOKEN}"
        - time npm ci --cache .npm --prefer-offline --no-audit --no-fund
        - time ./node_modules/.bin/tsc
    artifacts:
        when: on_success
        paths:
            - node_modules
            - dist
