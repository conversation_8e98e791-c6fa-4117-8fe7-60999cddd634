import * as stream from 'node:stream';
import * as chai from 'chai';
import * as chaiAsPromised from 'chai-as-promised';
import {StatusCodes} from 'http-status-codes';
import * as td from 'testdouble';
import {BrowserPage} from '../../../../src/browserV2';
import {InsufficientPrivilegesLevelException, LoginException, RequestFailedException} from '../../../../src/error/exceptions';
import {downloadFile} from '../../../../src/scrapersV2/epic/epic';
import {createErrorResponse} from '../../../utils/stubAxios';

chai.use(chaiAsPromised);
const {expect} = chai;

describe('downloadFile', async () => {
    afterEach(() => td.reset());

    const setupTest = (responseData: object, statusCode: number) => {
        const page = td.object<BrowserPage>();
        const dataStream = stream.Readable.from([JSON.stringify(responseData)]);
        const axiosError = createErrorResponse(dataStream, statusCode);

        td.when(page.request(td.matchers.anything(), td.matchers.anything(), td.matchers.anything())).thenReject(new RequestFailedException(axiosError));

        return page;
    };

    it('should properly recognize REQUIRE_FEATURE_ACCESS error response', async () => {
        const responseData = {
            responseStatusCode: 403,
            responseData: {organizationId: 'o-s22ys6lvw4qa9tvpqsnwkqmtug2ey7', reason: 'REQUIRE_FEATURE_ACCESS', featureSlug: 'reports'},
            message: 'Request failed with status code 403',
            path: '/:organizationId/report/download'
        };
        const page = setupTest(responseData, StatusCodes.FORBIDDEN);

        await expect(downloadFile('https://example.com', 'file.zip', page)).to.be.rejectedWith(InsufficientPrivilegesLevelException);
    });

    it('should properly recognize REQUIRE_MEMBER error response', async () => {
        const responseData = {
            responseStatusCode: 403,
            responseData: {organizationId: 'o-s22ys6lvw4qa9tvpqsnwkqmtug2ey7', reason: 'REQUIRE_MEMBER'},
            message: 'Request failed with status code 403',
            path: '/:organizationId/report/download'
        };
        const page = setupTest(responseData, StatusCodes.FORBIDDEN);

        await expect(downloadFile('https://example.com', 'file.zip', page)).to.be.rejectedWith(InsufficientPrivilegesLevelException);
    });

    it('should properly recognize 401 status code', async () => {
        const responseData = {status: 'Error', statusMessage: 'Please login or refresh the page.', statusCode: 401};
        const page = setupTest(responseData, StatusCodes.UNAUTHORIZED);

        await expect(downloadFile('https://example.com', 'file.zip', page)).to.be.rejectedWith(LoginException);
    });
});
