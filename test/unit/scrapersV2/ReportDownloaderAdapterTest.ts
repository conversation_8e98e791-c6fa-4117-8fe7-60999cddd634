import * as chai from 'chai';
import * as chaiAsPromised from 'chai-as-promised';
import * as moment from 'moment';
import * as td from 'testdouble';
import {TestDouble} from 'testdouble';
import {BrowserPage, BrowserV2} from '../../../src/browserV2';
import {Source} from '../../../src/dataTypes';
import {PuppeteerPage} from '../../../src/puppeteer';
import {DownloadReportResult} from '../../../src/scrapersV1/DownloadReportResult';
import * as GogSalesReportDownloader from '../../../src/scrapersV1/httpDownloaders/gog/GOGReportDownloader';
import {HttpReportDownloader} from '../../../src/scrapersV1/HttpReportDownloader';
import {LoginResult} from '../../../src/scrapersV1/LoginResult';
import {Report} from '../../../src/scrapersV1/Report';
import {ReportDownloader} from '../../../src/scrapersV1/ReportDownloader';
import {ProgressCallback} from '../../../src/scrapersV2/ProgressCallback';
import * as DownloaderFactory from '../../../src/scrapersV2/reportDownloadAdapter/DownloaderFactory';
import {ReportDownloaderAdapter} from '../../../src/scrapersV2/reportDownloadAdapter/ReportDownloaderAdapter';
import {ScraperContext} from '../../../src/scrapersV2/ScraperContext';

const expect = chai.expect;
chai.use(chaiAsPromised);

describe('ReportDownloaderAdapter', function () {
    let launch: TestDouble<any>;
    let page: BrowserPage;
    let browserV2: BrowserV2;
    let getDownloader: (params: unknown) => ReportDownloader;
    let reportDownloader: HttpReportDownloader;

    const mockedProgress = td.func<ProgressCallback>();
    const getPage: () => Promise<BrowserPage> = async () => page;
    const startDate = moment().utc();
    const endDate = moment().utc().add(7, 'days');
    const context = {
        params: {user: 'x', password: 'x', ignoredProducts: ['1', '2']},
        progress: mockedProgress,
        getPage
    } as ScraperContext<any, any>;
    beforeEach(() => {
        launch = td.replace(BrowserV2, 'launch');
        browserV2 = td.object<BrowserV2>();
        page = td.object<BrowserPage>();
        (page as any).page = td.object<PuppeteerPage>();
        (browserV2 as any).page = page;
        td.when(page.getSession()).thenResolve({});
        td.when(launch(), {ignoreExtraArgs: true}).thenResolve(browserV2);

        getDownloader = td.replace(DownloaderFactory, 'getDownloader');
        reportDownloader = td.object<GogSalesReportDownloader.GOGReportDownloader>();
        td.when(getDownloader(td.matchers.anything())).thenReturn(reportDownloader);
        td.when(reportDownloader.performLogin()).thenResolve(LoginResult.createSuccessfulLoginResult());
    });

    afterEach(td.reset);

    it('initializes ReportDownloader', async () => {
        const browserV1Matcher = td.matchers.create({
            name: 'browserV1Matcher',
            matches(_matcherArgs, actual) {
                return actual.page === page.page;
            }
        });

        const adapter = new ReportDownloaderAdapter(Source.STEAM_SALES);
        await adapter.login(context);
        td.verify(
            getDownloader({login: 'x', password: 'x', totpSecret: undefined, browser: browserV1Matcher(), source: Source.STEAM_SALES, ignoredProductsArray: ['1', '2']})
        );
    });

    it('throws LoginException when login failed during login', async () => {
        td.when(reportDownloader.performLogin()).thenResolve(LoginResult.createAuthenticationExceptionResult('invalid password'));
        const adapter = new ReportDownloaderAdapter(Source.STEAM_SALES);
        await expect(adapter.login(context)).to.be.rejectedWith('invalid password');
    });

    it('lazy loads ReportDownloader', async () => {
        const adapter = new ReportDownloaderAdapter(Source.STEAM_SALES);
        await adapter.login(context);
        await adapter.login(context);
        td.verify(getDownloader(td.matchers.anything()), {times: 1});
    });

    it('throws Error when no reports were generated', async () => {
        td.when(reportDownloader.downloadReport(startDate, endDate)).thenResolve(
            DownloadReportResult.createLoginErrorResult(LoginResult.createAuthenticationExceptionResult('foo'))
        );
        const adapter = new ReportDownloaderAdapter(Source.STEAM_SALES);
        await expect(adapter.scrape(context, startDate, endDate)).to.be.rejectedWith('foo');
    });

    it('returns generated reports', async () => {
        const expectedReports = [new Report(Source.STEAM_SALES, 'foo1.csv', startDate, endDate), new Report(Source.STEAM_SALES, 'foo2.csv', startDate, endDate)];

        td.when(reportDownloader.downloadReport(startDate, endDate)).thenResolve(DownloadReportResult.createReportResult(expectedReports));
        const adapter = new ReportDownloaderAdapter(Source.STEAM_SALES);
        const actualReports = await adapter.scrape(context, startDate, endDate);
        expect(actualReports).to.deep.equal(expectedReports);
    });
});
