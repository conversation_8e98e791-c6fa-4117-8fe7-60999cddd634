#!/bin/bash

# Usage: ./package-windows.sh <config_file> <target_dir>

# go to top-level project directory
cd $(dirname "$0")/..

PKG_DIR=$1
TARGETS=$2

RELEASE_VERSION=$(echo $(./scripts/get-version.sh) | sed "s/-.*//g")
if [ -z "$RELEASE_VERSION" ]; then
    echo "Release version cannot be empty!"
    exit 1
fi

pushd dist && npm version $RELEASE_VERSION --git-tag-version=false --allow-same-version && popd

./node_modules/.bin/pkg ./pkg.config.json --targets $TARGETS --out-path=$PKG_DIR
