from scrapers_vcr.util.scraper_runner import ScraperRunConfig


def test_can_get_manual_login_details(default_scraper: ScraperRunConfig):
    default_scraper.source = "steam_impressions"
    result = default_scraper.get_manual_login_details()
    result.expect_success()
    result_data = result.data
    assert result_data
    assert result_data["url"] == "https://partner.steamgames.com/dashboard"
    assert result_data["successSelector"] == {"value": "#landingWelcome", "type": "css"}
