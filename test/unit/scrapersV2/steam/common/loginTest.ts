import * as chai from 'chai';
import * as chaiAsPromised from 'chai-as-promised';
import * as steamSession from 'steam-session';
import {EResult} from 'steam-session';
import * as td from 'testdouble';
import * as authCodes from '../../../../../src/apiCommunication/authCodes';
import * as messaging from '../../../../../src/cli/messaging';
import {DualAuthMethod} from '../../../../../src/cli/messaging';
import {Portal, Source} from '../../../../../src/dataTypes';
import {LoginException, LoginExceptionType} from '../../../../../src/error/exceptions';
import {<PERSON>ie} from '../../../../../src/puppeteer';
import {ProgressCallback} from '../../../../../src/scrapersV2/ProgressCallback';
import {ScraperContext} from '../../../../../src/scrapersV2/ScraperContext';
import {getLoginFunction, maxSteamEmailCodeAttempts, refreshSessionCookies} from '../../../../../src/scrapersV2/steam/common/login';
import {SteamSession} from '../../../../../src/scrapersV2/steam/common/SteamSession';
import {HTTPClient} from '../../../../../src/utils/http';
import {sleep} from '../../../../../src/utils/sleep';
import {trackPromiseState} from '../../../../utils/promises';
import {shouldThrowAsync} from '../../../../utils/shouldThrowAsync';

chai.use(chaiAsPromised);
const expect = chai.expect;

describe('Steam (common) - getLoginFunction', () => {
    let LoginSession: typeof steamSession.LoginSession;
    let context: ScraperContext<any, any>;
    let startWithCredentialsStub: td.PromiseStubber<any, any>;
    const sessionOkMock = async () => true;
    const sessionExpiredMock = async () => {
        throw new LoginException(LoginExceptionType.SESSION_EXPIRED);
    };
    const eventHandlers: Record<string, (...args: any[]) => any> = {};
    const params = {user: 'user', password: 'password'};
    const steamGuardMachineToken = 'steamGuardMachineToken';
    const session = {refreshToken: false, steamGuardMachineToken};
    const source = Source.STEAM_DISCOUNTS;
    const portal = Portal.STEAM;

    beforeEach(() => {
        context = {
            params,
            session,
            progress: td.func<ProgressCallback>(),
            getHttpClient: () => td.object<HTTPClient>()
        } as ScraperContext<any, any>;

        LoginSession = td.replace(steamSession, 'LoginSession');
        td.replace(LoginSession.prototype, 'on', (event: string, handler: () => any) => {
            eventHandlers[event] = handler;
        });
        td.replace(LoginSession.prototype, 'getWebCookies', () => []);
        startWithCredentialsStub = td.when(LoginSession.prototype.startWithCredentials(td.matchers.anything()));
    });

    afterEach(() => {
        td.reset();
    });

    it('getLoginFunction returns a function, returning a rejected promise in case of Steam-specific eresult error', async () => {
        startWithCredentialsStub.thenReject({eresult: steamSession.EResult.RateLimitExceeded} as any as Error);
        const loginFunction = getLoginFunction(source, sessionExpiredMock);
        await expect(loginFunction(context)).to.be.rejectedWith(new LoginException(LoginExceptionType.RATE_LIMIT_EXCEEDED).message);
    });

    it('getLoginFunction returns a function, rethrowing standard error if occurred', async () => {
        const error = new Error('Some error');
        startWithCredentialsStub.thenReject(error);
        const loginFunction = getLoginFunction(source, sessionExpiredMock);
        await expect(loginFunction(context)).to.be.rejectedWith(error);
    });

    it('getLoginFunction returns a function, returning a resolved promise with a session in case of checkLoginFunction resolves to true ', async () => {
        const result = await getLoginFunction(source, sessionOkMock)(context);
        expect(result).to.be.deep.equal(session);
    });

    it(`getLoginFunction tries to log in with email code up to ${maxSteamEmailCodeAttempts} times`, async () => {
        startWithCredentialsStub.thenResolve({actionRequired: true, validActions: [{type: steamSession.EAuthSessionGuardType.EmailCode}]});
        const submitSteamGuardCodeStub = td.when(LoginSession.prototype.submitSteamGuardCode(td.matchers.anything()));
        const loginException = new LoginException(LoginExceptionType.TOO_MANY_2FA_ATTEMPTS);
        td.when(submitSteamGuardCodeStub).thenReject(loginException);

        const getAuthCode = td.replace(authCodes, 'getAuthCode');
        const code = '12345';
        td.when(getAuthCode({source})).thenResolve(code);

        const printDualAuth = td.replace(messaging, 'printDualAuth');
        const credentials = {accountName: params.user, password: params.password, steamGuardMachineToken};

        await shouldThrowAsync(async () => getLoginFunction(source, sessionExpiredMock)(context), new LoginException(LoginExceptionType.TOO_MANY_2FA_ATTEMPTS), true);

        td.verify(LoginSession.prototype.startWithCredentials(credentials), {times: 1}); // first time, without code
        Array.from({length: maxSteamEmailCodeAttempts}, (_, i) => i + 1).forEach((attempt) => {
            td.verify(printDualAuth({portal, attempt, maxAttempts: maxSteamEmailCodeAttempts, authMethod: DualAuthMethod.EMAIL_CODE}), {times: 1});
        });
        td.verify(getAuthCode({source}, undefined), {times: maxSteamEmailCodeAttempts});
    });

    it('getLoginFunction returns a function that tries to log in with 2FA when an account is set up to use TOTP but no TOTP value is provided', async () => {
        startWithCredentialsStub.thenResolve({
            actionRequired: true,
            validActions: [{type: steamSession.EAuthSessionGuardType.DeviceCode}]
        });
        const submitSteamGuardCodeStub = td.when(LoginSession.prototype.submitSteamGuardCode(td.matchers.anything()));
        const loginException = new LoginException(LoginExceptionType.TOO_MANY_2FA_ATTEMPTS);
        td.when(submitSteamGuardCodeStub).thenReject(loginException);

        const getAuthCode = td.replace(authCodes, 'getAuthCode');
        td.when(getAuthCode({source})).thenResolve('12345');

        //We expect the specific error because by mocking startWithCredentialsStub we also mock the event emitter that would call the proper event handler
        await expect(getLoginFunction(source, sessionExpiredMock)(context)).to.be.rejectedWith(loginException.message);
        td.verify(getAuthCode({source}, undefined), {times: 5});
    });

    it('getLoginFunction tries to log in with TOTP when steam login returns a proper response and TOTP is provided', async () => {
        const totpSecret = 'totpSecret';
        context = {
            params: {...params, totpSecret},
            session,
            progress: td.func<ProgressCallback>(),
            getHttpClient: () => td.object<HTTPClient>()
        } as ScraperContext<any, any>;

        startWithCredentialsStub.thenResolve({
            actionRequired: true,
            validActions: [{type: steamSession.EAuthSessionGuardType.DeviceCode}]
        });
        const submitSteamGuardCodeStub = td.when(LoginSession.prototype.submitSteamGuardCode(td.matchers.anything()));
        const loginException = new LoginException(LoginExceptionType.MFA_INVALID);
        td.when(submitSteamGuardCodeStub).thenReject(loginException);

        await expect(getLoginFunction(source, sessionExpiredMock)(context)).to.be.rejectedWith(loginException.message);

        td.verify(
            LoginSession.prototype.startWithCredentials({
                accountName: params.user,
                password: params.password,
                steamGuardMachineToken
            }),
            {times: 1}
        );
    });

    it('getLoginFunction ignores device confirmation and generates 2FA code if TOTP is provided', async () => {
        const totpSecret = 'totpSecret';
        context = {
            params: {...params, totpSecret},
            session,
            progress: td.func<ProgressCallback>(),
            getHttpClient: () => td.object<HTTPClient>()
        } as ScraperContext<any, any>;

        startWithCredentialsStub.thenResolve({
            actionRequired: true,
            validActions: [{type: steamSession.EAuthSessionGuardType.DeviceConfirmation}, {type: steamSession.EAuthSessionGuardType.DeviceCode}]
        });

        td.when(LoginSession.prototype.submitSteamGuardCode(td.matchers.anything())).thenDo(() => {
            return eventHandlers['authenticated']();
        });

        await getLoginFunction(source, sessionExpiredMock)(context);
    });

    describe('event handlers', () => {
        let promise: Promise<any>;
        let state: ReturnType<typeof trackPromiseState>;

        beforeEach(async () => {
            startWithCredentialsStub.thenResolve({actionRequired: false});
            promise = getLoginFunction(source, sessionExpiredMock)(context);
            state = trackPromiseState(promise);
            await sleep(0); // next tick
        });

        it('event handlers are registered on loginSession for "timeout", "error", and "authenticated" events', () => {
            expect(eventHandlers).to.have.all.keys('authenticated', 'timeout', 'error');
            expect(state.isResolved).to.be.false;
        });

        it('eventHandler for "authenticated" resolves the login promise', async () => {
            await eventHandlers['authenticated']();
            await sleep(0); // next tick
            expect(state.isResolved).to.be.true;
            await promise;
        });

        it('eventHandler for "timeout" rejects the login promise with a LoginException', async () => {
            await eventHandlers['timeout']();
            await sleep(0); // next tick
            expect(state.isRejected).to.be.true;
            await shouldThrowAsync(async () => promise, new LoginException(LoginExceptionType.TIMEOUT_2FA), true);
        });

        it('eventHandler for "error" rejects the login promise with a LoginException', async () => {
            await eventHandlers['error']({eresult: EResult.InvalidPassword} as any as Error);
            await sleep(0); // next tick
            expect(state.isRejected).to.be.true;
            await shouldThrowAsync(async () => promise, new LoginException(LoginExceptionType.CREDENTIALS_INVALID), true);
        });
    });

    it('should correctly merge cookies in session during refresh', async function () {
        const originalSession: SteamSession = {
            cookies: [
                {domain: 'partner.steampowered.com', name: 'Foo', value: 'oldValue'} as Cookie,
                {domain: 'partner.steampowered.com', name: 'Bar', value: 'oldValue'} as Cookie
            ],
            refreshToken: 'dummyToken'
        };
        const scraperContext = {
            session: originalSession,
            progress: (message: string) => console.log(message)
        } as ScraperContext<any, SteamSession>;

        td.replace(LoginSession.prototype, 'getWebCookies', () => [
            `Bar=newValue; Max-Age=1000; Domain=originalDomain`,
            `Baz=newValue; Max-Age=1000; Domain=originalDomain`
        ]);

        let updatedSession = await refreshSessionCookies(scraperContext);

        expect(updatedSession).to.have.property('cookies');
        expect(updatedSession.cookies).to.deep.include.members([
            {domain: 'partner.steampowered.com', name: 'Foo', value: 'oldValue'},
            {domain: 'partner.steampowered.com', name: 'Bar', value: 'newValue', maxAge: 1000},
            {domain: 'partner.steampowered.com', name: 'Baz', value: 'newValue', maxAge: 1000},
            {domain: 'partner.steamgames.com', name: 'Bar', value: 'newValue', maxAge: 1000},
            {domain: 'partner.steamgames.com', name: 'Baz', value: 'newValue', maxAge: 1000}
        ]);

        td.replace(LoginSession.prototype, 'getWebCookies', () => [`Bar=newestValue; Domain=originalDomain`, `Baz=newestValue;  Domain=originalDomain`]);
        updatedSession = await refreshSessionCookies(scraperContext);

        expect(updatedSession.cookies).to.deep.include.members([
            {domain: 'partner.steampowered.com', name: 'Foo', value: 'oldValue'},
            {domain: 'partner.steampowered.com', name: 'Bar', value: 'newestValue'},
            {domain: 'partner.steampowered.com', name: 'Baz', value: 'newestValue'},
            {domain: 'partner.steamgames.com', name: 'Bar', value: 'newestValue'},
            {domain: 'partner.steamgames.com', name: 'Baz', value: 'newestValue'}
        ]);
    });
});
