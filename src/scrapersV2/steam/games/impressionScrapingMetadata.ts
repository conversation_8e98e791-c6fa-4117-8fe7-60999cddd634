import {Moment} from 'moment/moment';
import {NoProductsToDownloadException} from '../../../error/exceptions';
import {HTTPClient} from '../../../utils/http';
import {log} from '../../../utils/logger';
import {ProgressCallback} from '../../ProgressCallback';
import {SteamAppData} from './SteamAppData';
import {steamGamesCheerioRequest} from './steamGamesRequest';
import {getUrlPartAtPosition} from './urls';

const idPosition = 5;

//TODO this can be an internal interface after v1 is removed
export interface RawAppData {
    id: string;
    name: string;
}

interface DataObtainingScrapeContext {
    httpClient: HTTPClient;
    progress: ProgressCallback;
    startDate: moment.Moment;
    endDate: moment.Moment;
    ignoredProducts: string[];
}

async function impressionScrapingMetadata(httpClient: HTTPClient): Promise<RawAppData[]> {
    const appListPage = await steamGamesCheerioRequest('/apps/', {httpClient});
    const apps = appListPage('div.section > div.recent_app_row > div.recent_app_name > a');
    const appData: RawAppData[] = [];
    apps.map((_i, elem) => {
        const newVar = {
            id: getUrlPartAtPosition(elem.attribs.href, idPosition),
            name: elem.children[0]['data'].trim()
        };
        appData.push(newVar); //TODO ugly way to unwrap cheerio object and avoid circular json dependencies later on.
        return newVar;
    });
    return appData;
}

function getAppDataBetweenDates(appId: string, appName: string, startDate: Moment, endDate: Moment, startIndex: number): SteamAppData[] {
    const currentDate = startDate.clone();
    const appData: SteamAppData[] = [];
    let i = 1;
    while (currentDate.isSameOrBefore(endDate)) {
        appData.push({
            index: startIndex + i,
            id: appId,
            name: appName,
            date: currentDate.utc().clone()
        });
        currentDate.add(1, 'days');
        i++;
    }
    return appData;
}

function filterDeletedOrUnpublishedApps(appData: RawAppData[]): RawAppData[] {
    return appData.filter((item) => item.name !== '');
}

async function getAppsToDownload(httpClient: HTTPClient, ignoredProducts: string[]): Promise<RawAppData[]> {
    const scrapedApps = await impressionScrapingMetadata(httpClient);

    if (!scrapedApps.length) {
        throw new NoProductsToDownloadException();
    }
    const scrapedAppsAfterBasicFiltering = filterDeletedOrUnpublishedApps(scrapedApps);

    if (!ignoredProducts.length) {
        return scrapedAppsAfterBasicFiltering;
    }

    const filteredProducts = scrapedAppsAfterBasicFiltering.filter((rawAppData) => !ignoredProducts.includes(rawAppData.id));
    log.info(`Number of games after filtering: ${filteredProducts.length}`);
    if (!filteredProducts.length) {
        throw new NoProductsToDownloadException('All available products were ignored because of user configuration');
    }

    return filteredProducts;
}

export default async function getFullSetOfDataToScrape({httpClient, progress, startDate, endDate, ignoredProducts}: DataObtainingScrapeContext): Promise<SteamAppData[]> {
    progress('Gathering Steam applications data...');
    const appsToDownload = await getAppsToDownload(httpClient, ignoredProducts);
    progress(`Number of reported apps: ${appsToDownload.length}`);
    log.debug(`Reported apps data: ${JSON.stringify(appsToDownload)}`);

    return appsToDownload.reduce((result, data) => {
        const items = getAppDataBetweenDates(data.id, data.name, startDate, endDate, result.length);
        return [...result, ...items];
    }, []);
}
