import {expect} from 'chai';
import * as moment from 'moment';
import {Source} from '../../../src/dataTypes';
import {DateIsNotUTCException, Report} from '../../../src/scrapersV1/Report';

describe('Report :', function () {
    it('should be properly created when valid dates are provided', () => {
        const report = new Report(Source.META_RIFT_SALES, 'fakeFileName', moment().utc(), moment().utc());
        expect(report).to.not.be.null;
    });

    it('should throw error when creating with non utc start date', () => {
        expect(() => new Report(Source.META_RIFT_SALES, 'fakeFileName', moment(), moment().utc())).to.throw(new DateIsNotUTCException().message);
    });

    it('should throw error when creating with non utc end date', () => {
        expect(() => new Report(Source.META_RIFT_SALES, 'fakeFileName', moment().utc(), moment())).to.throw(new DateIsNotUTCException().message);
    });
});
