import * as fs from 'fs';
import {expect} from 'chai';
import * as moment from 'moment';
import {CLIArguments} from '../../../src/cli/arguments';
import {Source} from '../../../src/dataTypes';
import {FileExtension} from '../../../src/utils/files/FileExtension';
import {steamSuperhotOrgs} from '../expectedData';
import {createGetSourceSideOrgTest} from '../getSourceSideOrganizationTestGenerator';
import {CliArgumentsInTests, LOGIN_ARGS, getScrapeArgs} from '../utils/ArgsProvider';
import steamGenerateTestZipEntries from '../utils/steam/SteamGenerateTestZipEntries';
import {getFileFromZip, listZipContentNames, removeIfFilesExist, runCliMainWithConvertedArgs} from '../utils/Utils';

describe('Steam Sales runner should:', function () {
    const defaultStartDateString = '2020-01-01';
    const defaultEndDateString = '2020-02-01';
    const recentStartDate = moment.utc().subtract(4, 'days').format('YYYY-MM-DD');
    const recentEndDate = moment.utc().subtract(2, 'days').format('YYYY-MM-DD');
    const loginArgs = LOGIN_ARGS.for_steam_sales_fullpermissions;
    const scrapeArgs = getScrapeArgs(loginArgs, defaultStartDateString, defaultEndDateString, FileExtension.ZIP);
    const recentScrapeArgs = getScrapeArgs(loginArgs, recentStartDate, recentEndDate, FileExtension.ZIP);

    before(async function () {
        await removeIfFilesExist(scrapeArgs.testedFilePath!);
        await runCliMainWithConvertedArgs(loginArgs);
    });

    createGetSourceSideOrgTest(LOGIN_ARGS.for_steam_sales_fullpermissions, steamSuperhotOrgs, true);

    const entryAmountForSteamFileForTestPeriod = 1;

    function validateManifest(scrapeArgs: CliArgumentsInTests, startDateString: string, endDateString: string) {
        const manifest = getFileFromZip(scrapeArgs.testedFilePath, `additionalData_steam_sales_${startDateString}_${endDateString}.json`);
        const jsonManifest = JSON.parse(manifest[0].getData().toString());
        const jsonManifestFileMetadata: Array<Record<string, any>> = Object.values(jsonManifest.fileMetaData);
        expect(jsonManifest).to.have.property('scraper', 'steam_sales');
        expect(jsonManifest).to.have.property('startDate', `${startDateString}T00:00:00.000Z`);
        expect(jsonManifest).to.have.property('endDate', `${endDateString}T00:00:00.000Z`);
        const salesFiles = jsonManifestFileMetadata.filter((file) => file.contentType === 'sales');

        const inAppSalesFiles = jsonManifestFileMetadata.filter((file) => file.contentType === 'in-app-sales');
        expect(salesFiles.length).to.be.equal(1);
        expect(inAppSalesFiles.length).to.be.equal(0); //TODO change once in-app sales are downloaded

        jsonManifestFileMetadata
            .filter((file) => file.contentType === 'complementary-packages')
            .forEach((file) => {
                expect(file).to.have.property('packageId');
                expect(file).to.have.property('packageName');
                expect(file.checksum).to.deep.equal({unitsSold: -1});
            });
    }

    it('download STEAM_SALES zipped data and check number of files', async function () {
        await runCliMainWithConvertedArgs(scrapeArgs);
        expect(listZipContentNames(scrapeArgs.testedFilePath!)).to.be.deep.equal(
            steamGenerateTestZipEntries(Source.STEAM_SALES, defaultStartDateString, defaultEndDateString, entryAmountForSteamFileForTestPeriod)
        );

        validateManifest(scrapeArgs, defaultStartDateString, defaultEndDateString);
    });

    it('download latest STEAM_SALES zipped data and verify number of files', async function () {
        await runCliMainWithConvertedArgs(recentScrapeArgs);
        expect(listZipContentNames(recentScrapeArgs.testedFilePath!)).to.have.members(
            steamGenerateTestZipEntries(Source.STEAM_SALES, recentStartDate, recentEndDate, entryAmountForSteamFileForTestPeriod)
        );
    });

    it('download whatever utilizing refreshToken to generate new cookies', async function () {
        function prepareArgsWithSessionFileWithoutCookies(recentScrapeArgs: CLIArguments): CLIArguments {
            const sessionFilePath = recentScrapeArgs.sessionFile!;

            const newSessionFilePath = sessionFilePath.replace('.json', '-sales-without-cookies.json');
            fs.copyFileSync(sessionFilePath, newSessionFilePath);
            const sessionContent = JSON.parse(fs.readFileSync(newSessionFilePath, 'utf8'));
            fs.writeFileSync(newSessionFilePath, JSON.stringify({...sessionContent, cookies: []}));

            return {...recentScrapeArgs, sessionFile: newSessionFilePath};
        }

        await runCliMainWithConvertedArgs(prepareArgsWithSessionFileWithoutCookies(recentScrapeArgs));
        expect(listZipContentNames(recentScrapeArgs.testedFilePath!)).to.be.deep.equal(
            steamGenerateTestZipEntries(Source.STEAM_SALES, recentStartDate, recentEndDate, entryAmountForSteamFileForTestPeriod)
        );
    });
});
