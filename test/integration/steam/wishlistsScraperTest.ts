import {expect} from 'chai';
import * as moment from 'moment';
import {Source} from '../../../src/dataTypes';
import {FileExtension} from '../../../src/utils/files/FileExtension';
import {getLoginArgs, getScrapeArgs} from '../utils/ArgsProvider';
import {listZipContentNames, removeIfFilesExist, runCliMainWithConvertedArgs} from '../utils/Utils';

const skuList = [1261380, 1454840, 322500, 617830, 690040];

function generateSteamZipEntries(platform, dateFrom, dateTo): string[] {
    const entries: string[] = [`additionalData_${platform}_${dateFrom}_${dateTo}.json`];
    skuList.forEach((sku) => {
        for (let i = 0; i < 2; i++) {
            entries.push(`${platform}-${dateFrom}_${dateTo}-${sku}_${i}.csv`);
        }
    });
    return entries;
}
describe('Steam Wishlists runner should:', () => {
    const defaultStartDateString = '2020-01-01';
    const defaultEndDateString = '2020-02-01';
    const recentStartDate = moment.utc().subtract(7, 'days').format('YYYY-MM-DD');
    const recentEndDate = moment.utc().subtract(2, 'days').format('YYYY-MM-DD');
    const loginArgs = getLoginArgs(Source.STEAM_WISHLISTS, 'steam_sales_superhot_fullpermissions', 'steam_fullpermissions.json');
    const scrapeArgs = getScrapeArgs(loginArgs, defaultStartDateString, defaultEndDateString, FileExtension.ZIP);
    const recentScrapeArgs = getScrapeArgs(loginArgs, recentStartDate, recentEndDate, FileExtension.ZIP);

    before(async () => {
        await removeIfFilesExist(scrapeArgs.testedFilePath!);
        await runCliMainWithConvertedArgs(loginArgs);
    });

    it('should download STEAM_WISHLISTS zipped data and check number of files', async () => {
        await runCliMainWithConvertedArgs(scrapeArgs);
        expect(listZipContentNames(scrapeArgs.testedFilePath!)).to.have.members(
            generateSteamZipEntries(Source.STEAM_WISHLISTS, defaultStartDateString, defaultEndDateString)
        );
    });

    it('should download recent STEAM_WISHLISTS zipped data and check number of files', async () => {
        await runCliMainWithConvertedArgs(recentScrapeArgs);
        expect(listZipContentNames(recentScrapeArgs.testedFilePath!)).to.be.deep.equal(generateSteamZipEntries(Source.STEAM_WISHLISTS, recentStartDate, recentEndDate));
    });
});
