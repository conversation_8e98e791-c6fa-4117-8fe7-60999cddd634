import Axios, {AxiosError, AxiosResponse} from 'axios';
import * as sinon from 'sinon';
import {CustomException} from '../../src/error/CustomException';

export function stubAxios(responses: Array<AxiosResponse | AxiosError | Error | CustomException>): sinon.SinonStub {
    const requestStub = sinon.stub(Axios, 'request');
    responses.forEach((response, index) => {
        if ('response' in response || response instanceof Error || response instanceof CustomException) {
            requestStub.onCall(index).rejects(response);
        } else {
            requestStub.onCall(index).resolves(response);
        }
    });
    return requestStub;
}

export function createErrorResponse(data: any, status: number) {
    return <AxiosError>{response: {data, status, headers: {}}};
}
