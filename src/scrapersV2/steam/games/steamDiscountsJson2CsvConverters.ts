import * as fs from 'fs/promises';
import {unparse} from 'papaparse';
import {printTraceWithError} from '../../../cli/messaging';
import {buildFilePath} from '../../../utils/files/fileUtils';
import {log} from '../../../utils/logger';
import {DiscountInfo, DiscountManagementEntry} from './DiscountManagementData';

async function writeDataToCsv(filename: string, data: any[]) {
    await fs.writeFile(buildFilePath(filename), unparse(data));
    return filename;
}

async function basePricesToCsv(entries: DiscountManagementEntry[]) {
    return writeDataToCsv(
        'basePrices.csv',
        entries.flatMap((entry) =>
            Object.entries(entry.basePrices).map(([packageId, prices]) => ({
                publisherId: entry.publisherid,
                packageId,
                ...prices
            }))
        )
    );
}

async function discountEventsToCsv(entries: DiscountManagementEntry[]) {
    return writeDataToCsv(
        'discountEvents.csv',
        entries.flatMap((entry) =>
            entry.discountEvents.map((event) => ({
                publisherId: entry.publisherid,
                ...event
            }))
        )
    );
}

async function packageDiscountsToCsv(entries: DiscountManagementEntry[]) {
    return writeDataToCsv(
        'packageDiscounts.csv',
        entries.flatMap((entry) =>
            entry.packageDiscounts.map((discount) => ({
                publisherId: entry.publisherid,
                ...discount
            }))
        )
    );
}

async function packageIdsToCsv(entries: DiscountManagementEntry[]) {
    return writeDataToCsv(
        'packageIds.csv',
        entries.flatMap((entry) =>
            entry.packageIds.map((packageId) => ({
                publisherId: entry.publisherid,
                packageId
            }))
        )
    );
}

async function priceIncreaseTimesToCsv(entries: DiscountManagementEntry[]) {
    return writeDataToCsv(
        'priceIncreaseTimes.csv',
        entries.flatMap((entry) =>
            Object.entries(entry.priceIncreaseTimes).map(([packageId, time]) => ({
                publisherId: entry.publisherid,
                packageId,
                priceIncreaseTime: time
            }))
        )
    );
}

async function userinfoToCsv(entries: DiscountManagementEntry[]) {
    const data = entries.map((entry) => ({
        publisherId: entry.publisherid,
        ...entry.userinfo
    }));

    const normalizedData = data.map((entry) => {
        const normalizedCopy = {...entry} as Record<string, any>;
        if (entry.excludedContentDescriptors && Array.isArray(entry.excludedContentDescriptors)) {
            normalizedCopy.excludedContentDescriptors = JSON.stringify(entry.excludedContentDescriptors);
        }
        return normalizedCopy;
    });

    return writeDataToCsv('userinfo.csv', normalizedData);
}

async function discountHistoryToCsv(infos: DiscountInfo[]) {
    return writeDataToCsv('discountHistory.csv', infos);
}

export const convertToCSVsFiles = async (entries: DiscountManagementEntry[], discountInfos: DiscountInfo[]) => {
    type Args = DiscountManagementEntry[] | DiscountInfo[];

    const operations: Array<[(arg: Args) => Promise<string>, Args]> = [
        [basePricesToCsv, entries],
        [discountEventsToCsv, entries],
        [packageDiscountsToCsv, entries],
        [packageIdsToCsv, entries],
        [priceIncreaseTimesToCsv, entries],
        [userinfoToCsv, entries],
        [discountHistoryToCsv, discountInfos]
    ];

    const filenames: string[] = [];

    for (const [converter, data] of operations) {
        try {
            const filename = await converter(data);
            filenames.push(filename);
        } catch (error) {
            log.debug(`Conversion to CSV failed for: ${converter.name}.`);
            printTraceWithError(error);
        }
    }

    return filenames;
};
