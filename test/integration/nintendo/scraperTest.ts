import * as adm from 'adm-zip';
import {expect} from 'chai';
import * as moment from 'moment';
import {FileExtension} from '../../../src/utils/files/FileExtension';
import {createCheckSessionTest} from '../checkSessionTestGenerator';
import {createScrapeTest} from '../scrapeTestGenerator';
import {LOGIN_ARGS, getScrapeArgs} from '../utils/ArgsProvider';
import {removeIfFilesExist, runCliMainWithConvertedArgs} from '../utils/Utils';

describe('Nintendo runner:', () => {
    const defaultStartDateString = '2020-01-01';
    const defaultEndDateString = '2020-02-01';
    const recentStartDate = moment.utc().subtract(5, 'days').format('YYYY-MM-DD');
    const recentEndDate = moment.utc().subtract(3, 'days').format('YYYY-MM-DD');
    const loginArgs = LOGIN_ARGS.for_nintendo;
    const scrapeArgs = getScrapeArgs(loginArgs, defaultStartDateString, defaultEndDateString, FileExtension.ZIP);
    const recentScrapeArgs = getScrapeArgs(loginArgs, recentStartDate, recentEndDate, FileExtension.ZIP);

    before(async () => {
        await removeIfFilesExist(scrapeArgs.testedFilePath!);
        await runCliMainWithConvertedArgs(loginArgs);
    });

    createCheckSessionTest(LOGIN_ARGS.for_nintendo, {id: 'Data Aggregation'}, true);

    /**
     * Each Nintendo Switch file contains entries for ALL promotions/entries from this platform
     * That is why the number is specified as minimal expected entries. Each new promotion
     * will increase the row amount in all files (they will be 0 value rows but still)
     * In order to compensate we are checking for at least X entries rather than exactly X entries
     * WARNING in theory this number can also decrease (because of the same logic, removal of a promotion).
     * This was never observed but it is possible and should be investigated if a situation like that occurs.
     */
    it('should download NINTENDO_SWITCH zip file', async function () {
        await runCliMainWithConvertedArgs(scrapeArgs);

        const zipEntries = new adm(scrapeArgs.testedFilePath!).getEntries();
        const manifestFile = zipEntries.filter((entry) => entry.entryName === 'manifest.json')[0];
        const dataFiles = zipEntries.filter((entry) => entry.entryName !== 'manifest.json');

        const content = dataFiles[0].getData();
        const manifestContentAsJson = await JSON.parse(manifestFile.getData().toString('utf8'));

        expect(dataFiles.length).to.be.equal(1);
        expect(content.length).to.be.greaterThan(390);

        expect(manifestContentAsJson).to.be.deep.equal({
            dateFrom: '2020-01-01T00:00:00.000Z',
            dateTo: '2020-02-01T00:00:00.000Z'
        });
    });

    createScrapeTest('should download recent NINTENDO Data', recentScrapeArgs, [`nintendo_sales-${recentStartDate}_${recentEndDate}-1.csv`]);
});
