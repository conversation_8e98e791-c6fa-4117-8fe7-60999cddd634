import {assert} from 'chai';
import {getUrlPartAtPosition} from '../../../src/scrapersV2/steam/games/urls';
import {wrapInInputOutput} from '../../utils/testDataWrappers';

describe('Urls utils test', () => {
    describe('get url part at positon', () => {
        type Input = {url: string; position: number};
        type Output = string;

        [
            wrapInInputOutput<Input, Output>({url: '/correct/one/id', position: 3}, 'id'),
            wrapInInputOutput<Input, Output>({url: '/correct/id', position: 1}, 'correct'),
            wrapInInputOutput<Input, Output>({url: '/correct/one/id', position: 0}, ''),
            wrapInInputOutput<Input, Output>({url: 'foo/correct/one/id', position: 0}, 'foo'),
            wrapInInputOutput<Input, Output>({url: '', position: 0}, '')
        ].forEach((testCase) => {
            const {
                input: {position, url},
                output
            } = testCase;
            it(`should return portion no. ${position} of the url ${url}: ${output}`, async () => {
                assert.equal(getUrlPartAtPosition(url, position), output);
            });
        });

        [
            {url: '/correct/one/id', position: 7},
            {url: '/', position: 2},
            {url: undefined, position: 2},
            {url: null, position: 2}
        ].forEach((testCase: Input) => {
            const {position, url} = testCase;
            it(`should throw an error for position ${position} of ${url}`, async () => {
                assert.throws(() => getUrlPartAtPosition(url, position));
            });
        });
    });
});
