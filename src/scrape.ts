#!/usr/bin/env node
/* eslint-disable import/order */

// to properly init legacy config which is loaded on module import,
// parse CLI arguments before loading the rest of the app.

import {parseArguments} from './cli/arguments';
import {main} from './cli/cli';
import globalErrorHandler from './utils/globalErrorHandler';
import {mapToLegacyConfig} from './config/ConfigService';

const args = parseArguments(process.argv, 'scrape.exe');
mapToLegacyConfig(args);

main(args).catch(globalErrorHandler);
