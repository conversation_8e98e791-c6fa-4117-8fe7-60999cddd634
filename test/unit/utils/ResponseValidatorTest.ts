import {AxiosResponse} from 'axios';
import {expect} from 'chai';
import {validateResponseHasData} from '../../../src/utils/ResponseValidator';

describe('Response validator', () => {
    it('should throw error when response data is an empty string', async () => {
        const stubResponse = {data: ''};
        expect(() => validateResponseHasData(stubResponse as unknown as AxiosResponse)).to.throws();
    });

    it('should not throw error when response data is a non empty string', async () => {
        const stubResponse = {data: '1'};
        validateResponseHasData(stubResponse as unknown as AxiosResponse);
        expect(true).to.be.true;
    });
});
