import {Action} from './Action';
import {LoginResult} from './LoginResult';
import {Report} from './Report';

export class DownloadReportResult {
    private readonly reports: Report[] | undefined;
    private readonly customAction?: Action;
    private readonly loginErrorObject?: LoginResult;
    private readonly invalidAppIdError?: string;

    private constructor(reports: Report[] | undefined, loginError?: LoginResult, invalidAppIdError?: string, action?: Action) {
        this.reports = reports;
        this.loginErrorObject = loginError;
        this.customAction = action;
        this.invalidAppIdError = invalidAppIdError;
    }

    public static createReportResult(reports: Report[]): DownloadReportResult {
        return new DownloadReportResult(reports);
    }

    public static createLoginErrorResult(loginResult: LoginResult): DownloadReportResult {
        return new DownloadReportResult(undefined, loginResult);
    }

    public static createInvalidApplicationResult(invalidAppError: string): DownloadReportResult {
        return new DownloadReportResult(undefined, undefined, invalidAppError, Action.INVALID_APP_ID);
    }

    public isReport(): boolean {
        return !!this.reports;
    }

    public isInvalidAppException(): boolean {
        return this.customAction === Action.INVALID_APP_ID;
    }

    public isAuthenticationException(): boolean {
        return !!this.loginErrorObject;
    }

    public getAuthenticationException(): LoginResult | undefined {
        return this.loginErrorObject;
    }

    public getReports(): Report[] | undefined {
        return this.reports;
    }

    public getInvalidAppIdError(): string | undefined {
        return this.invalidAppIdError;
    }

    public getErrorString(): string {
        return this.customAction || this.loginErrorObject?.toString() || this.invalidAppIdError || 'no error';
    }
}
