vcr playback test:
    cache:
        key:
            files:
                - test/vcr/poetry.lock
        paths:
            - test/vcr/.venv
            - test/vcr/.poetry-cache
    stage: test
    interruptible: true
    retry: 2
    needs: ['build', 'lint']
    rules:
        - if: '$PIPELINE == "Nightly"'
          allow_failure: false
        - allow_failure: true

    script:
        - cd test/vcr
        - poetry config virtualenvs.in-project true && poetry config cache-dir $(pwd)/.poetry-cache
        - poetry install
        - poetry run pip install scrapers-vcr --index-url https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.com/api/v4/projects/45807331/packages/pypi/simple
        - poetry run black --check . || (echo "VCR code is not formatted properly! Please run 'black .' in test/vcr before committing." && exit 1)
        - ../../node_modules/.bin/nyc --no-clean poetry run pytest --vcr-mode=playback --junitxml=report.xml

    artifacts:
        paths:
            - .nyc_output
        reports:
            junit: test/vcr/report.xml
