import {SourceSideOrganization} from '../../dataTypes/SourceSideOrganization';
import {LogLevel} from '../../utils/logger';
import {CustomException} from '../CustomException';
import {errorTypes} from '../errorTypes';

export class MissingPermissionsException extends CustomException {
    constructor(input: SourceSideOrganization[]) {
        super({
            message: `Missing permissions for ${input.reduce((acc, x) => acc + ',' + x.name, '')} organizations`,
            suggestedAction: "You don't have enough permissions to scrape with those credentials. Please check your permissions or use different accounts.",
            telemetryLogLevel: LogLevel.ERROR,
            errorType: errorTypes.MISSING_PERMISSIONS,
            canBeFixedByRetry: false,
            additionalErrorData: {
                organizationsWithMissingPermissions: input
            }
        });
    }
}
