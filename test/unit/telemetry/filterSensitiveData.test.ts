import {expect} from 'chai';
import {CustomException} from '../../../src/error/CustomException';
import {errorTypes} from '../../../src/error/errorTypes';
import {filterSensitiveFields, stringifyAndFilterSensitiveFields} from '../../../src/telemetry/filterSensitiveData';

describe('sensitivity filter should filter out sensitive params', () => {
    it('should filter out sensitive fields from object', () => {
        const input = {
            name: 'name',
            password: 'password',
            apiSetupData: 'apiSetupData',
            clientId: 'clientId',
            tenantId: 'tenantId',
            totpSecret: 'totpSecret',
            cloudStorageBucket: 'cloudStorageBucket',
            'XSRF-COOKIE-NAME': 'xsrfCookieName',
            token: 'token',
            safe: {
                butNotReally: {
                    password: 'password',
                    thisIsFine: true,
                    safe: 0
                }
            }
        };
        const output = filterSensitiveFields(input);
        expect(output).deep.equal({
            name: 'name',
            safe: {
                butNotReally: {
                    thisIsFine: true,
                    safe: 0
                }
            }
        });
    });

    it('should return empty object if input is empty', () => {
        const output = filterSensitiveFields();
        expect(output).deep.equal({});
    });

    it('should filter out variations of sensitive fields names', () => {
        const input = {
            cookie: 'cookie',
            Cookie: 'Cookie',
            cloudStorageBucket: 'cloudStorageBucket',
            CloudStorageBucket: 'cloudStorageBucket',
            'cloud-storage-bucket': 'cloudStorageBucket',
            'CLOUD-STORAGE-BUCKET': 'cloudStorageBucket',
            cloud_storage_bucket: 'cloudStorageBucket'
        };
        const output = filterSensitiveFields(input);
        expect(output).deep.equal({});
    });

    it('should handle circular objects', () => {
        const circularObj: any = {};
        circularObj.circularRef = circularObj;
        circularObj.list = [circularObj, circularObj];
        circularObj.item = {
            property: 'test',
            notSafe: {
                password: 'password',
                thisIsFine: true,
                safe: 0
            }
        };
        const output = filterSensitiveFields(circularObj);
        expect(output).deep.equal({
            circularRef: '[Circular ~]',
            item: {
                notSafe: {
                    safe: 0,
                    thisIsFine: true
                },
                property: 'test'
            },
            list: ['[Circular ~]', '[Circular ~]']
        });
    });

    it('should iterate over all objects in an array', () => {
        const input = {
            array: [
                {
                    safe: true,
                    password: 'password'
                },
                {
                    safe: true,
                    password: 'password'
                }
            ]
        };
        const output = filterSensitiveFields(input);
        expect(output).deep.equal({
            array: [
                {
                    safe: true
                },
                {
                    safe: true
                }
            ]
        });
    });
});

describe('stringifyAndFilterSensitiveFields should:', () => {
    it('should stringify simple object', () => {
        expect(stringifyAndFilterSensitiveFields({a: 1})).to.be.equal('{"a":1}');
    });

    it('should stringify error', () => {
        const error = new Error('test');
        error.stack = undefined; // remove stack to make tests deterministic
        expect(stringifyAndFilterSensitiveFields(error)).to.be.equal('{"name":"Error","message":"test"}');
    });

    it('should produce string with all non-enumerable error properties', () => {
        const error = new Error('test');
        const parsed = JSON.parse(stringifyAndFilterSensitiveFields(error));
        ['stack', 'name', 'message'].forEach((key) => {
            expect(parsed).to.have.property(key);
        });
    });

    it('should stringify custom exception', () => {
        const error = new CustomException({
            message: 'test',
            suggestedAction: 'user-friendly-test',
            errorType: errorTypes.CONFIGURATION_ISSUE
        });
        expect(stringifyAndFilterSensitiveFields(error)).to.be.equal(
            '{"telemetryLogLevel":"error","message":"test","suggestedAction":"Please try again. If this issue persists, please contact IndieBI support.","errorType":"UNEXPECTED_ERROR","userFriendlyMessage":"user-friendly-test"}'
        );
    });

    it('should stringify custom exception with original error', () => {
        const error = new Error('test');
        error.stack = undefined; // remove stack to make tests deterministic

        const exception = new CustomException({
            message: 'test',
            suggestedAction: 'user-friendly-test',
            error,
            errorType: errorTypes.CONFIGURATION_ISSUE
        });

        expect(stringifyAndFilterSensitiveFields(exception)).to.be.equal(
            '{"telemetryLogLevel":"error","message":"test","suggestedAction":"Please try again. If this issue persists, please contact IndieBI support.","originalError":{"name":"Error","message":"test"},"errorType":"UNEXPECTED_ERROR","userFriendlyMessage":"user-friendly-test"}'
        );
    });

    it('should stringify circular object', () => {
        const circularObj: any = {};
        circularObj.circularRef = circularObj;
        circularObj.list = [circularObj, circularObj];
        circularObj.item = {
            property: 'test'
        };
        expect(stringifyAndFilterSensitiveFields(circularObj)).to.be.equal(
            '{"circularRef":"[Circular ~]","list":["[Circular ~]","[Circular ~]"],"item":{"property":"test"}}'
        );
    });

    it('should stringify object and remove sensitive fields', () => {
        const input = {
            name: 'name',
            password: 'password',
            apiSetupData: 'apiSetupData',
            clientId: 'clientId',
            tenantId: 'tenantId',
            totpSecret: 'totpSecret',
            cloudStorageBucket: 'cloudStorageBucket',
            token: 'token',
            safe: {
                butNotReally: {
                    password: 'password',
                    thisIsFine: true,
                    safe: 0
                }
            }
        };
        expect(stringifyAndFilterSensitiveFields(input)).to.be.equal('{"name":"name","safe":{"butNotReally":{"thisIsFine":true,"safe":0}}}');
    });
});
