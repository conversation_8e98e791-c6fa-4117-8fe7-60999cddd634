import {expect} from 'chai';
import {CliArgumentsInTests} from '../../utils/ArgsProvider';
import {listZipContentNames, runCliMainWithConvertedArgs} from '../../utils/Utils';

export function itShouldDownloadZippedDataAndCheckFileFormat(
    scrapeArgs: CliArgumentsInTests,
    defaultStartDateString: string,
    defaultEndDateString: string
): Mocha.Func | undefined {
    return async function () {
        await runCliMainWithConvertedArgs(scrapeArgs);
        expect(listZipContentNames(scrapeArgs.testedFilePath!)).to.be.deep.equal([
            `${scrapeArgs.source}-${defaultStartDateString}_${defaultEndDateString}-SUPERHOT-VR.csv`,
            'manifest.json'
        ]);
    };
}
