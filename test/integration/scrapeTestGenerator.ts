import {IZipEntry} from 'adm-zip';
import * as adm from 'adm-zip';
import {expect} from 'chai';
import {Report} from '../../src/scrapersV1/Report';
import {IReportManifest} from '../../src/scrapersV2/ReportManifest';
import {CliArgumentsInTests, generateApiToken} from './utils/ArgsProvider';
import {runCliMainWithConvertedArgs, zipEntryToString} from './utils/Utils';

type ScrapeTestExpectFunction = (dataFiles: IZipEntry[], manifest: IReportManifest) => void | Promise<void>;

export function createScrapeTest(testDescription: string, scrapeArgs: CliArgumentsInTests, expectedFileNames: string[] = [], expectFn?: ScrapeTestExpectFunction) {
    return it(testDescription, async function () {
        scrapeArgs.apiToken = await generateApiToken();

        const result = (await runCliMainWithConvertedArgs(scrapeArgs)) as Report[];
        expect(result).to.have.lengthOf(1);
        expect(scrapeArgs.testedFilePath).to.contain(result[0].reportFileName);

        // Legacy scrapers uses raw csv files, zip with manifest is default for all newer scrapers
        if (!scrapeArgs.testedFilePath?.endsWith('zip')) return;

        const zipEntries = new adm(scrapeArgs.testedFilePath!).getEntries();
        expect(zipEntries.map(({entryName}) => entryName)).to.have.members([...expectedFileNames, 'manifest.json']);

        if (expectFn) {
            const manifestFile = zipEntries.filter((entry) => entry.entryName === 'manifest.json')[0];
            const dataFiles = zipEntries.filter((entry) => entry.entryName !== 'manifest.json');
            const manifest: IReportManifest = await JSON.parse(zipEntryToString(manifestFile));
            await expectFn(dataFiles, manifest);
        }
    });
}
