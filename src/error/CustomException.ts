import {LogLevel} from '../utils/logger';
import {ErrorType} from './errorTypes';

interface CustomExceptionParameters {
    message: string;
    suggestedAction?: string;
    error?: Error | CustomException;
    errorType: ErrorType;
    canBeFixedByRetry?: boolean;
    telemetryLogLevel?: LogLevel;
    additionalErrorData?: Record<string, any>;
    errorCode?: string;
}

export const defaultSuggestedAction = 'Please try again. If this issue persists, please contact IndieBI support.';

export class CustomException extends Error {
    /**
     * Used for conveying self-help information to the user OR hiding information from the original message that the user should not see.
     * TODO this should be renamed but it is used in electron in this form
     */
    readonly suggestedAction: string;
    readonly errorType: ErrorType;
    readonly originalError?: any;
    /**
     * Some errors are recoverable
     * This flag indicates if the app should try to scrape data again, or should the process be stopped.
     */
    readonly canBeFixedByRetry: boolean;
    /**
     * Some errors are errors for the customers but not for us (for example invalid credentials)
     */
    readonly telemetryLogLevel: LogLevel;
    /**
     * Sometimes it's valuable to not only log the error but also some additional data.
     * For example:
     *  When setting up a scraper and finding out that his account does not have enough permissions
     *  we could use this field to tell exactly which of the accounts he does have access too is/are
     *  the invalid ones.
     */
    readonly additionalErrorData?: Record<string, any>;

    constructor({telemetryLogLevel, message, error, suggestedAction, canBeFixedByRetry, errorType, additionalErrorData}: CustomExceptionParameters) {
        super(message);
        this.telemetryLogLevel = telemetryLogLevel || LogLevel.ERROR;
        this.suggestedAction = suggestedAction || `${message} - ${defaultSuggestedAction}`;
        this.originalError = error;
        this.errorType = errorType;
        this.canBeFixedByRetry = canBeFixedByRetry ?? true;
        this.additionalErrorData = additionalErrorData;
    }
}
