import {HTTPClient} from '../../../utils/http';
import {SteamOrganization} from '../common/SteamSession';
import {steamPoweredRequest, steamPoweredUrl} from './steamPoweredRequest';

export async function detectLowPermission(httpClient: HTTPClient, org: SteamOrganization) {
    const {
        status,
        headers: {location}
    } = await steamPoweredRequest('/', {httpClient, org, isStream: false});
    return status === 302 && location === `${steamPoweredUrl}/app/top/`;
}
