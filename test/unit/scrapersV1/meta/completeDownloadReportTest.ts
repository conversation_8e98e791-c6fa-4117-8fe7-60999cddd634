import * as chai from 'chai';
import * as chaiAsPromised from 'chai-as-promised';
import {match, restore, stub} from 'sinon';
import * as td from 'testdouble';
import {Browser} from '../../../../src/browser/Browser';
import * as configService from '../../../../src/config/ConfigService';
import {Source} from '../../../../src/dataTypes';
import {NoProductsToDownloadException, UnableToDownloadReportException} from '../../../../src/error/exceptions';
import * as OculusConstants from '../../../../src/scrapersV1/httpDownloaders/meta/MetaConstants';
import {GraphQLPersistedQuery} from '../../../../src/scrapersV1/httpDownloaders/meta/MetaConstants';
import {MetaReportDownloader} from '../../../../src/scrapersV1/httpDownloaders/meta/MetaReportDownloader';
import * as zip from '../../../../src/scrapersV2/zip';
import * as fileUtils from '../../../../src/utils/files/fileUtils';
import * as testData from './testData';
import {graphqlRequestMatcher} from './testData';

chai.use(chaiAsPromised);
const {expect} = chai;

describe('Meta scraper - completeDownloadReport', () => {
    const {startDate, endDate, validLogin, validPassword, source} = testData;

    beforeEach(() => {
        stub(fileUtils, 'zipFiles').resolves(undefined);
        stub(zip, 'packReportsToZip').resolves(undefined);
        td.replace(configService, 'downloadDirPath', () => '.private');
    });

    afterEach(() => {
        restore();
        td.reset();
    });

    const prepareFakeBrowser = (): Browser => {
        const fakeBrowser = td.object<Browser>();

        td.when(fakeBrowser.elementExists(OculusConstants.manageAppsListLinksSelector)).thenResolve(true);
        td.when(fakeBrowser.elementExists(OculusConstants.userSettingsAvailableXPathSelector)).thenResolve(true);
        td.when(fakeBrowser.xPathElementExistsAfterTime(OculusConstants.accessibleAppsXPathSelector)).thenResolve(true);

        testData.prepareFakeCookiesAndMetaOrganizations(fakeBrowser);

        return fakeBrowser;
    };

    it('should throw an error when not found any apps with lifetime revenue', async () => {
        const fakeBrowser = prepareFakeBrowser();
        td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_APP_LIFETIME_REVENUE))).thenResolve({
            data: {app_store_item: {overview_analytics_lifetime_data: null}}
        });

        const oculusReportDownloader = new MetaReportDownloader({
            login: validLogin,
            password: validPassword,
            browser: fakeBrowser,
            source
        });

        await expect(oculusReportDownloader.completeReportDownload(startDate, endDate)).to.be.rejectedWith(NoProductsToDownloadException);
    });

    it('should throw an error when another error occurred while downloading a file', async () => {
        const fakeBrowser = prepareFakeBrowser();
        td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_APP_LIFETIME_REVENUE))).thenResolve({
            data: {
                app_store_item: {
                    overview_analytics_lifetime_data: {
                        lifetime_revenue: 123
                    }
                }
            }
        });

        const oculusReportDownloader = new MetaReportDownloader({
            login: validLogin,
            password: validPassword,
            browser: fakeBrowser,
            source: Source.META_RIFT_SALES
        });

        await expect(oculusReportDownloader.completeReportDownload(startDate, endDate)).to.be.rejectedWith(UnableToDownloadReportException);
    });

    it('should throw an error when there are no apps to download', async () => {
        const fakeBrowser = prepareFakeBrowser();
        testData.prepareFakeCookiesAndMetaOrganizations(fakeBrowser);

        td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_APP_LIFETIME_REVENUE))).thenResolve({
            data: {app_store_item: {overview_analytics_lifetime_data: null}}
        });

        const oculusReportDownloader = new MetaReportDownloader({
            login: validLogin,
            password: validPassword,
            browser: fakeBrowser,
            source
        });

        await expect(oculusReportDownloader.completeReportDownload(startDate, endDate)).to.be.rejectedWith(NoProductsToDownloadException);
    });

    it('should mark the report as no sales when there are no sales for all apps in given time period', async () => {
        const fakeBrowser = prepareFakeBrowser();
        td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_APP_LIFETIME_REVENUE))).thenResolve({
            data: {app_store_item: {overview_analytics_lifetime_data: {lifetime_revenue: 123}}}
        });
        td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_APP_REAL_TIME))).thenResolve(
            testData.noSalesValidResponse
        );

        const oculusReportDownloader = new MetaReportDownloader({
            login: validLogin,
            password: validPassword,
            browser: fakeBrowser,
            source: Source.META_RIFT_SALES
        });

        const result = await oculusReportDownloader.completeReportDownload(startDate, endDate);

        expect(result[0].noData).to.be.true;
    });

    it('should support countries with comma in the full name', async () => {
        const fakeBrowser = prepareFakeBrowser();
        td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_APP_LIFETIME_REVENUE))).thenResolve({
            data: {app_store_item: {overview_analytics_lifetime_data: {lifetime_revenue: 123}}}
        });
        td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_APP_REAL_TIME))).thenResolve(
            testData.commaInCountryNameValidResponse
        );

        const saveFileStub = stub(fileUtils, 'saveFileToDownloads');

        const oculusReportDownloader = new MetaReportDownloader({
            login: validLogin,
            password: validPassword,
            browser: fakeBrowser,
            source: Source.META_RIFT_SALES
        });

        const result = await oculusReportDownloader.completeReportDownload(startDate, endDate);
        const content = 'Date,Country,App Sales Revenue (USD),Copies Sold,Active Users\n2025-01-01,"Congo, Democratic Republic of the",$9.99,1,0\n';

        expect(result[0].noData).to.be.false;
        expect(saveFileStub).to.have.been.calledWith(match.string, content);
    });

    it('should mark the report as no sales when there are no sales for all apps in given LEGACY time period', async () => {
        const fakeBrowser = prepareFakeBrowser();
        td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_APP_LIFETIME_REVENUE))).thenResolve({
            data: {app_store_item: {overview_analytics_lifetime_data: {lifetime_revenue: 123}}}
        });
        td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_APP_UNITS_SOLD_LEGACY_DASHBOARD))).thenResolve({
            data: {app_store_item: {revenue_analytics_data: {app_copies_sold: {total_copies_sold: 0}}}}
        });
        td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_APP_REVENUE_LEGACY_DASHBOARD))).thenResolve(
            testData.noSalesLegacyDashboardValidResponse
        );

        const oculusReportDownloader = new MetaReportDownloader({
            login: validLogin,
            password: validPassword,
            browser: fakeBrowser,
            source: Source.META_RIFT_SALES
        });

        // consider 2024 data obtainable only by legacy dashboard
        const legacyStartDate = startDate.clone().subtract(365, 'day');
        const legacyEndDate = endDate.clone().subtract(365, 'day');

        const result = await oculusReportDownloader.completeReportDownload(legacyStartDate, legacyEndDate);

        expect(result[0].noData).to.be.true;
    });

    it('should use legacy dashboards when request is for data before meta cutoff', async () => {
        const fakeBrowser = prepareFakeBrowser();
        td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_APP_LIFETIME_REVENUE))).thenResolve({
            data: {app_store_item: {overview_analytics_lifetime_data: {lifetime_revenue: 123}}}
        });
        td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_APP_UNITS_SOLD_LEGACY_DASHBOARD))).thenResolve({
            data: {app_store_item: {revenue_analytics_data: {app_copies_sold: {total_copies_sold: 123}}}}
        });
        td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_APP_REVENUE_LEGACY_DASHBOARD))).thenResolve(
            testData.salesLegacyDashboardValidResponse
        );

        const saveFileStub = stub(fileUtils, 'saveFileToDownloads');

        const oculusReportDownloader = new MetaReportDownloader({
            login: validLogin,
            password: validPassword,
            browser: fakeBrowser,
            source: Source.META_RIFT_SALES
        });

        // consider 2024 data obtainable only by legacy dashboard
        const legacyStartDate = startDate.clone().subtract(365, 'day');
        const legacyEndDate = endDate.clone().subtract(365, 'day');

        const result = await oculusReportDownloader.completeReportDownload(legacyStartDate, legacyEndDate);
        const content = 'Date,Country,App Sales Revenue (USD),Copies Sold,Active Users\n2024-01-10,US,$1000.123,100,1\n2024-01-11,ZA,$230.123,23,1\n';

        expect(result[0].noData).to.be.false;
        expect(saveFileStub).to.have.been.calledWith(match.string, content);
    });
});
