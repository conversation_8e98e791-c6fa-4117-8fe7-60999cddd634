from scrapers_vcr.util.scraper_runner import ScraperRunConfig


def test_can_get_manual_login_details(default_scraper: ScraperRunConfig):
    default_scraper.source = "gog_sales"
    result = default_scraper.get_manual_login_details()
    result.expect_success()
    result_data = result.data
    assert result_data
    assert result_data["url"] == "https://partners.gog.com/charts/sales-summary"
    assert result_data["successSelector"] == {"value": "#results-container", "type": "css"}
