#!/bin/bash

if [ "$#" -ne 1 ] || [[ "$1" == "-h" ]] || [[ "$1" == "--help" ]]; then
    echo "Usage: $0 <source>"
    exit 1
fi

SOURCE=$1

if [[ ! -f .env ]]; then
    echo ".env file not found. Exiting."
    exit 1
fi

source .env

REQUIRED_VARS=("DATA_DIR" "SCRAPE_FROM_DATE" "SCRAPE_TO_DATE" "SCRAPER_API_URL" "INDIEBI_JWT" "CHROME_PATH")
for var in "${REQUIRED_VARS[@]}"; do
    if [[ -z "${!var}" ]]; then
        echo "$var is not set in .env file. Exiting."
        exit 1
    fi
done

SOURCE_UPPERCASE=$(echo $SOURCE | tr '[:lower:]' '[:upper:]')
CREDENTIALS_VAR_NAME="${SOURCE_UPPERCASE}_CREDENTIALS"
CREDENTIALS="${!CREDENTIALS_VAR_NAME}"

if [ -z "$CREDENTIALS" ]; then
    echo "Credentials for $SOURCE are not defined in .env file. Exiting."
    exit 1
fi


DATA_DIR_PATH=$(pwd)/$DATA_DIR
SESSION_FILE=$DATA_DIR_PATH/sessions/$SOURCE.json
REPORT_PATH=$DATA_DIR_PATH/reports/$SOURCE

if [[ ! -d "$DATA_DIR" ]]; then
    echo "$DATA_DIR does not exist. Creating it."
    mkdir -p "$DATA_DIR"
fi

mkdir -p $REPORT_PATH

node -r ts-node/register --inspect ./src/scrape.ts \
    scrape \
    --source=$SOURCE \
    --from=$SCRAPE_FROM_DATE \
    --to=$SCRAPE_TO_DATE \
    --output=json \
    --apiUrl=$SCRAPER_API_URL \
    --apiToken="$INDIEBI_JWT" \
    --chromePath="$CHROME_PATH" \
    --sessionFile="$SESSION_FILE" \
    --credentials="$CREDENTIALS" \
    --reportPath="$REPORT_PATH" \
    --headless=false
