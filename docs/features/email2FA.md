We're using Mandrill Inbound API to receive emails from platforms with 2FA Codes. `indiebi-scrapper-api` does most of the work of receiving Mandrill webhooks and parsing the emails, then saving the codes to Redis. The scraper client polls the API for the code, right after seeing a prompt for a 2FA code during a login session. See [src/apiCommunication/emailAuthCode.ts](../../src/apiCommunication/emailAuthCode.ts) for implementation details.

![](../images/stub.jpg)
