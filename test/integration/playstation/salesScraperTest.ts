import {expect} from 'chai';
import * as moment from 'moment';
import {Source} from '../../../src/dataTypes';
import {FileExtension} from '../../../src/utils/files/FileExtension';
import {createCheckSessionTest} from '../checkSessionTestGenerator';
import {createGetSourceSideOrgTest} from '../getSourceSideOrganizationTestGenerator';
import {createScrapeTest} from '../scrapeTestGenerator';
import {LOGIN_ARGS, getScrapeArgs} from '../utils/ArgsProvider';
import {removeIfFilesExist, runCliMainWithConvertedArgs} from '../utils/Utils';
import {expectedPlayStationLoginResult, expectedPlayStationOrganizationsResult} from './common';

describe('PlayStation runner:', function () {
    const defaultStartDateString = '2021-02-28';
    const defaultEndDateString = '2021-03-03';
    const recentStartDate = moment.utc().subtract(3, 'days').format('YYYY-MM-DD');
    const recentEndDate = moment.utc().subtract(5, 'days').format('YYYY-MM-DD');
    const loginArgs = LOGIN_ARGS.for_playstation(Source.PLAYSTATION_SALES);
    const scrapeArgs = getScrapeArgs(loginArgs, defaultStartDateString, defaultEndDateString, FileExtension.ZIP);
    const recentScrapeArgs = getScrapeArgs(loginArgs, recentStartDate, recentEndDate, FileExtension.ZIP);

    before(async function () {
        await removeIfFilesExist(scrapeArgs.testedFilePath!);
        await runCliMainWithConvertedArgs(loginArgs);
    });

    createCheckSessionTest(loginArgs, expectedPlayStationLoginResult, true);

    createGetSourceSideOrgTest(loginArgs, expectedPlayStationOrganizationsResult, true);

    createScrapeTest(
        'should download PlayStation zipped data',
        scrapeArgs,
        [`${Source.PLAYSTATION_SALES}-${defaultStartDateString}_${defaultEndDateString}.json`],
        async (dataFiles) => {
            expect(dataFiles).to.be.an.instanceof(Array).and.have.lengthOf(1);

            const {numRows, numColumns} = await JSON.parse(dataFiles[0].getData().toString('utf8'));
            expect(numRows).to.be.equal(844);
            expect(numColumns).to.be.equal(35);
        }
    );
    createScrapeTest('should download recent PlayStation zipped data', recentScrapeArgs, [`${Source.PLAYSTATION_SALES}-${recentStartDate}_${recentEndDate}.json`]);
});
