import * as http from 'http';
import * as https from 'https';
import axios, {AxiosRequestConfig, AxiosResponse} from 'axios';
import {AxiosInstance} from 'axios/index';
import {userAgent} from '../browserV2/BrowserV2';
import {requestTimeout} from '../config/ConfigService';
import {proxyUrlToAxiosProxyConfig, rewriteUrlAndCreateVCRHeaders} from '../utils/http';

let axiosInstance: AxiosInstance | null = null;

export function initAxiosInstance(apiUrl: string) {
    axiosInstance = axios.create({
        baseURL: apiUrl,
        timeout: requestTimeout,
        httpAgent: new http.Agent({keepAlive: true}),
        httpsAgent: new https.Agent({keepAlive: true})
    });
}

function getAxiosInstance(): AxiosInstance {
    if (!axiosInstance) {
        throw new Error('Axios instance not initialized');
    }
    return axiosInstance;
}

function injectDefaultBrowserLikeHeadersToConfig(config: AxiosRequestConfig | undefined) {
    const headers: any = config?.headers || {};
    return {
        ...config,
        headers: {
            ...headers,
            'User-Agent': userAgent,
            'Accept-Language': 'en-US',
            'Accept-encoding': 'gzip, deflate, br'
        }
    };
}

export async function get<T = any, R = AxiosResponse<T>, D = any>(url: string, config?: AxiosRequestConfig<D>): Promise<R> {
    const instance = getAxiosInstance();
    if (!instance.defaults.proxy) {
        return instance.get(url, injectDefaultBrowserLikeHeadersToConfig(config));
    }
    const {proxyFriendlyUrl, vcrHeaders} = rewriteUrlAndCreateVCRHeaders(true, instance.defaults.baseURL + url, false);
    const newConfig = {...config, headers: {...config?.headers, ...vcrHeaders}};
    return instance.get(proxyFriendlyUrl, injectDefaultBrowserLikeHeadersToConfig(newConfig));
}

export async function post<T = any, R = AxiosResponse<T>, D = any>(url: string, data?: D, config?: AxiosRequestConfig<D>): Promise<R> {
    return getAxiosInstance().post(url, data, injectDefaultBrowserLikeHeadersToConfig(config));
}

export function useProxy(proxyUrl?: string): void {
    getAxiosInstance().defaults.proxy = proxyUrlToAxiosProxyConfig(proxyUrl);
}
