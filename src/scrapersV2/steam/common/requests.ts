import {Stream} from 'stream';
import {AxiosResponse} from 'axios';
import * as cheerio from 'cheerio';
import {Moment} from 'moment';
import {Source} from '../../../dataTypes';
import {CustomException} from '../../../error/CustomException';
import {errorTypes} from '../../../error/errorTypes';
import {InvalidContentTypeException, LoginException, LoginExceptionType} from '../../../error/exceptions';
import {TemporaryPortalException} from '../../../error/exceptions/TemporaryPortalException';
import {HTTPClient, HTTPResponse, HttpMethod} from '../../../utils/http';
import {LogLevel, log} from '../../../utils/logger';
import {validateReportResponseHeaders, validateResponseHasData} from '../../../utils/ResponseValidator';
import {sleep} from '../../../utils/sleep';
import {steamGamesUrl} from '../games/steamGamesRequest';
import {steamPoweredUrl} from '../powered/steamPoweredRequest';
import {FileMetaData} from './FileMetaData';
import {SteamOrganization} from './SteamSession';

/**
 * @deprecated
 */
export type SteamAdditionalData = {
    scraper: Source;
    fileMetaData: {[p: string]: FileMetaData};
    startDate: Moment;
    endDate: Moment;
};
export const steamDateFormat = 'YYYY-MM-DD';

export interface SteamRequestOptions {
    isStream?: boolean;
    extraCookies?: Record<string, string>;
    httpClient: HTTPClient;
    followRedirects?: boolean;
    org?: SteamOrganization;
}

export interface BaseSteamRequestOptions extends SteamRequestOptions {
    baseUrl: string;
    path: string;
    isStream: boolean;
    followRedirects: boolean;
    method?: HttpMethod;
    formData?: Record<string, string>;
    customResponseHeaderValidation?: (response: AxiosResponse<Stream> | HTTPResponse) => void;
}

const MAX_ATTEMPTS = 5;
const REQUEST_RETRY_DELAY = 2000;

//TODO this could be refactored since it's quite long
export async function steamRequest<T = any>(
    {baseUrl, method, path, isStream, extraCookies, httpClient, followRedirects, formData}: BaseSteamRequestOptions,
    attempt = 0
): Promise<AxiosResponse<T>> {
    if (attempt > MAX_ATTEMPTS) {
        //TODO investigate if we should not use retryaction from utils
        throw new TemporaryPortalException(`Encountered Steam issue which was not resolved by multiple retries`);
    }

    const response = await httpClient.request<T>({
        url: `${baseUrl}${path}`,
        method: method || 'GET',
        followRedirects,
        stream: isStream,
        extraCookies,
        formData: method === 'POST' ? formData : undefined
    });

    /*
        Handle case when user is redirected to page informing him that he is logged out (example: steam impressions cheerio request in scrape with invalid cookies)
     */
    if (response.status === 200 && new URL(response.config.url!).searchParams.get('goto') === path) {
        log.debug('Received redirect to different page', {url: `${baseUrl}${path}`, responseUrl: response.config.url});
        throw new LoginException(LoginExceptionType.SESSION_EXPIRED);
    }

    if (response.status >= 300 && response.status < 400) {
        const location = response.headers['location'];
        if (location && location.startsWith(`${baseUrl}/login`)) {
            const {data} = await httpClient.get<T>({
                url: location,
                followRedirects: true,
                stream: false,
                extraCookies
            });

            const page = cheerio.load(data as any);

            const logoutButton = page(getLoggedInSelector(baseUrl));
            // The initial request failed but after checking the location page we found out that the user is still logged in,
            // so we retry the initial request
            if (logoutButton && logoutButton.length) {
                log.debug('Steam request failed, retrying', {attempt, url: `${baseUrl}${path}`, location});
                await sleep(REQUEST_RETRY_DELAY);
                return steamRequest<T>({baseUrl, method: 'GET', path, isStream, extraCookies, httpClient, followRedirects}, attempt + 1);
            }
            const errorMessage = page('#error_display').text().trim();

            // "The account you logged in to does not have access to this site." error is visible (or any other of similar mechanisms)
            if (errorMessage && errorMessage !== '') {
                log.debug('Received error message on Steam page', {errorMessage});
                throw new TemporaryPortalException(`Encountered Steam issue: ${errorMessage}`);
            }
            log.debug('Received redirect to login page', {url: `${baseUrl}${path}`, targetLocation: location});
            throw new LoginException(LoginExceptionType.SESSION_EXPIRED);
        }
    }

    if (response.status == 401 || response.status == 403) {
        log.debug('Received 401/403 status code', {url: `${baseUrl}${path}`, status: response.status});
        throw new LoginException(
            LoginExceptionType.BANNED,
            'Your IP is temporarily blocked by Steam because of too many unsuccessful login attempts. Try again in a few minutes to an hour. If after this time the issue still occurs please contact IndieBI support.'
        );
    }

    if (response.status === 429) {
        log.debug('Received 429 status code, too many requests', {url: `${baseUrl}${path}`, status: response.status});
        await sleep(REQUEST_RETRY_DELAY * 16); // 40 requests per minute is the limit, so we wait 30 + 2 seconds before retrying
        return steamRequest<T>({baseUrl, method: 'GET', path, isStream, extraCookies, httpClient, followRedirects}, attempt + 1);
    }

    if (response.status == 200 && response.headers['content-type'] == 'text/html; charset=UTF-8' && !response.data) {
        log.debug('Received empty response', {url: `${baseUrl}${path}`});
        throw new TemporaryPortalException('Steam returned empty response');
    }

    return response;
}

const getLoggedInSelector = (baseUrl: string) => {
    switch (baseUrl) {
        case steamPoweredUrl:
            return 'a[href="https://partner.steampowered.com/login/logout"]';
        case steamGamesUrl:
            return 'a.header_logout_btn';
        default:
            throw new Error('Unknown Steam URL');
    }
};

export async function steamCheerioRequest(baseUrl: string, path: string, http: HTTPClient, extraCookies: Record<string, string>): Promise<cheerio.CheerioAPI> {
    const response = await steamRequest({
        baseUrl,
        method: 'GET',
        path,
        httpClient: http,
        extraCookies,
        isStream: false,
        followRedirects: true
    });
    return cheerio.load(response.data);
}

export async function requestSteamFileWithCorruptionHandling(baseSteamRequestOptions: BaseSteamRequestOptions) {
    try {
        const response = await steamRequest(baseSteamRequestOptions);
        if (baseSteamRequestOptions.customResponseHeaderValidation) {
            baseSteamRequestOptions.customResponseHeaderValidation(response);
        } else {
            validateReportResponseHeaders(response);
        }
        validateResponseHasData(response);
        return response;
    } catch (error) {
        if (error instanceof InvalidContentTypeException) {
            throw new CustomException({
                message: 'The downloaded file is corrupted, possibly due to another user logged in to scraper platform account.',
                suggestedAction: 'If this issue persists, please try to set up a scraper using a non-shared account.',
                telemetryLogLevel: LogLevel.WARN,
                error,
                errorType: errorTypes.TEMPORARY_PORTAL_ISSUE
            });
        }
        throw error;
    }
}
