import {expect} from 'chai';
import * as moment from 'moment';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>} from 'puppeteer';
import * as td from 'testdouble';
import * as authCodeUtil from '../../../../src/apiCommunication/authCodes';
import {<PERSON>rowser} from '../../../../src/browser/Browser';
import {Source} from '../../../../src/dataTypes';
import {defaultSourceSideOrganization} from '../../../../src/dataTypes/SourceSideOrganization';
import {InsufficientPrivilegesLevelException} from '../../../../src/error/exceptions';
import * as NintendoConstants from '../../../../src/scrapersV1/httpDownloaders/nintendo/NintendoConstants';
import {NintendoReportDownloader} from '../../../../src/scrapersV1/httpDownloaders/nintendo/NintendoReportDownloader';
import {Report} from '../../../../src/scrapersV1/Report';
import * as zip from '../../../../src/scrapersV2/zip';
import * as fileUtils from '../../../../src/utils/files/fileUtils';
import {shouldNotThrowAsync, shouldThrowAsync} from '../../../utils/shouldThrowAsync';

describe('Nintendo scraper should:', () => {
    const login = 'login';
    const password = 'password';
    const startDate: moment.Moment = moment(new Date('01.01.1970')).utc();
    const endDate = moment().utc();
    const defaultGameNames = ['SUPERHOT'];
    const fakeReportPath = 'fakeValue';

    const anything = td.matchers.anything();

    function mockPlatformList(browser: Browser, games: string[]): void {
        td.when(browser.get(NintendoConstants.reportWebsiteUrl)).thenResolve(null);
        td.when(browser.get(NintendoConstants.authorizationRenewalUrl)).thenResolve(null);
        td.when(browser.get(NintendoConstants.productIdsApiRequestUrl)).thenResolve(games);
        td.when(browser.xPathElementExistsAfterTime(anything)).thenResolve(true);
    }

    function verifyBrowserForReportDownloadByLinks(browser: Browser, gameNames: string[]): void {
        const urlPart = `searchCodes=${gameNames.join('%OD%0A')}`;
        td.verify(browser.downloadReportFile(td.matchers.contains(urlPart), td.matchers.anything(), td.matchers.anything()));
    }

    function mockLoggedOutState(browser: Browser) {
        td.when(browser.waitForAnySelector(NintendoConstants.loginStateSelectors)).thenResolve([NintendoConstants.loggedOutSelector, td.object<ElementHandle>()]);
    }

    function mockLoggedInState(browser: Browser) {
        td.when(browser.waitForAnySelector(NintendoConstants.loginStateSelectors)).thenResolve([NintendoConstants.loggedInXpathSelector, td.object<ElementHandle>()]);
    }

    function mockSuccessfulLoggingStatesProcess(browser: Browser) {
        td.when(browser.waitForAnySelector(NintendoConstants.loginStateSelectors)).thenResolve([NintendoConstants.loggedOutSelector, td.object<ElementHandle>()]);
        td.when(browser.waitForAnySelector(NintendoConstants.loginStateSelectors)).thenResolve([NintendoConstants.loggedInXpathSelector, td.object<ElementHandle>()]);
    }

    function mockRedirectToLogin(browser: Browser) {
        td.when(browser.waitForAnySelector(NintendoConstants.redirectStateSelectors)).thenResolve([NintendoConstants.signInButtonSelector, td.object<ElementHandle>()]);
    }

    function mockDoNotRedirectToLogin(browser: Browser) {
        td.when(browser.waitForAnySelector(NintendoConstants.redirectStateSelectors)).thenResolve([NintendoConstants.loginInputSelector, td.object<ElementHandle>()]);
    }

    function mockSuccessful2FAWithRedirectToLoginPage(browser: Browser) {
        td.when(browser.waitForAnySelector(NintendoConstants.afterLoginAttemptStateSelectors)).thenResolve(
            [NintendoConstants.mfaCodeInputSelector, td.object<ElementHandle>()],
            [NintendoConstants.signInButtonSelector, td.object<ElementHandle>()],
            [NintendoConstants.mfaCodeInputSelector, td.object<ElementHandle>()]
        );
    }

    function mockSuccessfulLogin(browser: Browser) {
        td.when(browser.waitForAnySelector(NintendoConstants.afterLoginAttemptStateSelectors)).thenResolve([
            NintendoConstants.loggedInXpathSelector,
            td.object<ElementHandle>()
        ]);
    }

    function mockInvalidCredentialsLoginResult(browser: Browser) {
        td.when(browser.waitForAnySelector(NintendoConstants.afterLoginAttemptStateSelectors)).thenResolve([
            NintendoConstants.invalidCredentialsSelector,
            td.object<ElementHandle>()
        ]);
    }

    function mockEnglishLanguage(browser: Browser) {
        td.when(browser.waitForAnySelector(NintendoConstants.languageStateSelectors)).thenResolve([
            NintendoConstants.isJapaneseLanguageSelector,
            td.object<ElementHandle>()
        ]);
    }

    function mockJapaneseLanguage(browser: Browser, shouldSuccess: boolean = true) {
        td.when(browser.waitForAnySelector(NintendoConstants.languageStateSelectors)).thenResolve([
            NintendoConstants.isEnglishLanguageSelector,
            td.object<ElementHandle>()
        ]);
        td.when(browser.elementExists(NintendoConstants.isJapaneseLanguageSelector)).thenResolve(shouldSuccess);
    }

    beforeEach(() => {
        td.replace(fileUtils, 'checkFileExists', () => Promise.resolve(true));
        td.replace(fileUtils, 'convertNintendoJSONOrCSVToCSV', () => Promise.resolve(true));
        td.replace(fileUtils, 'generateFileName', () => fakeReportPath);
        td.replace(zip, 'packReportsToZip', () => Promise.resolve());
    });

    afterEach(() => {
        td.reset();
    });

    it('login user successfully', async () => {
        const browser = td.object<Browser>();

        mockLoggedOutState(browser);
        mockDoNotRedirectToLogin(browser);
        mockEnglishLanguage(browser);
        mockSuccessfulLogin(browser);

        const reportDownloader = new NintendoReportDownloader({login, password, browser});
        const loginResult = await reportDownloader.performLogin();

        td.verify(browser.type(NintendoConstants.loginInputSelector, login));
        td.verify(browser.type(NintendoConstants.passwordInputSelector, password));
        td.verify(browser.click(NintendoConstants.submitButton));

        expect(loginResult.isSuccessfulLogin()).to.be.true;
    });

    it('login user successfully and change language', async () => {
        const browser = td.object<Browser>();

        mockLoggedOutState(browser);
        mockDoNotRedirectToLogin(browser);
        mockJapaneseLanguage(browser, true);
        mockSuccessfulLogin(browser);

        const reportDownloader = new NintendoReportDownloader({login, password, browser});
        const loginResult = await reportDownloader.performLogin();
        expect(loginResult.isFailedLogin()).to.be.false;
        td.verify(browser.click(NintendoConstants.isEnglishLanguageSelector));
    });

    it('try to login user and inform that it could not change language', async () => {
        const browser = td.object<Browser>();

        mockLoggedOutState(browser);
        mockDoNotRedirectToLogin(browser);
        mockJapaneseLanguage(browser, false);
        mockSuccessfulLogin(browser);

        const reportDownloader = new NintendoReportDownloader({login, password, browser});
        const loginResult = await reportDownloader.performLogin();

        expect(loginResult.isAuthenticationException()).to.be.true;
        expect(loginResult.getAuthenticationErrorString()).to.be.equal("Couldn't change language.");
    });

    it('try to login user and inform that it credentials were incorrect', async () => {
        const browser = td.object<Browser>();

        mockLoggedOutState(browser);
        mockDoNotRedirectToLogin(browser);
        mockEnglishLanguage(browser);

        mockInvalidCredentialsLoginResult(browser);

        const reportDownloader = new NintendoReportDownloader({login, password, browser});
        const loginResult = await reportDownloader.performLogin();

        expect(loginResult.isAuthenticationException()).to.be.true;
        expect(loginResult.getAuthenticationErrorString()).to.be.equal('Wrong credentials, please enter correct username and/or password.');
    });

    it('should return result with expired session if no credentials provided (manual session)', async () => {
        const browser = td.object<Browser>();

        mockLoggedOutState(browser);

        const reportDownloader = new NintendoReportDownloader({login: undefined, password: undefined, browser});

        const loginResult = await reportDownloader.performLogin();
        expect(loginResult.isSessionExpired()).to.be.true;
    });

    it(`Download Report When User is already logged in, downloading with links`, async () => {
        const browser = td.object<Browser>();

        mockLoggedInState(browser);
        mockPlatformList(browser, defaultGameNames);

        const reportDownloader = new NintendoReportDownloader({login, password, browser});
        const downloadResult = await reportDownloader.downloadReport(startDate, endDate);

        expect(downloadResult.isReport()).to.be.true;
        expect(downloadResult.getReports()).to.be.deep.equal([new Report(Source.NINTENDO_SALES, fakeReportPath, startDate, endDate)]);
        verifyBrowserForReportDownloadByLinks(browser, defaultGameNames);
    });

    it(`Download Report When User needs to login, downloading with links`, async () => {
        const browser = td.object<Browser>();

        mockLoggedOutState(browser);
        mockDoNotRedirectToLogin(browser);
        mockEnglishLanguage(browser);
        mockSuccessfulLogin(browser);

        mockPlatformList(browser, defaultGameNames);

        const reportDownloader = new NintendoReportDownloader({login, password, browser});
        const downloadResult = await reportDownloader.downloadReport(startDate, endDate);

        expect(downloadResult.isReport()).to.be.true;
        expect(downloadResult.getReports()).to.be.deep.equal([new Report(Source.NINTENDO_SALES, fakeReportPath, startDate, endDate)]);

        verifyBrowserForReportDownloadByLinks(browser, defaultGameNames);
    });

    it(`Download Report When User needs to login with 2FA, downloading with links`, async () => {
        const browser = td.object<Browser>();

        // mockLoggedOurState and mockSuccessfulLogin consecutively
        mockSuccessfulLoggingStatesProcess(browser);
        mockDoNotRedirectToLogin(browser);
        mockEnglishLanguage(browser);
        // mock 2FA tokens consecutively
        const mockGetAuthCode = td.replace(authCodeUtil, 'getAuthCode');
        td.when(mockGetAuthCode(td.matchers.anything(), td.matchers.anything())).thenResolve('123456', '654321');
        // first 2fa attempt redirects to login page
        mockSuccessful2FAWithRedirectToLoginPage(browser);
        mockRedirectToLogin(browser);
        td.when(browser.page.evaluate(NintendoConstants.mfaCodeInputSelector)).thenResolve(false, true);

        mockPlatformList(browser, defaultGameNames);

        const reportDownloader = new NintendoReportDownloader({login, password, totpSecret: 'fake', browser});
        const downloadResult = await reportDownloader.downloadReport(startDate, endDate);

        expect(downloadResult.isReport()).to.be.true;
        expect(downloadResult.getReports()).to.be.deep.equal([new Report(Source.NINTENDO_SALES, fakeReportPath, startDate, endDate)]);

        verifyBrowserForReportDownloadByLinks(browser, defaultGameNames);
    });

    it(`Properly complete report download, downloading with links`, async () => {
        const browser = td.object<Browser>();
        mockPlatformList(browser, defaultGameNames);
        const reportDownloader = new NintendoReportDownloader({login, password, browser});
        const downloadResult = await reportDownloader.completeReportDownload(startDate, endDate);
        expect(downloadResult).to.be.deep.equal([new Report(Source.NINTENDO_SALES, fakeReportPath, startDate, endDate)]);
        verifyBrowserForReportDownloadByLinks(browser, defaultGameNames);
    });

    it('Check that account has enough privileges', async () => {
        const browser: Browser = td.object<Browser>();
        const reportDownloader = new NintendoReportDownloader({login, password, browser});
        mockPlatformList(browser, defaultGameNames);
        await shouldNotThrowAsync(async () => reportDownloader.validatePrivileges());
    });

    it('Check that account has not enough privileges', async () => {
        const browser: Browser = td.object<Browser>();
        const reportDownloader = new NintendoReportDownloader({login, password, browser});
        mockPlatformList(browser, []);
        const wrapperFn = async () => reportDownloader.validatePrivileges();
        await shouldThrowAsync(wrapperFn, new InsufficientPrivilegesLevelException());
    });

    it('Should return sourceSideOrganizations', async () => {
        const reportDownloader = new NintendoReportDownloader({login, password, browser: td.object<Browser>()});
        const result = await reportDownloader.getSourceSideOrganizations();
        expect(result).to.deep.equal([defaultSourceSideOrganization]);
    });
});
