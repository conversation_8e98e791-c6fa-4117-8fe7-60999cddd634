import {Moment} from 'moment';
import {SourceIdentifier, SourceSideOrganization} from '../dataTypes/SourceSideOrganization';
import {Report} from '../scrapersV1/Report';
import {log} from '../utils/logger';
import ManualLoginDetails from './ManualLoginDetails';
import {ScraperContext} from './ScraperContext';

// GENERIC TYPES
export type Session = unknown;

export interface LoginFunction<T extends ScraperParams, S extends Session = Session> {
    (context: ScraperContext<T, S>): Promise<S>;
}

export interface ScrapeFunction<T extends ScraperParams, S extends Session = Session> {
    (context: ScraperContext<T, S>, startDate: Moment, endDate: Moment): Promise<Report[]>;
}

export interface GetSourceSideOrganizationsFunction<T extends ScraperParams, S extends Session = Session> {
    (context: ScraperContext<T, S>): Promise<SourceSideOrganization[]>;
}

export interface CheckSessionFunction<T extends ScraperParams, S extends Session = Session> {
    (context: ScraperContext<T, S>): Promise<SourceIdentifier>;
}

export interface SessionObjectSyntaxValidator<T> {
    (session: any): session is T;
}

export interface Scraper<T extends ScraperParams = ScraperParams, S extends Session = Session> {
    /**
     * Attempt to log in using dependencies provided to the scraper during it's construction.
     *
     * Save the login state so that it could be used by subsequent calls to scrape().
     * If the browser instance already has a logged in session for this scraper,
     * the function should just return.
     *
     * If login was unsuccessful for any reason, should throw a LoginException.
     **/
    login: LoginFunction<T, S>;

    /**
     * Download the report for the specified date range.
     * Assume the browser instance already contains a valid login session.
     */
    scrape: ScrapeFunction<T, S>;

    /**
     * Get all source side organizations available for this session
     * Assume the browser instance already contains a valid login session.
     */
    getSourceSideOrganizations: GetSourceSideOrganizationsFunction<T, S>;

    /**
     * Validate if provided session is valid and the user has permissions to download data
     *
     * If the session is not valid for any reason, should throw an exception
     */
    checkSession: CheckSessionFunction<T, S>;

    /**
     * optional data to be used by the manual login
     */
    manualLoginDetails?: ManualLoginDetails;

    sessionObjectSyntaxValidator?: SessionObjectSyntaxValidator<S>;
}

type StaticFeatureFlag = 'scrapers' | 'apple-scrapers' | 'full-history-dump' | 'steam-include-complementary-packages' | 'steam-include-in-app-sales';

type DynamicFeatureFlag = `nintendo-sales-product-ids-per-report-${number}` | `nintendo-sales-max-concurrent-downloads-${number}`;

export type FeatureFlag = StaticFeatureFlag | DynamicFeatureFlag;

export type DynamicFlagConfig<T extends string, V> = {
    prefix: T;
    setter: (value: V) => void;
    parser: (value: string) => V;
};

export function processFeatureFlags<T extends string, V>(flags: FeatureFlag[], configs: DynamicFlagConfig<T, V>[]): void {
    for (const flag of flags) {
        for (const {prefix, setter, parser} of configs) {
            if (typeof flag === 'string' && flag.startsWith(prefix)) {
                const value = flag.slice(prefix.length);
                const parsedValue = parser(value);
                setter(parsedValue);
                log.info(`Feature flag ${flag} is set to ${parsedValue}`);
            }
        }
    }
}

export type ScraperParams = {
    ignoredOrganizations?: string[];
    ignoredProducts?: string[];
    featureFlags?: FeatureFlag[];
    [key: string]: any;
};
