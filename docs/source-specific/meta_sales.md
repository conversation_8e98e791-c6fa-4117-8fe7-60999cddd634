# Business logic in code

External data source from Oculus introduced realtime revenue dashboard in addition to, so called, legacy one. The main difference is time range covered by these separate dashboards, identified with separate GraphQL doc_ids.

## Legacy Dashboard

-   Revenue doc_id: `MetaConstants.GET_APP_REVENUE_LEGACY_DASHBOARD = '6372287432833402'`
-   Units sold doc_id: `MetaConstants.GET_APP_UNITS_SOLD_LEGACY_DASHBOARD = '6308235845886250'`
-   Time range: last 2 years

## Livetime Dashboard

-   Revenue doc_id: `GET_APP_REAL_TIME = '8725122720898947'`
-   Units sold doc_id: `GET_APP_REAL_TIME = '8725122720898947'`
-   Time range: cutoff on 2024-09-08

## Scraper solution to ensure largest data available

Oculus scraper bases on the startDate param received from Scraper API. When its after the `legacyDashboardDateCutoff = '2024-09-10'`, it uses Livetime Dashboard with better data accuracy. Otherwise, it uses the Legacy Dashboard, to ensure longer data history available. Two days margin is customly added to ensure possible data overlapping on the critical time range cutoff date.
