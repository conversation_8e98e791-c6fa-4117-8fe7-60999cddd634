import {BrowserSession} from '../../../browserV2';
import {defaultSourceSideOrganization} from '../../../dataTypes/SourceSideOrganization';
import {GetSourceSideOrganizationsFunction} from '../../Scraper';
import {SteamParams} from '../common/login';
import {steamGamesRequest} from './steamGamesRequest';

function extractOrganizationDataFromInternalJSvariable(dashboardPageHtml) {
    const orgCodeLineFromHtml = dashboardPageHtml.match(new RegExp('.*g_rgAllAffiliatedPublishers.*'));

    if (!orgCodeLineFromHtml.length) {
        return [defaultSourceSideOrganization];
    }
    const allOrgsRawStringLine = orgCodeLineFromHtml[0];
    const splitByVariableDeclaration = allOrgsRawStringLine.split('=')[1];
    const removeTrailingSemicolon = splitByVariableDeclaration.split(';')[0];
    const rawOrgsJsonString = removeTrailingSemicolon.trim();
    const orgsObject = JSON.parse(rawOrgsJsonString);
    return Object.keys(orgsObject).map((id) => ({id, name: orgsObject[id]}));
}
export const getSourceSideOrganizations: GetSourceSideOrganizationsFunction<SteamParams, BrowserSession> = async ({getHttpClient}) => {
    const httpClient = getHttpClient();
    const dashboardPage = (await steamGamesRequest('/dashboard', {httpClient, followRedirects: true})).data;
    return extractOrganizationDataFromInternalJSvariable(dashboardPage);
};
