import {expect} from 'chai';
import * as sinon from 'sinon';
import * as messaging from '../../../../src/cli/messaging';
import {log} from '../../../../src/utils/logger';
import {LogLevel} from '../../../../src/utils/logger';

describe('logger should:', () => {
    const testMessage = 'Test message';

    let sinonLogSpy;

    beforeEach(() => {
        sinonLogSpy = sinon.spy(messaging, 'printOutput');
    });

    afterEach(() => {
        sinon.restore();
    });

    it('should test log.string when logging Info', () => {
        log.info(testMessage);
        expect(sinonLogSpy.calledWithExactly(testMessage)).to.be.true;
    });

    it('should test log.string when logging warn', () => {
        log.warning(testMessage);
        expect(sinonLogSpy.calledWithExactly(testMessage, LogLevel.WARN)).to.be.true;
    });
});
