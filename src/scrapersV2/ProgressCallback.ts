import {LogLevel} from '../utils/logger';

/**
 * A function for reporting progress. An implementation of Progress is passed to long-running functions
 * so that the function can report on the progress.
 * A typical implementation would show a progress bar in the UI, reporting progress to the user.
 * @param message: the message to display. If message is undefined, no change in message is assumed (e.g. when only progress changes)
 * @param progressValue: if progressValue is a number between 0-100, it indicates the current progress in terms of percentage.
 * if progressValue is undefined or not given as a param, no change in progress is assumed.
 */
export interface ProgressCallback {
    (message: string, progress?: number, logLevel?: LogLevel): void;
}
