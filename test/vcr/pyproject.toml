[tool.poetry]
name = "vcr_proxy"
version = "0.1.0"
description = ""
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.10"
starlette = "^0.20.4"
httpx = "^0.23.0"
uvicorn = {extras = ["standard"], version = "^0.18.2"}
click = "^8.1.3"
poetry = "^1.1.13"
black = "^22.6.0"
pytest = "^7.1.2"
cryptography = "^37.0.4"

[tool.poetry.dev-dependencies]

[tool.poetry.scripts]
vcr = "vcr_proxy.vcr_proxy:hello"

[tool.pytest.ini_options]
log_cli = false
log_cli_level = "INFO"
log_cli_format = "%(asctime)s,%(msecs)03d [%(levelname)8s] %(message)s (%(filename)s:%(lineno)s)"
log_cli_date_format = "%Y-%m-%d %H:%M:%S"

[tool.black]
line-length = 200

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
