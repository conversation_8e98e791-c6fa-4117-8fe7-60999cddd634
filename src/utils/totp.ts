import * as Crypto from 'crypto';
import {Moment} from 'moment';
import {TOTP} from 'otpauth';

/**
 *  Return a Time-based One-Time Password (TOTP) based on the supplied sharedSecret and current system time.
 *  Used for 2FA by Google Authenticator and similar tools.
 *  On some scraped platforms (e.g. Epic), we can use this to replicate 2FA codes internally in the scraper.
 */
export function generate(sharedSecret: string, time?: Moment): string {
    const timestamp = time === undefined ? Date.now() : time.valueOf();
    let totp: TOTP;
    try {
        totp = new TOTP({
            algorithm: 'SHA1',
            digits: 6,
            period: 30,
            secret: sharedSecret
        });
    } catch (error) {
        if (error instanceof TypeError && error.message.includes('Invalid character found: ')) {
            const invalidCharacter = error.message.split('Invalid character found: ')[1];
            let errorMessage = `Invalid authenticator key. Invalid character: '${invalidCharacter}'.`;
            if (invalidCharacter === ' ') {
                errorMessage += ' Try to remove all spaces from your authenticator key.';
            }
            throw new Error(errorMessage);
        }
        throw error;
    }
    return totp.generate({timestamp});
}

export function b32ToHex(s: string): string {
    // borrowed from https://github.com/bitwarden/clients/blob/be6f2573983276dec208660a3add6f5a40961238/libs/common/src/vault/services/totp.service.ts#L127

    const b32Chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';

    function leftPad(s: string, l: number, p: string): string {
        if (l + 1 >= s.length) {
            s = Array(l + 1 - s.length).join(p) + s;
        }
        return s;
    }

    s = s.toUpperCase();
    let cleanedInput = '';

    for (let i = 0; i < s.length; i++) {
        if (b32Chars.indexOf(s[i]) < 0) {
            continue;
        }

        cleanedInput += s[i];
    }
    s = cleanedInput;

    let bits = '';
    let hex = '';
    for (let i = 0; i < s.length; i++) {
        const byteIndex = b32Chars.indexOf(s.charAt(i));
        if (byteIndex < 0) {
            continue;
        }
        bits += leftPad(byteIndex.toString(2), 5, '0');
    }
    for (let i = 0; i + 4 <= bits.length; i += 4) {
        const chunk = bits.substr(i, 4);
        hex = hex + parseInt(chunk, 2).toString(16);
    }
    return hex;
}

export function generateSteamGuard(sharedSecret: string, time?: Moment): string {
    // after https://github.com/DoctorMcKay/node-steam-totp/blob/master/index.js

    const millisInSecond = 1000;
    const timestamp = Math.floor((time === undefined ? Date.now() : time.valueOf()) / millisInSecond);

    const steamPeriod = 30;
    const bufSize = 8;
    const bufSizeHexChars = 4;

    let secret: Buffer;

    if (sharedSecret.match(/[0-9a-f]{40}/i)) {
        secret = Buffer.from(sharedSecret, 'hex');
    } else if (sharedSecret.match(/[2-7a-z]{16}/i)) {
        secret = Buffer.from(b32ToHex(sharedSecret), 'hex');
    } else {
        secret = Buffer.from(sharedSecret, 'base64');
    }

    const buffer = Buffer.allocUnsafe(bufSize);
    buffer.writeUInt32BE(0, 0);
    buffer.writeUInt32BE(Math.floor(timestamp / steamPeriod), bufSizeHexChars);

    const hmac = Crypto.createHmac('sha1', secret);
    let hmacBuffer = hmac.update(buffer).digest();
    /* eslint-disable no-bitwise, @typescript-eslint/no-magic-numbers */
    // https://bit.ly/3wbNphl
    const start = hmacBuffer[19] & 0x0f;
    hmacBuffer = hmacBuffer.subarray(start, start + 4);
    let fullcode = hmacBuffer.readUInt32BE(0) & 0x7fffffff;
    /* eslint-enable no-bitwise, @typescript-eslint/no-magic-numbers */

    const chars = '23456789BCDFGHJKMNPQRTVWXY';

    const steamCodeLen = 5;
    let code = '';
    for (let i = 0; i < steamCodeLen; i++) {
        code += chars.charAt(fullcode % chars.length);
        fullcode /= chars.length;
    }

    return code;
}
