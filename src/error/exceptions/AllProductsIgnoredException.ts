import {Source, sourceToPortal} from '../../dataTypes';
import {LogLevel} from '../../utils/logger';
import {CustomException} from '../CustomException';
import {errorTypes} from '../errorTypes';

export class AllProductsIgnoredException extends CustomException {
    constructor(source: Source) {
        super({
            message: `All downloadable products were marked to be ignored! The ${sourceToPortal(source)} scraper has nothing to scrape.`,
            suggestedAction: 'Remove some products from the ignored list and try again.',
            telemetryLogLevel: LogLevel.WARN,
            errorType: errorTypes.CONFIGURATION_ISSUE
        });
    }
}
