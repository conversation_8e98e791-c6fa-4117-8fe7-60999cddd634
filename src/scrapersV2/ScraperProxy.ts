import {Moment} from 'moment';
import {Source, displaySource} from '../dataTypes';
import {SourceIdentifier, SourceSideOrganization} from '../dataTypes/SourceSideOrganization';
import {CustomException} from '../error/CustomException';
import {errorTypes} from '../error/errorTypes';
import {Report} from '../scrapersV1/Report';
import {SessionFile} from '../utils/files/JsonFile';
import {convertMapToParamsObj} from '../utils/objectOperations';
import {sanitizeSession} from '../utils/sanitizeSession';
import ManualLoginDetails from './ManualLoginDetails';
import {ProgressCallback} from './ProgressCallback';
import {ReportDownloaderAdapter} from './reportDownloadAdapter/ReportDownloaderAdapter';
import {Scraper, ScraperParams} from './Scraper';
import {ScraperContext} from './ScraperContext';
import {v2Scrapers} from './V2ScrapersList';

interface ScraperProxyCreateOptions {
    source: Source | string;
    progress: ProgressCallback;
    paramsMap: Map<string, any>;
    sessionFile: SessionFile;
    // if provided, sessionFile becomes readonly
    outputSessionFile?: SessionFile;
    proxyUrl?: string;
}

export class ScraperProxy {
    readonly scraper: Scraper;
    private readonly progress: ProgressCallback;
    private readonly outputSessionFile: SessionFile;
    private readonly context: ScraperContext;

    constructor(scraper: Scraper, progress: ProgressCallback, sessionFile: SessionFile, context: ScraperContext, outputSessionFile?: SessionFile) {
        this.scraper = scraper;
        this.progress = progress;
        this.outputSessionFile = outputSessionFile || sessionFile;
        this.context = context;
    }

    public static async getRawScraperWithoutContext(source: Source): Promise<Scraper> {
        return v2Scrapers.get(source) || new ReportDownloaderAdapter(source as Source);
    }

    public static async create({source, progress, paramsMap, sessionFile, outputSessionFile, proxyUrl}: ScraperProxyCreateOptions): Promise<ScraperProxy> {
        const sourceProgress = (message: string, _progress?: number) => progress(`[${displaySource[source] || source}] ${message}`, _progress);
        const scraper = v2Scrapers.get(source) || new ReportDownloaderAdapter(source as Source);
        const params = convertMapToParamsObj<ScraperParams>(paramsMap);
        const session = await sessionFile.load();
        const context = new ScraperContext(sourceProgress, params, sanitizeSession(session), proxyUrl);
        return new ScraperProxy(scraper, sourceProgress, sessionFile, context, outputSessionFile);
    }

    @proxyErrorHandler
    public async login(): Promise<SourceIdentifier> {
        this.progress('Logging in...');
        const session = await this.scraper.login(this.context);
        this.context.updateSession(session);
        await this.outputSessionFile.save(session);
        this.progress('Login successful.');
        return this.scraper.checkSession(this.context);
    }

    @proxyErrorHandler
    public async scrape(startDate: Moment, endDate: Moment): Promise<Report[]> {
        this.progress(`Scraping ${startDate.format('YYYY-MM-DD')} - ${endDate.format('YYYY-MM-DD')}`);
        const reports = await this.scraper.scrape(this.context, startDate, endDate);

        await this.outputSessionFile.save(this.context.session, false);
        return reports;
    }

    @proxyErrorHandler
    public async checkSession(): Promise<SourceIdentifier> {
        this.progress('Verifying session status');

        if (this.scraper.sessionObjectSyntaxValidator && !this.scraper.sessionObjectSyntaxValidator(this.context.session)) {
            this.progress('Session syntax is invalid.');
            throw new CustomException({message: 'Session syntax is invalid.', errorType: errorTypes.CONFIGURATION_ISSUE});
        }
        return this.scraper.checkSession(this.context);
    }

    /**
     * Called to clean up any open resources that the proxy and its scraper might have.
     */
    public async close(): Promise<void> {
        await this.context.close();
    }

    @proxyErrorHandler
    async getSourceSideOrganizations(): Promise<SourceSideOrganization[]> {
        this.progress('Obtaining source side organizations');
        const organizations = await this.scraper.getSourceSideOrganizations(this.context);
        this.progress(`Obtaining organizations: ${JSON.stringify(organizations)}`);
        return organizations;
    }

    @proxyErrorHandler
    getManualLoginDetails(): ManualLoginDetails | undefined {
        return this.scraper.manualLoginDetails;
    }
}

function proxyErrorHandler(_target: unknown, _propertyKey: string, descriptor: TypedPropertyDescriptor<(...params: unknown[]) => Promise<unknown> | unknown>) {
    const oldFunc = descriptor.value;
    // eslint-disable-next-line no-param-reassign
    descriptor.value = async function (): Promise<unknown> {
        try {
            // eslint-disable-next-line prefer-rest-params
            return await Reflect.apply(oldFunc!, this, arguments);
        } catch (error) {
            await this.context.emergencyClose();
            throw error;
        }
    };
}
