import {Fernet} from 'fernet-nodejs';

export class FileCrypter {
    private readonly fernet: Fernet;

    public constructor() {
        if (!process.env.SCP_CREDENTIALS_ENCRYPTION_KEY) {
            throw new Error('Could not find an encryption key.');
        }
        this.fernet = new Fernet(process.env.SCP_CREDENTIALS_ENCRYPTION_KEY);
    }

    public decrypt(content: string) {
        return this.fernet.decrypt(content);
    }

    public crypt(content: string) {
        return this.fernet.encrypt(content);
    }
}
