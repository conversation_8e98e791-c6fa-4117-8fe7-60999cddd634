<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Steam impressions login" type="NodeJSConfigurationType" application-parameters="login --source=steam_impressions --sessionFile=&quot;.private/steam_impressions.json&quot; --headless=false --credentials=&quot;{\&quot;user\&quot;:\&quot;asd\&quot;,\&quot;password\&quot;:\&quot;asd\&quot;} &quot; --apiUrl=&quot;https://scraper-api.indiebi.dev&quot; --apiToken=&quot;token&quot;" path-to-js-file="dist/src/scrape.js" working-dir="$PROJECT_DIR$">
    <method v="2">
      <option name="TypeScript.Before.Run" enabled="true" FAIL_ON_ERROR="true" CONFIG_PATH="$PROJECT_DIR$/tsconfig.json" />
    </method>
  </configuration>
</component>