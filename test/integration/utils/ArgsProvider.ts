import * as os from 'os';
import * as path from 'path';
import axios from 'axios';
import {utc} from 'moment';
import * as moment from 'moment/moment';
import * as puppeteer from 'puppeteer';
import {CLIArguments, Command} from '../../../src/cli/arguments';
import {Source} from '../../../src/dataTypes';
import {FileExtension} from '../../../src/utils/files/FileExtension';
import {generateFileName} from '../../../src/utils/files/fileUtils';
import {sessionsManager} from './SessionsManager';
import {testConfigRepo} from './TestConfigRepository';

export function getCommonTestArgs() {
    return {
        apiUrl: testConfigRepo.get('indie_bi_api').url,
        encrypt: false,
        encryptionToken: '',
        headless: true,
        printTelemetry: false,
        dumpDir: path.join(os.homedir(), testConfigRepo.get('app').dumpDir).toString(),
        chromePath: puppeteer.executablePath()
    };
}

export async function generateApiToken() {
    const {email, password, url} = testConfigRepo.get('indie_bi_api');
    const {data} = await axios.post(`${url}/1/login`, {email, password});
    return data.jwt;
}

/**
 * @param source - source for which to generate args
 * @param credentialsKey - the name of the key in the json file which holds credentials that should be used for this test. The json file is created by decrypting credentials_encrypted.json
 * @param sessionFileName - the name of the session file. It is assumed it will be held in a default directory (check in SessionsManager). Sessions are decrypted from encrypted_sessions.blob when integration tests start (see rootLevelHooks).
 * @returns login arguments
 */
export function getLoginArgs(source: Source, credentialsKey: string, sessionFileName: string): CLIArguments {
    return {
        ...getCommonTestArgs(),
        command: Command.LOGIN,
        source: source,
        credentials: testConfigRepo.get(credentialsKey),
        sessionFile: sessionsManager.get(sessionFileName)
    };
}

export function getScrapeArgs(
    loginArgs: any,
    defaultStartDateString: string,
    defaultEndDateString: string,
    fileExtension: FileExtension,
    additionalIndex?: string | number
): CliArgumentsInTests {
    return getScrapeArgsUtc(loginArgs, utc(defaultStartDateString), utc(defaultEndDateString), fileExtension, additionalIndex);
}

export interface CliArgumentsInTests extends CLIArguments {
    testedFilePath: string;
    expectedFileName: string;
}

export function getScrapeArgsUtc(
    loginArgs: any,
    defaultStartDateUtc: moment.Moment,
    defaultEndDateUtc: moment.Moment,
    fileExtension: FileExtension,
    additionalIndex?: string | number | undefined
): CliArgumentsInTests {
    const expectedFileName = generateFileName(loginArgs.source, defaultStartDateUtc, defaultEndDateUtc, fileExtension, additionalIndex);
    const userDataPath = getDownloadPath();
    const testedFilePath = path.join(userDataPath, expectedFileName);
    return {
        ...loginArgs,
        command: Command.SCRAPE,
        reportPath: userDataPath,
        from: defaultStartDateUtc,
        to: defaultEndDateUtc,
        testedFilePath,
        expectedFileName
    };
}

export const LOGIN_ARGS = {
    for_meta_rift: getLoginArgs(Source.META_RIFT_SALES, 'oculus', 'oculus.json'),
    for_meta_quest: getLoginArgs(Source.META_QUEST_SALES, 'oculus', 'oculus.json'),
    for_epic: getLoginArgs(Source.EPIC_SALES, 'epic_sales_superhot', 'epic_sales.json'),
    for_gog: getLoginArgs(Source.GOG_SALES, 'gog_tom', 'gog_sales.json'),
    for_humble: getLoginArgs(Source.HUMBLE_SALES, 'humble_sales_tom', 'humble_sales.json'),
    for_nintendo: getLoginArgs(Source.NINTENDO_SALES, 'nintendo', 'nintendo_switch.json'),
    for_playstation: (source: Source.PLAYSTATION_SALES | Source.PLAYSTATION_WISHLIST_ACTIONS) => getLoginArgs(source, 'playstation', 'playstation.json'),
    for_steam_sales_fullpermissions: getLoginArgs(Source.STEAM_SALES, 'steam_sales_superhot_fullpermissions', 'steam_fullpermissions.json'),
    for_steam_sales_low_permissions: getLoginArgs(Source.STEAM_SALES, 'steam_sales_superhot_lowpermissions', 'steam_lowpermissions.json')
};

// TODO get rid of this, instead of using old desktop-like paths we could use some custom directories so we don't have to deal with process envs
function getDownloadPath(): string {
    const {APPDATA, HOME} = process.env;
    const basePath = APPDATA ? `${APPDATA}\\` : process.platform === 'darwin' ? `${HOME}/Library/Preferences/` : `${HOME}/.local/share/`;
    return path.join(basePath, 'indieBI', 'downloads');
}
