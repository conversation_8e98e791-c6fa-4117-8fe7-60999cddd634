import {CheerioAPI} from 'cheerio';

const totalVisitsColumnIndex = 5;
const totalImpressionsColumnIndex = 1;
const cheerioElementIndexOffset = 1;

function rawStringToNumber(raw: string): number {
    return parseInt(raw.replace(/,/g, ''), 10);
}
export function extractChecksum(checksumPage: CheerioAPI) {
    const impressionsChecksum = checksumPage(`.tr.totals_row .td:nth-child(${totalImpressionsColumnIndex + cheerioElementIndexOffset})`);
    const totalVisitsChecksum = checksumPage(`.tr.totals_row .td:nth-child(${totalVisitsColumnIndex + cheerioElementIndexOffset})`);

    if (!impressionsChecksum.text() || !totalVisitsChecksum.text()) {
        return null;
    }

    return {
        impressions: rawStringToNumber(impressionsChecksum.text()),
        visits: rawStringToNumber(totalVisitsChecksum.text())
    };
}
