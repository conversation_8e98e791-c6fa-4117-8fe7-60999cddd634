import * as chai from 'chai';
import * as chaiAsPromised from 'chai-as-promised';
import * as moment from 'moment';
import {TimeoutError} from 'puppeteer';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, J<PERSON>H<PERSON><PERSON>} from 'puppeteer';
import * as td from 'testdouble';
import {<PERSON>rowser} from '../../../../src/browser/Browser';
import {Source} from '../../../../src/dataTypes';
import {MissingPermissionsException} from '../../../../src/error/common/MissingPermissionsException';
import {
    DashCard,
    Parameter,
    getDashboardMetadataQueryUrl,
    getProductsListQueryUrl,
    getSalesDataQueryUrl,
    hasPermissionSelector
} from '../../../../src/scrapersV1/httpDownloaders/gog/GOGConstants';
import {GOGReportDownloader} from '../../../../src/scrapersV1/httpDownloaders/gog/GOGReportDownloader';
import {ReportManifest} from '../../../../src/scrapersV2/ReportManifest';
import * as zip from '../../../../src/scrapersV2/zip';
import * as fileUtils from '../../../../src/utils/files/fileUtils';

chai.use(chaiAsPromised);
const {expect} = chai;

describe('GogReportDownloader', function () {
    const login = 'test';
    const password = 'pass';
    const organization_name = 'SUPERHOT';
    const product_1 = 'sampleProduct1';
    const sampleProduct_1Filename = 'gog_sales-2019-10-10_2019-10-11-sampleProduct1';
    const product_2 = 'sampleProduct2';
    const sampleProduct_2Filename = 'gog_sales-2019-10-10_2019-10-11-sampleProduct2';
    const product_2_3 = 'sample: Product 2 & 3';
    const sampleProduct_2_3Filename = 'gog_sales-2019-10-10_2019-10-11-sample-Product-2-and-3';
    const productEmoji = 'sampleProduct😀';
    const startDate = moment('2019-10-10T10:00:00.000Z').utc();
    const endDate = moment('2019-10-11T10:00:00.000Z').utc();
    const orgId = 11661;
    const jwt = 'ey...jwt...token...';
    const fakeDashcard = {card: {name: 'Sales Summary Periodical', id: 1}, id: 1} as DashCard;
    const fakeParameter = {name: 'Products', id: '1'} as Parameter;
    const sampleData = [
        [1, 2],
        [1, 2]
    ];
    const orgsHtml = `
<div class="list-group">
    <a href="https://partners.gog.com/profile/changeRoleTo/${orgId}" class="list-group-item keychainify-checked">
        <span class="label label-success pull-right">Publisher #1502</span>
        SUPERHOT
    </a>
</div>`;

    afterEach(() => {
        td.reset();
    });

    function prepareOrganizations(fakeBrowser: Browser, orgsHtml: string) {
        td.when(fakeBrowser.get('https://partners.gog.com/profile')).thenResolve(orgsHtml);
    }

    function preparePermission(fakeBrowser: Browser, withCorrectPermissions: boolean) {
        td.when(fakeBrowser.elementExists(hasPermissionSelector, Browser.defaultSelectorTimeout, false)).thenResolve(withCorrectPermissions);
    }

    function prepareMetabaseIFrameToExtractJwt(fakeBrowser: Browser, jwt: string) {
        const fakeIFrame = td.object<ElementHandle>();
        const fakeIFrameHandle = td.object<JSHandle>();
        td.when(fakeIFrame.getProperty('src')).thenResolve(fakeIFrameHandle);
        td.when(fakeIFrameHandle.jsonValue()).thenResolve(`https://metabase-public.gog.com/embed/dashboard/${jwt}#bordered=false&titled=false`);
        td.when(fakeBrowser.querySelector('iframe')).thenResolve(fakeIFrame);
    }

    function prepareDashboardMetadata(fakeBrowser: Browser, jwt: string, fakeDashcard: DashCard, fakeParameter: Parameter) {
        td.when(fakeBrowser.get(getDashboardMetadataQueryUrl(jwt))).thenResolve({
            dashcards: [fakeDashcard],
            parameters: [fakeParameter]
        });
    }

    function prepareProductsList(fakeBrowser: Browser, jwt: string, fakeParameter: Parameter, fakeProducts: string[]) {
        td.when(fakeBrowser.get(getProductsListQueryUrl(jwt, fakeParameter))).thenResolve({values: fakeProducts});
    }

    function prepareSalesData(
        fakeProducts: string[],
        fakeBrowser: Browser,
        jwt: string,
        fakeDashcard: DashCard,
        startDate: moment.Moment,
        endDate: moment.Moment,
        salesData: Array<Array<any>>
    ) {
        fakeProducts.forEach((product) => {
            td.when(fakeBrowser.get(getSalesDataQueryUrl(jwt, fakeDashcard, startDate, endDate, product))).thenResolve({
                data: {
                    cols: [{name: 'first'}, {name: 'second'}],
                    rows: salesData
                },
                json_query: {
                    parameters: [{type: 'string/=', slug: 'products', id: fakeParameter.id, value: [product], target: ['dimension', ['template-tag', 'product']]}]
                },
                status: 'completed'
            });
        });
    }

    function prepareFakeBrowser(fakeProducts: string[], salesData: Array<Array<any>> = [], withCorrectPermissions: boolean = true): Browser {
        const fakeBrowser = td.object<Browser>();

        prepareOrganizations(fakeBrowser, orgsHtml);
        preparePermission(fakeBrowser, withCorrectPermissions);
        prepareMetabaseIFrameToExtractJwt(fakeBrowser, jwt);
        prepareDashboardMetadata(fakeBrowser, jwt, fakeDashcard, fakeParameter);
        prepareProductsList(fakeBrowser, jwt, fakeParameter, fakeProducts);
        prepareSalesData(fakeProducts, fakeBrowser, jwt, fakeDashcard, startDate, endDate, salesData);

        return fakeBrowser;
    }

    function prepareExpectedManifest(startDate: moment.Moment, endDate: moment.Moment) {
        const manifest = new ReportManifest(startDate, endDate, 'v3');

        manifest.addFile(`${sampleProduct_1Filename}.csv`, {
            organization: organization_name,
            product: product_1,
            rawData: false
        });
        manifest.addFile(`${sampleProduct_1Filename}.json`, {
            organization: organization_name,
            product: product_1,
            rawData: true
        });
        manifest.addFile(`${sampleProduct_2Filename}.csv`, {
            organization: organization_name,
            product: product_2,
            rawData: false
        });
        manifest.addFile(`${sampleProduct_2Filename}.json`, {
            organization: organization_name,
            product: product_2,
            rawData: true
        });
        return manifest;
    }

    function prepareExpectedManifestWithAmpersand(startDate: moment.Moment, endDate: moment.Moment) {
        const manifest = new ReportManifest(startDate, endDate, 'v3');
        manifest.addFile(`${sampleProduct_2_3Filename}.csv`, {
            organization: organization_name,
            product: product_2_3,
            rawData: false
        });
        manifest.addFile(`${sampleProduct_2_3Filename}.json`, {
            organization: organization_name,
            product: product_2_3,
            rawData: true
        });
        return manifest;
    }

    it('should generate valid URL for one word product name', async () => {
        const url = getSalesDataQueryUrl(jwt, fakeDashcard, startDate, endDate, product_1);
        expect(url).to.equal(
            'https://metabase-public.gog.com/api/embed/dashboard/ey...jwt...token.../dashcard/1/card/1?date=2019-10-10~2019-10-11&interval=Day&unit_type=&countries=&promotions=&products=sampleProduct1'
        );
    });

    it('should generate valid URL for product name with special characters', async () => {
        const url = getSalesDataQueryUrl(jwt, fakeDashcard, startDate, endDate, product_2_3);
        expect(url).to.equal(
            'https://metabase-public.gog.com/api/embed/dashboard/ey...jwt...token.../dashcard/1/card/1?date=2019-10-10~2019-10-11&interval=Day&unit_type=&countries=&promotions=&products=sample%3A%20Product%202%20%26%203'
        );
    });

    it('should generate valid URL for product name with emoji', async () => {
        const url = getSalesDataQueryUrl(jwt, fakeDashcard, startDate, endDate, productEmoji);
        expect(url).to.equal(
            'https://metabase-public.gog.com/api/embed/dashboard/ey...jwt...token.../dashcard/1/card/1?date=2019-10-10~2019-10-11&interval=Day&unit_type=&countries=&promotions=&products=sampleProduct%F0%9F%98%80'
        );
    });

    it('should complete report download for specified dates and prepare a zip if data is not available', async () => {
        const fakeBrowser = prepareFakeBrowser([product_1, product_2], []);
        td.replace(fileUtils, 'saveFileToDownloads');
        const createZipBasedOnManifest = td.replace(zip, 'createZipBasedOnManifest');

        const gogReportDownloader = new GOGReportDownloader({login, password, browser: fakeBrowser});
        await gogReportDownloader.completeReportDownload(startDate, endDate);

        td.verify(createZipBasedOnManifest(prepareExpectedManifest(startDate, endDate), Source.GOG_SALES, false));
    });

    it('should complete report download for specified dates and prepare a zip with reports if data is available', async () => {
        const fakeBrowser = prepareFakeBrowser([product_1, product_2], sampleData);
        td.replace(fileUtils, 'saveFileToDownloads');
        const createZipBasedOnManifest = td.replace(zip, 'createZipBasedOnManifest');

        const gogReportDownloader = new GOGReportDownloader({login, password, browser: fakeBrowser});
        await gogReportDownloader.completeReportDownload(startDate, endDate);

        td.verify(createZipBasedOnManifest(prepareExpectedManifest(startDate, endDate), Source.GOG_SALES, false));
    });

    it('should complete report download for specified dates and product with &amp in product name', async () => {
        const fakeBrowser = prepareFakeBrowser([product_2_3], sampleData);
        td.replace(fileUtils, 'saveFileToDownloads');
        const createZipBasedOnManifest = td.replace(zip, 'createZipBasedOnManifest');

        const gogReportDownloader = new GOGReportDownloader({login, password, browser: fakeBrowser});
        await gogReportDownloader.completeReportDownload(startDate, endDate);

        const url =
            'https://metabase-public.gog.com/api/embed/dashboard/ey...jwt...token.../dashcard/1/card/1?date=2019-10-10~2019-10-11&interval=Day&unit_type=&countries=&promotions=&products=sample%3A%20Product%202%20%26%203';

        td.verify(fakeBrowser.get(url));

        td.verify(createZipBasedOnManifest(prepareExpectedManifestWithAmpersand(startDate, endDate), Source.GOG_SALES, false));
    });

    it('should immediately rethrow non-custom exception while changing the organization', async () => {
        const fakeBrowser = prepareFakeBrowser([product_1, product_2], sampleData);
        const exception = new Error('test');

        td.when(fakeBrowser.goto(`https://partners.gog.com/profile/changeRoleTo/${orgId}`)).thenReject(exception);

        const gogReportDownloader = new GOGReportDownloader({login, password, browser: fakeBrowser});
        await expect(gogReportDownloader.completeReportDownload(startDate, endDate)).to.eventually.rejectedWith(exception);
        td.verify(fakeBrowser.waitForSelector('.alert.alert-success'), {times: 0});
    });

    it('should try 3 times to change the organization until it fails', async () => {
        const fakeBrowser = prepareFakeBrowser([product_1, product_2], sampleData);
        const exception = new TimeoutError();
        td.when(fakeBrowser.goto(`https://partners.gog.com/profile/changeRoleTo/${orgId}`)).thenReject(exception);

        const gogReportDownloader = new GOGReportDownloader({login, password, browser: fakeBrowser});
        await expect(gogReportDownloader.completeReportDownload(startDate, endDate)).to.eventually.rejectedWith('Unexpected problem with organization occurred');

        td.verify(fakeBrowser.goto(`https://partners.gog.com/profile/changeRoleTo/${orgId}`), {times: 3});
    });

    it('should raise MISSING_PERMISSIONS if we cant open Stats page', async () => {
        const fakeBrowser = prepareFakeBrowser([product_1, product_2], sampleData, false);

        const gogReportDownloader = new GOGReportDownloader({login, password, browser: fakeBrowser});
        await expect(gogReportDownloader.completeReportDownload(startDate, endDate)).to.eventually.to.be.rejectedWith(MissingPermissionsException);
    });

    it('should filter data in case GOG returns incorrect data', async () => {
        const fakeBrowser = td.object<Browser>();
        const sampleProduct1Data = {
            data: {
                cols: [{name: 'first'}, {name: 'second'}],
                rows: sampleData
            },
            json_query: {
                parameters: [{type: 'string/=', slug: 'products', id: fakeParameter.id, value: ['sampleProduct1'], target: ['dimension', ['template-tag', 'product']]}]
            },
            status: 'completed'
        };
        const sampleProduct2Data = {
            data: {
                cols: [{name: 'first'}, {name: 'second'}],
                rows: sampleData
            },
            json_query: {
                parameters: [
                    // In normal case, this should be a parameter with product name
                ]
            },
            status: 'completed'
        };

        prepareOrganizations(fakeBrowser, orgsHtml);
        preparePermission(fakeBrowser, true);
        prepareMetabaseIFrameToExtractJwt(fakeBrowser, jwt);
        prepareDashboardMetadata(fakeBrowser, jwt, fakeDashcard, fakeParameter);
        prepareProductsList(fakeBrowser, jwt, fakeParameter, [product_1, product_2]);

        td.when(fakeBrowser.get(getSalesDataQueryUrl(jwt, fakeDashcard, startDate, endDate, product_1))).thenResolve(sampleProduct1Data);
        td.when(fakeBrowser.get(getSalesDataQueryUrl(jwt, fakeDashcard, startDate, endDate, product_2))).thenResolve(sampleProduct2Data);

        const saveFileToDownloads = td.replace(fileUtils, 'saveFileToDownloads');
        td.replace(zip, 'createZipBasedOnManifest');

        const gogReportDownloader = new GOGReportDownloader({login, password, browser: fakeBrowser});
        await gogReportDownloader.completeReportDownload(startDate, endDate);

        // Should save raw files
        td.verify(saveFileToDownloads('gog_sales-2019-10-10_2019-10-11-sampleProduct1.json', sampleProduct1Data), {times: 1});
        td.verify(saveFileToDownloads('gog_sales-2019-10-10_2019-10-11-sampleProduct2.json', sampleProduct2Data), {times: 1});

        // Should save CSV with data for valid product
        td.verify(saveFileToDownloads('gog_sales-2019-10-10_2019-10-11-sampleProduct1.csv', 'first;second\r\n1;2\r\n1;2'));
        // Should save CSV without data (only header) for invalid product
        td.verify(saveFileToDownloads('gog_sales-2019-10-10_2019-10-11-sampleProduct2.csv', 'first;second\r\n'));
    });
});
