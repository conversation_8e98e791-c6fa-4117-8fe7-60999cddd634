{"calls": {"https://partner.steampowered.com/login": [{"body": {"data": "https_partner.steampowered.com/login", "encoding": "file"}, "headers": {"content-security-policy": "default-src blob: data: https: 'unsafe-inline' 'unsafe-eval'; connect-src 'self' https://community.akamai.steamstatic.com/ https://store.steampowered.com/ wss://community.steam-api.com/websocket/ https://api.steampowered.com/ https://login.steampowered.com/ https://help.steampowered.com/ https://*.valvesoftware.com https://*.steambeta.net https://*.steamcontent.com; report-uri /actions/CSPReport", "content-encoding": "gzip"}}, {"status": 302, "req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561198803773815%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jHOgT7eZGS1QK7g8xVz4wdIRgYQRR7yTZEBKs03tArmIR8zDs1Sb0JBz9hegwvHgpwTvwF0oDLTxflK8Cmw3DA; sessionid=7229b3d72605d80e8449d718;"}, "headers": {"content-security-policy": "default-src blob: data: https: 'unsafe-inline' 'unsafe-eval'; connect-src 'self' https://community.akamai.steamstatic.com/ https://store.steampowered.com/ wss://community.steam-api.com/websocket/ https://api.steampowered.com/ https://login.steampowered.com/ https://help.steampowered.com/ https://*.valvesoftware.com https://*.steambeta.net https://*.steamcontent.com; report-uri /actions/CSPReport", "location": "https://partner.steampowered.com/", "vary": ""}}], "https://scraper-api.indiebi.dev/1/auth-code/steam_sales": [{"req_headers": {"authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************.Ov9uvr4-waR2P4LZq_ndz7Wuv486Nu2nndutkla34kI", "host": "scraper-api.indiebi.dev", "connection": "keep-alive"}, "body": "{\"authCode\":null}", "headers": {"content-type": "application/json; charset=utf-8", "connection": "keep-alive", "request-context": "appId=cid-v1:1ae09687-f2ab-424c-b8e7-c482afdc19c9", "x-powered-by": "Express", "vary": "Origin", "access-control-allow-credentials": "true", "etag": "W/\"11-CSaGiLTQJ36vkT+ep4rzOLCKeXw\"", "strict-transport-security": "max-age=15724800; includeSubDomains", "server": ""}}, {"req_headers": {"authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************.Ov9uvr4-waR2P4LZq_ndz7Wuv486Nu2nndutkla34kI", "host": "scraper-api.indiebi.dev", "connection": "keep-alive"}, "body": "{\"authCode\":\"DBV62\"}", "headers": {"content-type": "application/json; charset=utf-8", "connection": "keep-alive", "request-context": "appId=cid-v1:1ae09687-f2ab-424c-b8e7-c482afdc19c9", "x-powered-by": "Express", "vary": "Origin", "access-control-allow-credentials": "true", "etag": "W/\"14-Gq2nNw712Irxs+qGhYW8yNgu6+4\"", "strict-transport-security": "max-age=15724800; includeSubDomains", "server": ""}}], "https://partner.steampowered.com/": [{"req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561198803773815%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jHOgT7eZGS1QK7g8xVz4wdIRgYQRR7yTZEBKs03tArmIR8zDs1Sb0JBz9hegwvHgpwTvwF0oDLTxflK8Cmw3DA; sessionid=7229b3d72605d80e8449d718;"}, "body": {"data": "https_partner.steampowered.com/index.html", "encoding": "file"}, "headers": {"content-security-policy": "script-src 'self' 'unsafe-inline' 'unsafe-eval'; object-src 'none'; connect-src 'self'; child-src 'self'; report-uri /actions/cspreport", "content-encoding": "gzip", "set-cookie": "dateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:46:33 GMT; Max-Age=10800; path=/, dateEnd=2023-08-08; expires=Tue, 08-Aug-2023 15:46:33 GMT; Max-Age=10800; path=/, priorDateStart=2023-08-07; expires=Tue, 08-Aug-2023 15:46:33 GMT; Max-Age=10800; path=/, priorDateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:46:33 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/nav_games.php": [{"req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561198803773815%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jHOgT7eZGS1QK7g8xVz4wdIRgYQRR7yTZEBKs03tArmIR8zDs1Sb0JBz9hegwvHgpwTvwF0oDLTxflK8Cmw3DA; sessionid=7229b3d72605d80e8449d718; dateStart=2023-08-08;"}, "body": {"data": "https_partner.steampowered.com/nav_games.php", "encoding": "file"}, "headers": {"content-security-policy": "script-src 'self' 'unsafe-inline' 'unsafe-eval'; object-src 'none'; connect-src 'self'; child-src 'self'; report-uri /actions/cspreport", "content-encoding": "gzip", "set-cookie": "dateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:46:34 GMT; Max-Age=10800; path=/, dateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:46:34 GMT; Max-Age=10800; path=/, priorDateStart=2023-08-08; expires=Tue, 08-Aug-2023 15:46:34 GMT; Max-Age=10800; path=/, priorDateEnd=2023-08-07; expires=Tue, 08-Aug-2023 15:46:34 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}]}, "defaults": {"request_headers": {"accept": "application/json, text/plain, */*", "user-agent": "axios/0.27.2", "host": "partner.steampowered.com", "connection": "close"}, "headers": {"server": "nginx", "content-type": "text/html; charset=UTF-8", "vary": "Accept-Encoding", "connection": "close"}}}