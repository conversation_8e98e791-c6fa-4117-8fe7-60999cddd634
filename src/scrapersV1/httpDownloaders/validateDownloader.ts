import {Source} from '../../dataTypes';
import {InvalidDownloaderParamException} from '../../error/exceptions';
import {log} from '../../utils/logger';

// TODO This is bad. All downloaders take an object as arg to constructor which has those params as optional.
// And yet they often treat them as they're always there
// As it's just next to impossible to refactor this without having to check
// 2/3 of the app if it's not broken, I add this safeguard for downloaders to ensure they don't get undefined
// and handle this error clearly right in the constructor.
export function validateDownloader(source: Source, params: Record<string, unknown>): void {
    for (const key of Object.keys(params)) {
        if (params[key] === null || params[key] === undefined) {
            log.debug('Invalid downloader param', {source, key});
            throw new InvalidDownloaderParamException(source, key);
        }
    }
}
