import {LogLevel} from '../../utils/logger';
import {CustomException} from '../CustomException';
import {errorTypes} from '../errorTypes';

export class InvalidContentTypeException extends CustomException {
    constructor(contentType: string, url = 'unspecified url') {
        super({
            message: `Invalid response content type (${contentType}) while downloading report from ${url}`,
            suggestedAction: 'The downloaded file is corrupted.',
            telemetryLogLevel: LogLevel.WARN,
            errorType: errorTypes.TEMPORARY_PORTAL_ISSUE
        });
    }
}
