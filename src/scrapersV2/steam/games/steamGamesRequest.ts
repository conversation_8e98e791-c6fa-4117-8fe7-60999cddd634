import {Stream} from 'stream';
import {AxiosResponse} from 'axios';
import * as cheerio from 'cheerio';
import {InsufficientPrivilegesLevelException, InvalidContentTypeException} from '../../../error/exceptions';
import {HTTPClient, HTTPResponse} from '../../../utils/http';
import {SteamRequestOptions, requestSteamFileWithCorruptionHandling, steamCheerioRequest, steamRequest} from '../common/requests';

export const steamGamesUrl = 'https://partner.steamgames.com';
const defaultLanguageCookie = {Steam_Language: 'english'};

export function impressionsReportHeaderValidator(response: AxiosResponse<Stream> | HTTPResponse): void {
    const contentType = response.headers['content-type'] ? response.headers['content-type'].replace(/\s/g, '').toLowerCase() : '';
    if (
        !['application/octet-stream', 'application/octet-stream;charset=utf-8', 'text/csv;charset=utf-8', 'unknown/unknown;charset=utf-8', 'text/csv'].includes(
            contentType
        )
    ) {
        if (['text/html;charset=utf-8'].includes(contentType) && cheerio.load(response.data)('#appName').text() === 'Unauthorized') {
            throw new InsufficientPrivilegesLevelException();
        }
        throw new InvalidContentTypeException(contentType, response.config.url);
    }
}

export async function downloadSteamGamesFile(httpClient: HTTPClient, path: string, appName?: string) {
    try {
        return await requestSteamFileWithCorruptionHandling({
            httpClient,
            baseUrl: steamGamesUrl,
            path,
            isStream: false,
            followRedirects: false,
            customResponseHeaderValidation: impressionsReportHeaderValidator
        });
    } catch (e) {
        if (appName && e instanceof InsufficientPrivilegesLevelException) {
            throw new InsufficientPrivilegesLevelException(`Missing permission to scrape data for "${appName}"`);
        }
        throw e;
    }
}

export const steamGamesRequest = async <T = any>(path: string, {httpClient, extraCookies, isStream = false, followRedirects = false}: SteamRequestOptions) => {
    return steamRequest<T>({
        baseUrl: steamGamesUrl,
        httpClient,
        path,
        extraCookies: {...extraCookies, ...defaultLanguageCookie},
        isStream,
        followRedirects
    });
};

export async function steamGamesCheerioRequest(path: string, {httpClient, extraCookies}: SteamRequestOptions): Promise<cheerio.CheerioAPI> {
    return steamCheerioRequest(steamGamesUrl, path, httpClient, {...extraCookies, ...defaultLanguageCookie});
}
