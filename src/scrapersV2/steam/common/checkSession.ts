import {HTTPClient} from '../../../utils/http';
import {ScraperContext} from '../../ScraperContext';
import {SteamParams} from './login';
import {SteamSession} from './SteamSession';

export async function checkSteamSession(
    {getHttpClient}: ScraperContext<SteamParams, SteamSession>,
    checkLoginFunction: (httpClient: HTTPClient) => Promise<boolean>,
    retrieveLoggedInUserFunction: (httpClient: HTTPClient) => Promise<string>
) {
    const http = getHttpClient();

    await checkLoginFunction(http);

    return {
        id: await retrieveLoggedInUserFunction(http)
    };
}
