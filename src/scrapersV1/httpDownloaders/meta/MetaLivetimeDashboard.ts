import * as moment from 'moment';
import * as MetaConstants from './MetaConstants';
import {MetaEntitlementRow, MetaSalesRow} from './MetaInterfaces';

export function convertJSONtoLegacyCSV(data: any): string {
    const header = `Date,Country,App Sales Revenue (USD),Copies Sold,Active Users\n`;
    const jsonData = data ?? {};
    if (!jsonData.node) {
        return header;
    }

    const salesData: MetaSalesRow[] = [];
    const entitlementData: MetaEntitlementRow[] = [];

    for (const sale of jsonData.node.sales.nodes) {
        const date = sale?.dimensions?.end_time;
        const country = sale?.dimensions?.country || 'Unknown';
        const revenue = sale?.metrics?.revenue_usd || 0;
        const copiesSold = 0;
        const activeUsers = 0;
        salesData.push({date, country, revenue, copiesSold, activeUsers} as MetaSalesRow);
    }
    for (const entitlement of jsonData.node.entitlements.nodes) {
        const date = entitlement?.dimensions?.end_time;
        const country = entitlement?.dimensions?.country || 'Unknown';
        const copiesSold = entitlement?.metrics?.entitlement_grant_count || 0;
        entitlementData.push({date, country, copiesSold} as MetaEntitlementRow);
    }

    const combinedData: MetaSalesRow[] = [];

    salesData.forEach((sale) => {
        const matchingEntitlement = entitlementData.find((entitlement) => entitlement.date === sale.date && entitlement.country === sale.country);

        if (matchingEntitlement) {
            combinedData.push({
                date: sale.date,
                country: sale.country,
                revenue: sale.revenue,
                copiesSold: sale.copiesSold + matchingEntitlement.copiesSold,
                activeUsers: sale.activeUsers
            });
        } else {
            combinedData.push(sale);
        }
    });

    entitlementData.forEach((entitlement) => {
        const existingRow = combinedData.find((row) => row.date === entitlement.date && row.country === entitlement.country);

        if (!existingRow) {
            combinedData.push({
                date: entitlement.date,
                country: entitlement.country,
                revenue: 0, // No revenue for entitlement-only rows
                copiesSold: entitlement.copiesSold,
                activeUsers: 0 // Placeholder for active users
            });
        }
    });

    let csvContent = header;
    for (const row of combinedData) {
        csvContent += `${moment.unix(row.date).format(MetaConstants.timeFormat)},"${row.country}",$${row.revenue},${row.copiesSold},${row.activeUsers}\n`;
    }

    return csvContent;
}
