{"calls": {"https://partner.steampowered.com/login": [{"body": {"data": "https_partner.steampowered.com/login", "encoding": "file"}, "headers": {"content-security-policy": "default-src blob: data: https: 'unsafe-inline' 'unsafe-eval'; connect-src 'self' https://community.akamai.steamstatic.com/ https://store.steampowered.com/ wss://community.steam-api.com/websocket/ https://api.steampowered.com/ https://login.steampowered.com/ https://help.steampowered.com/ https://*.valvesoftware.com https://*.steambeta.net https://*.steamcontent.com; report-uri /actions/CSPReport", "content-encoding": "gzip"}}, {"status": 302, "req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561198803773815%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.2vbQbNJZ8IVdNsyEbFm0glhva0qeqzhL0liVfcdlI8zgB32JfojcRc-tt86IN5d-zYtML26eIsS0AqdwAp6vAw; sessionid=76b06ef22162e76da1ecf744;"}, "headers": {"content-security-policy": "default-src blob: data: https: 'unsafe-inline' 'unsafe-eval'; connect-src 'self' https://community.akamai.steamstatic.com/ https://store.steampowered.com/ wss://community.steam-api.com/websocket/ https://api.steampowered.com/ https://login.steampowered.com/ https://help.steampowered.com/ https://*.valvesoftware.com https://*.steambeta.net https://*.steamcontent.com; report-uri /actions/CSPReport", "location": "https://partner.steampowered.com/", "vary": ""}}, {"status": 302, "req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561198803773815%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.6nSKC65sSzuTNP_SbZM5SMEgZlXHg5FMFALzwA7l1i6dBRir_HnZpuTR7YLnT2zgiw_U3zQKCj6j0b4KdpiQDg; sessionid=d0320450604f32910ebdb898;"}, "headers": {"content-security-policy": "default-src blob: data: https: 'unsafe-inline' 'unsafe-eval'; connect-src 'self' https://community.akamai.steamstatic.com/ https://store.steampowered.com/ wss://community.steam-api.com/websocket/ https://api.steampowered.com/ https://login.steampowered.com/ https://help.steampowered.com/ https://*.valvesoftware.com https://*.steambeta.net https://*.steamcontent.com; report-uri /actions/CSPReport", "location": "https://partner.steampowered.com/", "vary": ""}}], "https://scraper-api.indiebi.dev/1/auth-code/steam_sales": [{"req_headers": {"authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************.9G8ffpJhQ_VjEAb_SS7N6w_0xM9cQoXV9t0ek1msPGQ", "host": "scraper-api.indiebi.dev", "connection": "keep-alive"}, "body": "{\"authCode\":null}", "headers": {"content-type": "application/json; charset=utf-8", "connection": "keep-alive", "request-context": "appId=cid-v1:1ae09687-f2ab-424c-b8e7-c482afdc19c9", "x-powered-by": "Express", "vary": "Origin", "access-control-allow-credentials": "true", "etag": "W/\"11-CSaGiLTQJ36vkT+ep4rzOLCKeXw\"", "strict-transport-security": "max-age=15724800; includeSubDomains", "server": "", "content-security-policy": ""}}, {"req_headers": {"authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************.9G8ffpJhQ_VjEAb_SS7N6w_0xM9cQoXV9t0ek1msPGQ", "host": "scraper-api.indiebi.dev", "connection": "keep-alive"}, "body": "{\"authCode\":\"QR2CW\"}", "headers": {"content-type": "application/json; charset=utf-8", "connection": "keep-alive", "request-context": "appId=cid-v1:1ae09687-f2ab-424c-b8e7-c482afdc19c9", "x-powered-by": "Express", "vary": "Origin", "access-control-allow-credentials": "true", "etag": "W/\"14-bfAs9d8z1qwAjD2Cpe60k+1B5gc\"", "strict-transport-security": "max-age=15724800; includeSubDomains", "server": "", "content-security-policy": ""}}], "https://partner.steampowered.com/": [{"req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561198803773815%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.2vbQbNJZ8IVdNsyEbFm0glhva0qeqzhL0liVfcdlI8zgB32JfojcRc-tt86IN5d-zYtML26eIsS0AqdwAp6vAw; sessionid=76b06ef22162e76da1ecf744;"}, "body": {"data": "https_partner.steampowered.com/index.html", "encoding": "file"}, "headers": {"content-encoding": "gzip", "set-cookie": "dateStart=2023-08-08; expires=Tue, 08-Aug-2023 16:09:15 GMT; Max-Age=10800; path=/, dateEnd=2023-08-08; expires=Tue, 08-Aug-2023 16:09:15 GMT; Max-Age=10800; path=/, priorDateStart=2023-08-07; expires=Tue, 08-Aug-2023 16:09:15 GMT; Max-Age=10800; path=/, priorDateEnd=2023-08-07; expires=Tue, 08-Aug-2023 16:09:15 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}, {"req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561198803773815%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.6nSKC65sSzuTNP_SbZM5SMEgZlXHg5FMFALzwA7l1i6dBRir_HnZpuTR7YLnT2zgiw_U3zQKCj6j0b4KdpiQDg; sessionid=d0320450604f32910ebdb898;"}, "body": {"data": "https_partner.steampowered.com/index.html", "encoding": "file"}, "headers": {"content-encoding": "gzip", "set-cookie": "dateStart=2023-08-08; expires=Tue, 08-Aug-2023 16:09:19 GMT; Max-Age=10800; path=/, dateEnd=2023-08-08; expires=Tue, 08-Aug-2023 16:09:19 GMT; Max-Age=10800; path=/, priorDateStart=2023-08-07; expires=Tue, 08-Aug-2023 16:09:19 GMT; Max-Age=10800; path=/, priorDateEnd=2023-08-07; expires=Tue, 08-Aug-2023 16:09:19 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}], "https://partner.steampowered.com/nav_games.php": [{"req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561198803773815%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.2vbQbNJZ8IVdNsyEbFm0glhva0qeqzhL0liVfcdlI8zgB32JfojcRc-tt86IN5d-zYtML26eIsS0AqdwAp6vAw; sessionid=76b06ef22162e76da1ecf744; dateStart=2023-08-08;"}, "body": {"data": "https_partner.steampowered.com/nav_games.php", "encoding": "file"}, "headers": {"content-encoding": "gzip", "set-cookie": "dateStart=2023-08-08; expires=Tue, 08-Aug-2023 16:09:17 GMT; Max-Age=10800; path=/, dateEnd=2023-08-07; expires=Tue, 08-Aug-2023 16:09:17 GMT; Max-Age=10800; path=/, priorDateStart=2023-08-08; expires=Tue, 08-Aug-2023 16:09:17 GMT; Max-Age=10800; path=/, priorDateEnd=2023-08-07; expires=Tue, 08-Aug-2023 16:09:17 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}, {"req_headers": {"cookie": "steamCountry=PL%7C46415f4ed36b5daf99b717a545704bc6; steamLoginSecure=76561198803773815%7C%7CeyAidHlwIjogIkpXVCIsICJhbGciOiAiRWREU0EiIH0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.6nSKC65sSzuTNP_SbZM5SMEgZlXHg5FMFALzwA7l1i6dBRir_HnZpuTR7YLnT2zgiw_U3zQKCj6j0b4KdpiQDg; sessionid=d0320450604f32910ebdb898; dateStart=2023-08-08;"}, "body": {"data": "https_partner.steampowered.com/nav_games.php", "encoding": "file"}, "headers": {"content-encoding": "gzip", "set-cookie": "dateStart=2023-08-08; expires=Tue, 08-Aug-2023 16:09:20 GMT; Max-Age=10800; path=/, dateEnd=2023-08-07; expires=Tue, 08-Aug-2023 16:09:20 GMT; Max-Age=10800; path=/, priorDateStart=2023-08-08; expires=Tue, 08-Aug-2023 16:09:20 GMT; Max-Age=10800; path=/, priorDateEnd=2023-08-07; expires=Tue, 08-Aug-2023 16:09:20 GMT; Max-Age=10800; path=/, specialPeriod=deleted; expires=Thu, 01-Jan-1970 00:00:01 GMT; Max-Age=0; path=/"}}]}, "defaults": {"request_headers": {"accept": "application/json, text/plain, */*", "user-agent": "axios/0.27.2", "host": "partner.steampowered.com", "connection": "close"}, "headers": {"server": "nginx", "content-type": "text/html; charset=UTF-8", "content-security-policy": "script-src 'self' 'unsafe-inline' 'unsafe-eval'; object-src 'none'; connect-src 'self'; child-src 'self'; report-uri /actions/cspreport", "vary": "Accept-Encoding", "connection": "close"}}}