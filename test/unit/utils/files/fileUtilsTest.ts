import {join} from 'path';
import {expect} from 'chai';
import * as fse from 'fs-extra';
import * as mockfs from 'mock-fs';
import * as moment from 'moment';
import * as sinon from 'sinon';
import * as configService from '../../../../src/config/ConfigService';
import {Source} from '../../../../src/dataTypes';
import {FileExtension} from '../../../../src/utils/files/FileExtension';
import * as fileUtils from '../../../../src/utils/files/fileUtils';
import {MAX_RENAME_RETRIES} from '../../../../src/utils/files/fileUtils';
import * as sleepUtil from '../../../../src/utils/sleep';
import {runInFakeTime} from '../../../utils/fakeTime';

describe('File utils', () => {
    const validMochaTimeout = 300;
    const downloadsDir = join('.', 'downloads');
    const validFileName = 'fileUtilsTest.ts';
    const validFilePath = join(downloadsDir, validFileName);
    const invalidFileName = 'fileUtilsTest_fake.ts';
    const invalidFilePath = join(downloadsDir, invalidFileName);

    beforeEach(() => {
        sinon.stub(sleepUtil, 'sleep').resolves();
        sinon.stub(configService, 'downloadDirPath').returns(downloadsDir);
    });

    afterEach(() => {
        sinon.restore();
        mockfs.restore();
    });

    it('should check with timeout that file exists', async () => {
        mockfs({
            [validFilePath]: 'fake'
        });

        expect(await runInFakeTime(async () => fileUtils.checkFileExists(validFileName, true, validMochaTimeout))).to.be.true;
    });

    it('should check with timeout that file does not exist', async () => {
        expect(await runInFakeTime(async () => fileUtils.checkFileExists(invalidFilePath, true, validMochaTimeout))).to.be.false;
    });

    it('should timeout while checking if file exists and return false', async () => {
        expect(await fileUtils.checkFileExists(validFilePath, true, 1)).to.be.false;
    });

    it('should check that file exists', async () => {
        mockfs({
            [validFilePath]: 'fake'
        });

        expect(await fileUtils.checkFileExists(validFileName)).to.be.true;
    });

    it('should check that file does not exists', async () => {
        expect(await fileUtils.checkFileExists(invalidFilePath)).to.be.false;
    });

    it('should create a zip file in specified location', async () => {
        mockfs({
            'downloads/fake.xls': 'fake',
            'downloads/fake2.xls': 'fake'
        });
        const filePath = 'file.zip';
        await fileUtils.zipFiles(['fake.xls', 'fake2.xls'], filePath);
        expect(await fileUtils.checkFileExists(filePath)).to.be.true;
    });

    const startDateAsString = '2020-01-01';

    it('should generate proper file name', () => {
        const input = {
            startDate: moment(startDateAsString),
            endDate: moment(startDateAsString).add(1, 'day')
        };
        const result = fileUtils.generateFileName(Source.STEAM_SALES, input.startDate, input.endDate, FileExtension.CSV);
        expect(result).to.be.equal(`${Source.STEAM_SALES}-2020-01-01_2020-01-02.${FileExtension.CSV}`);
    });

    ['\\', '/', ':', '*', '?', '"'].map((character) => {
        it(`should remove forbidden character ( ${character} ) from additional index`, () => {
            const dates = {
                startDate: moment(startDateAsString),
                endDate: moment(startDateAsString).add(1, 'day')
            };
            const result = fileUtils.generateFileName(Source.STEAM_SALES, dates.startDate, dates.endDate, FileExtension.CSV, `Test${character}`);
            expect(result).to.be.equal(`${Source.STEAM_SALES}-2020-01-01_2020-01-02-Test.${FileExtension.CSV}`);
        });
    });

    [
        {input: '<', output: 'less'},
        {input: '>', output: 'greater'},
        {input: '|', output: 'or'},
        {input: '♥', output: 'love'}
    ].map(({input, output}) => {
        it(`should slugify special unicode character ( ${input} )`, () => {
            const dates = {
                startDate: moment(startDateAsString),
                endDate: moment(startDateAsString).add(1, 'day')
            };
            const result = fileUtils.generateFileName(Source.STEAM_SALES, dates.startDate, dates.endDate, FileExtension.CSV, `Test${input}`);
            expect(result).to.be.equal(`${Source.STEAM_SALES}-2020-01-01_2020-01-02-Test${output}.${FileExtension.CSV}`);
        });
    });

    describe('renameFile', async () => {
        it('should successfully rename file', async () => {
            mockfs({
                [validFilePath]: 'fake'
            });
            await fileUtils.renameFile(validFileName, `${validFileName}-1`);
            expect(await fileUtils.checkFileExists(`${validFileName}-1`)).to.be.true;
            expect(await fileUtils.checkFileExists(validFileName)).to.be.false;
        });

        it('should successfully rename file after EBUSY error retry', async () => {
            mockfs({
                [validFilePath]: 'fake'
            });
            const {rename} = fse;
            sinon.stub(fse, 'rename').callsFake(rename).onFirstCall().rejects({code: 'EBUSY'});
            await fileUtils.renameFile(validFileName, `${validFileName}-1`);
            expect(await fileUtils.checkFileExists(`${validFileName}-1`)).to.be.true;
            expect(await fileUtils.checkFileExists(validFileName)).to.be.false;
        });

        it(`should throw EBUSY error after ${MAX_RENAME_RETRIES} retries`, async () => {
            const expectedError = {code: 'EBUSY'};
            mockfs({
                [validFilePath]: 'fake'
            });
            sinon.stub(fse, 'rename').callsFake(() => {
                throw expectedError;
            });
            try {
                await fileUtils.renameFile(validFileName, `${validFileName}-1`);
            } catch (error) {
                expect(error).to.be.deep.equal(expectedError);
                expect(await fileUtils.checkFileExists(`${validFileName}-1`)).to.be.false;
                expect(await fileUtils.checkFileExists(validFileName)).to.be.true;
            }
        });

        it('should rethrow error different than EBUSY', async () => {
            const expectedError = new Error();
            mockfs({
                [validFilePath]: 'fake'
            });
            sinon.stub(fse, 'rename').callsFake(() => {
                throw expectedError;
            });
            try {
                await fileUtils.renameFile(validFileName, `${validFileName}-1`);
            } catch (error) {
                expect(error).to.be.deep.equal(expectedError);
                expect(await fileUtils.checkFileExists(`${validFileName}-1`)).to.be.false;
                expect(await fileUtils.checkFileExists(validFileName)).to.be.true;
            }
        });
    });
});
