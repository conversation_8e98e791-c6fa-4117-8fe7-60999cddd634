import {assert} from 'chai';
import * as td from 'testdouble';
import {<PERSON>rowser} from '../../../src/browser/Browser';
import {Source} from '../../../src/dataTypes';
import {GOGReportDownloader} from '../../../src/scrapersV1/httpDownloaders/gog/GOGReportDownloader';
import {MetaReportDownloader} from '../../../src/scrapersV1/httpDownloaders/meta/MetaReportDownloader';
import {getDownloader} from '../../../src/scrapersV2/reportDownloadAdapter/DownloaderFactory';

describe('Downloader factory test', () => {
    describe('Get downloader for platform', () => {
        [
            {source: Source.META_QUEST_SALES, expectedDownloader: MetaReportDownloader},
            {source: Source.GOG_SALES, expectedDownloader: GOGReportDownloader}
        ].forEach((item) => {
            it(`should select the right downloader for ${item.source}`, async () => {
                const downloader = getDownloader({login: 'login', password: 'pass', browser: td.object<Browser>(), appId: 'app', source: item.source});
                assert.instanceOf(downloader, item.expectedDownloader);
            });
        });
    });
});
