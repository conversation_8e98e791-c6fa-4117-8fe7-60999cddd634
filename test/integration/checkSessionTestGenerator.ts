import {expect} from 'chai';
import {CLIArguments, Command} from '../../src/cli/arguments';
import {generateApiToken} from './utils/ArgsProvider';
import {runCliMainWithConvertedArgs} from './utils/Utils';

export function createCheckSessionTest(loginArgs: CLIArguments, expectation: any, loginAlreadyPerformed = false) {
    return it(`should verify session for ${loginArgs.source}`, async function () {
        loginArgs.apiToken = await generateApiToken();

        if (!loginAlreadyPerformed) {
            await runCliMainWithConvertedArgs(loginArgs);
        }
        const result = await runCliMainWithConvertedArgs({...loginArgs, command: Command.CHECK_SESSION});
        expect(result).to.be.deep.eq(expectation);
    });
}
