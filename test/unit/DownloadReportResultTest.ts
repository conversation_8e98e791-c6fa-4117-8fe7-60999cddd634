import {expect} from 'chai';
import * as moment from 'moment';
import {Source} from '../../src/dataTypes';
import * as DownloadReportResult from '../../src/scrapersV1/DownloadReportResult';
import {LoginResult} from '../../src/scrapersV1/LoginResult';
import {Report} from '../../src/scrapersV1/Report';

describe('Download report result should:', () => {
    const startDate = moment().utc();
    const endDate = moment().add(1, 'days').utc();

    it('create a report result', async () => {
        const sampleReports = [new Report(Source.STEAM_SALES, 'downloadReportResult', startDate, endDate)];
        const downloadReportResult = DownloadReportResult.DownloadReportResult.createReportResult(sampleReports);
        expect(downloadReportResult.getReports()).to.be.deep.equal(sampleReports);
        expect(downloadReportResult.getInvalidAppIdError()).to.be.undefined;
    });

    it('create a login error result', async () => {
        const downloadReportResult = DownloadReportResult.DownloadReportResult.createLoginErrorResult(LoginResult.create2FactorAuthResult());
        expect(downloadReportResult.isReport()).to.be.false;
        expect(downloadReportResult.isAuthenticationException()).to.be.true;
        expect(downloadReportResult.isInvalidAppException()).to.be.false;
    });

    it('check that result is an Invalid Aplication result', async () => {
        const downloadReportResult = DownloadReportResult.DownloadReportResult.createInvalidApplicationResult('Invalid App');
        expect(downloadReportResult.isReport()).to.be.false;
        expect(downloadReportResult.isAuthenticationException()).to.be.false;
        expect(downloadReportResult.isInvalidAppException()).to.be.true;
    });
});
