import {expect} from 'chai';
import {isEveryPropertyFulfilled, toCamelCase} from '../../../src/utils/objectOperations';

describe('timeoutPrompt should:', () => {
    [
        {object: {a: ''}, result: false},
        {object: {a: 'a'}, result: true},
        {object: {a: '', b: ''}, result: false},
        {object: {a: '', b: 'a'}, result: false},
        {object: {a: 'a', b: 'a'}, result: true}
    ].forEach((item) => {
        it('should check if object has empty properties', () => {
            expect(isEveryPropertyFulfilled(item.object as any)).to.be.equal(item.result);
        });
    });
});

describe('toCamelCase', () => {
    it('returns the same object if it is not an object', () => {
        const str = 'hello';
        expect(toCamelCase(str)).equal(str);
    });

    it('if passed element is an array, toCamelCase is called on each element of an array ', () => {
        const arr = [{first_name: '<PERSON>', last_name: '<PERSON><PERSON>'}, {person_info: {first_name: '<PERSON>', last_name: '<PERSON><PERSON>'}}];
        expect(toCamelCase(arr)).to.be.deep.equal([{firstName: 'John', lastName: 'Doe'}, {personInfo: {firstName: 'John', lastName: 'Doe'}}]);
    });

    it('converts snake_case keys to camelCase', () => {
        const obj = {first_name: 'John', last_name: 'Doe'};
        expect(toCamelCase(obj)).to.be.deep.equal({firstName: 'John', lastName: 'Doe'});
    });

    it('converts nested objects to camelCase', () => {
        const obj = {person_info: {first_name: 'John', last_name: 'Doe'}};
        expect(toCamelCase(obj)).to.be.deep.equal({personInfo: {firstName: 'John', lastName: 'Doe'}});
    });
});
