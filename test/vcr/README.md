## Development

-   install Python 3.10:
    **Ubuntu 20:**

    ```
    $ sudo apt update
    $ sudo apt install software-properties-common -y
    $ sudo add-apt-repository ppa:deadsnakes/ppa
    $ sudo apt install python3.10
    # verify with
    $ python3.10 --version
    ```

-   install Poetry:

    ```shell
    curl -sSL https://install.python-poetry.org | python3 -
    ```

-   Create the environment and install dependencies using poetry:
    `$ cd test/vcr`
    `$ poetry install`

-   Enter the environment using
    `$ poetry shell`

-   **VSCode**
    `vcr-proxy` has its own `.vscode` config file. Open this directory directly in VSCode for these settings to load.


## Before running tests

1. Go to the https://gitlab.com/-/profile/personal_access_tokens
1. Generate a new token (select only `api`, and set an expiration date)
1. Set up a generated token in your `.env` file or in your shell; e.g. as a value of the `GITLAB_PAT` variable
1. Install the required VCR library using the command: `poetry run pip install scrapers-vcr --index-url https://__token__:${GITLAB_PAT}@gitlab.com/api/v4/projects/45807331/packages/pypi/simple`
## Running tests

- Run playback tests: `vcr --mode=playback` or just `vcr pytest`
- Run live tests: `vcr --mode=live pytest -m live`
- Re-record a session using a test: `vcr --mode=record tests/steam_sales/steam_fullpermission`
- Run specific test: `vcr test/\*\*`
