import {Source} from '../../../dataTypes/Source';

export interface MetaApp {
    id: string;
    name: string;
    release_date: number | null;
    is_test: boolean;
    source: Source;
}

export interface MetaReportFile {
    productName: string;
    productId: string;
    fileName: string;
    noSales: boolean;
}

export interface MetaRowBase {
    date: EpochTimeStamp;
    country: string;
}

export interface MetaSalesRow extends MetaRowBase {
    revenue: number;
    copiesSold: number;
    activeUsers: number;
}

export interface MetaEntitlementRow extends MetaRowBase {
    copiesSold: number;
}

export interface MetaLegacyDashboardRevenue {
    data: {
        app_store_item: {
            __typename: string;
            display_name: string;
            revenue_analytics_data: {
                app_revenue: {
                    total_revenue: number;
                    wow_revenue: number | null;
                    mom_revenue: number | null;
                    time_series_data: LegacyDashboardRevenueTimeSeriesData[];
                };
            };
            id: string;
        };
    };
    extensions: {
        is_final: boolean;
    };
}

interface LegacyDashboardRevenueTimeSeriesData {
    revenue: number;
    timestamp: number;
    copies_sold: number;
    country: string;
    active_users: number;
}

/**
 * MetaRealTimeRevenue is the type of the minimal required data returned by the GraphQL query
 */
export interface MetaRealTimeRevenue {
    node: {
        sales: {
            nodes: {
                dimensions: {
                    end_time: string;
                    country: string;
                };
                metrics: {
                    revenue_usd: number;
                };
            }[];
        };
        entitlements: {
            nodes: {
                dimensions: {
                    end_time: string;
                    country: string;
                };
                metrics: {
                    entitlement_grant_count: number;
                };
            }[];
        };
    };
}
