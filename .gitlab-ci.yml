# pipeline types:
#
# MR - ran on a merge request event. Check the incoming MR before merging.
# Release - on a release tag, prepares a release candidate package.
# Nightly - scheduled run of integration tests (integration tests can fail without code changes because the portal APIs/sites have changed)
#         - longer static analysis, including security audits

workflow:
    rules:
        - if: '$CI_PIPELINE_SOURCE == "schedule"'
          variables:
              PIPELINE: Nightly
        - if: '$CI_COMMIT_TAG =~ /^\d+\.\d+\.\d+(-.*)?$/i'
          variables:
              PIPELINE: Release
        - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
          variables:
              PIPELINE: MR
        - if: '$CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS'
          when: never
        - if: '$CI_COMMIT_BRANCH'
          # for pushes to branches that are not covered by a MR, run an MR pipeline anyway
          variables:
              PIPELINE: MR

variables:
    GIT_DEPTH: 1
    TRANSFER_METER_FREQUENCY: '1s'
    ARTIFACT_COMPRESSION_LEVEL: 'default'
    CACHE_COMPRESSION_LEVEL: 'fastest'
    CHROMIUM_REVISION: 970485
    CHROME_FOR_TESTING: '129.0.6668.89'
    NODE_VERSION: '20.10.0'
    NOTIFICATIONS_WEBHOOK_URL: 'https://indiebisa.webhook.office.com/webhookb2/2da8a82e-4313-45b2-9a6c-1b67cff8b7b2@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/bfa1dad091b44d56b4190fde7e2f4725/bfaa9ba4-f9f4-4062-bfbd-01fc2a14278d'

include:
    - local: '/ci-cd/gitlab/build.yml'
    - local: '/ci-cd/gitlab/integration-test.yml'
    #    - local: '/ci-cd/gitlab/vcr-test.yml'
    - local: '/ci-cd/gitlab/test.yml'
    - local: '/ci-cd/gitlab/package.yml'
    - local: '/ci-cd/gitlab/deploy.yml'
    - local: '/ci-cd/gitlab/report.yml'
    - local: '/ci-cd/gitlab/audit.yml'
    - local: '/ci-cd/gitlab/coverage.yml'
    - local: '/ci-cd/gitlab/acceptance-test.yml'

image: crindiebimain.azurecr.io/dpt/ci-cd/scrapers-js-node-$NODE_VERSION-chromium-$CHROME_FOR_TESTING:prod

stages:
    - audit
    - build
    - test
    - package
    - acceptance-test
    - deploy

default:
    tags:
        - dpt-azure
