import * as chai from 'chai';
import * as chaiAsPromised from 'chai-as-promised';
import moment = require('moment');
import * as td from 'testdouble';
import {Browser} from '../../../../src/browser/Browser';
import {<PERSON>rame} from '../../../../src/browser/IFrame';
import {Invalid2FACode} from '../../../../src/error/exceptions/Invalid2FACode';
import {ElementHandle, JSHandle} from '../../../../src/puppeteer';
import * as GOGConstants from '../../../../src/scrapersV1/httpDownloaders/gog/GOGConstants';
import {GOGReportDownloader} from '../../../../src/scrapersV1/httpDownloaders/gog/GOGReportDownloader';
import {LoginResult} from '../../../../src/scrapersV1/LoginResult';

chai.use(chaiAsPromised);
const {expect} = chai;

describe('GogReportDownloaderLogin should:', function () {
    const login = 'login';
    const password = 'password';
    const fake2fa = '1234';
    const startDate = moment().subtract(1, 'day');
    const endDate = moment().add(1, 'day');

    const prepareBrowserForLogin = (
        browser: Browser,
        isPasswordOk = true,
        is2FAPresent = false,
        isCaptchaPresent = false,
        hasSuccessfullyLoggedIn = true,
        isUsernameDisabled = false
    ): void => {
        const fakeFrame = td.object<IFrame>();
        const fakeElement = td.object<ElementHandle<Element>>();
        const fakeCaptchaElement = td.object<ElementHandle<Element>>();
        const fakeUsernameHandle = td.object<JSHandle>();

        td.when(fakeFrame.querySelector(GOGConstants.loginCaptchaSelector)).thenResolve(fakeCaptchaElement);
        td.when(fakeCaptchaElement.isIntersectingViewport({threshold: 0})).thenResolve(isCaptchaPresent);
        td.when(fakeFrame.querySelector(GOGConstants.usernameInputSelector)).thenResolve(fakeElement);
        td.when(fakeElement.getProperty('disabled')).thenResolve(fakeUsernameHandle);
        td.when(fakeUsernameHandle.jsonValue()).thenResolve(isUsernameDisabled);
        td.when(fakeFrame.elementExists(GOGConstants.incorrectPasswordXPathSelector, td.matchers.isA(Number))).thenResolve(!isPasswordOk);
        td.when(fakeFrame.elementExists(GOGConstants.mailCodeInputSelector)).thenResolve(is2FAPresent);
        td.when(browser.getIFrame()).thenReturn(fakeFrame);
        td.when(browser.elementExists(GOGConstants.logoutButton)).thenResolve(false, hasSuccessfullyLoggedIn);
    };

    const verifyBrowserForLogin = (browser: Browser): void => {
        td.verify(browser.getIFrame().clearAndType(GOGConstants.usernameInputSelector, login));
        td.verify(browser.getIFrame().clearAndType(GOGConstants.passwordInputSelector, password));
        td.verify(browser.getIFrame().click(GOGConstants.loginButtonSelector));
    };

    afterEach(td.reset);

    it('properly fill second authentication factor', async () => {
        const fakeBrowser = td.object<Browser>();
        td.when(fakeBrowser.elementExists(GOGConstants.logoutButton)).thenResolve(true);
        const fakeFrame = td.object<IFrame>();
        td.when(fakeBrowser.getIFrame()).thenReturn(fakeFrame);

        const gogReportDownloader = new GOGReportDownloader({login, password, browser: fakeBrowser});
        await gogReportDownloader.fillSecondAuthenticationFactor(fake2fa);

        td.verify(fakeBrowser.getIFrame().type(GOGConstants.mailCodeInputSelector, fake2fa));
        td.verify(fakeBrowser.getIFrame().click(GOGConstants.mailCodeConfirmationButtonSelector));
    });

    it('properly validate second authentication factor code', async () => {
        const fakeBrowser = td.object<Browser>();
        td.when(fakeBrowser.elementExists(GOGConstants.logoutButton)).thenResolve(true);
        const fakeFrame = td.object<IFrame>();
        td.when(fakeBrowser.getIFrame()).thenReturn(fakeFrame);

        const gogReportDownloader = new GOGReportDownloader({login, password, browser: fakeBrowser});

        await expect(gogReportDownloader.fillSecondAuthenticationFactor('1')).to.be.rejectedWith(new Invalid2FACode().message);
    });

    it('properly fill login', async () => {
        const fakeBrowser = td.object<Browser>();
        const gogReportDownloader = new GOGReportDownloader({login, password, browser: fakeBrowser});
        td.when(fakeBrowser.elementExists(GOGConstants.logoutButton)).thenResolve(false);
        prepareBrowserForLogin(fakeBrowser);
        const loginResult: LoginResult = await gogReportDownloader.performLogin();

        verifyBrowserForLogin(fakeBrowser);
        expect(loginResult.isFailedLogin()).to.be.false;
    });

    it('properly return credential error', async () => {
        const fakeBrowser = td.object<Browser>();
        const gogReportDownloader = new GOGReportDownloader({login, password, browser: fakeBrowser});
        prepareBrowserForLogin(fakeBrowser, false);
        const loginResult: LoginResult = await gogReportDownloader.performLogin();

        verifyBrowserForLogin(fakeBrowser);
        expect(loginResult.isAuthenticationException()).to.be.true;
    });

    it('properly return 2FA Error', async () => {
        const fakeBrowser = td.object<Browser>();
        const gogReportDownloader = new GOGReportDownloader({login, password, browser: fakeBrowser});
        prepareBrowserForLogin(fakeBrowser, true, true);
        const loginResult: LoginResult = await gogReportDownloader.performLogin();

        verifyBrowserForLogin(fakeBrowser);
        expect(loginResult.is2FactorAuth()).to.be.true;
    });

    it('properly return Captcha Error', async () => {
        const fakeBrowser = td.object<Browser>();
        const gogReportDownloader = new GOGReportDownloader({login, password, browser: fakeBrowser});
        prepareBrowserForLogin(fakeBrowser, true, false, true);

        const loginResult: LoginResult = await gogReportDownloader.performLogin();

        expect(loginResult.isCaptchaError()).to.be.true;
    });

    it('properly tell user that login did not succeeded', async () => {
        const fakeBrowser = td.object<Browser>();
        const gogReportDownloader = new GOGReportDownloader({login, password, browser: fakeBrowser});
        prepareBrowserForLogin(fakeBrowser, true, false, false, false);
        const loginResult: LoginResult = await gogReportDownloader.performLogin();

        verifyBrowserForLogin(fakeBrowser);
        expect(loginResult.isAuthenticationException()).to.be.true;
        expect(loginResult.getAuthenticationErrorString()).to.be.equal('Login failed. Invalid credentials or too many login attempts in a row.');
    });

    it('should return authorization error when using invalid credentials', async () => {
        const fakeBrowser = td.object<Browser>();
        prepareBrowserForLogin(fakeBrowser, false, true, false, false);
        const steamReportDownloader = new GOGReportDownloader({login, password, browser: fakeBrowser});
        const result = await steamReportDownloader.downloadReport(startDate, endDate);
        td.verify(fakeBrowser.goto(GOGConstants.salesSummaryReportPageUrl));
        verifyBrowserForLogin(fakeBrowser);
        expect(result.isAuthenticationException()).to.be.true;
    });

    it('properly fill login when username is disabled', async () => {
        const fakeBrowser = td.object<Browser>();
        const gogReportDownloader = new GOGReportDownloader({login, password, browser: fakeBrowser});
        td.when(fakeBrowser.elementExists(GOGConstants.logoutButton)).thenResolve(false);
        prepareBrowserForLogin(fakeBrowser, true, false, false, true, false);
        const loginResult: LoginResult = await gogReportDownloader.performLogin();

        verifyBrowserForLogin(fakeBrowser);
        expect(loginResult.isFailedLogin()).to.be.false;
    });
});
