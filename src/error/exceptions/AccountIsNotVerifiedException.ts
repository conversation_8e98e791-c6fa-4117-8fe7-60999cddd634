import {LogLevel} from '../../utils/logger';
import {CustomException} from '../CustomException';
import {errorTypes} from '../errorTypes';

export class AccountIsNotVerifiedException extends CustomException {
    constructor() {
        super({
            message: 'Your account is not verified.',
            suggestedAction: 'Please log in to your Meta account and verify it.',
            telemetryLogLevel: LogLevel.WARN,
            errorType: errorTypes.MANUAL_ACTION_REQUIRED
        });
    }
}
