import {initAxiosInstance, useProxy} from '../apiCommunication/axios';
import {getAppVersion} from '../config/ConfigService';
import {Portal, Source} from '../dataTypes';
import {SourceIdentifier, SourceSideOrganization} from '../dataTypes/SourceSideOrganization';
import {CustomException} from '../error/CustomException';
import {errorTypes} from '../error/errorTypes';
import {Report} from '../scrapersV1/Report';
import ManualLoginDetails from '../scrapersV2/ManualLoginDetails';
import {ProgressCallback} from '../scrapersV2/ProgressCallback';
import {ScraperProxy} from '../scrapersV2/ScraperProxy';
import init, {prepareDefaultSentryInitOptions} from '../Sentry';
import {sanitizeArgsForDebug} from '../telemetry/cli/utils';
import {SessionFile} from '../utils/files/JsonFile';
import {log} from '../utils/logger';
import {retryAction} from '../utils/retryAction';
import {CLIArguments, Command} from './arguments';
import {exitCode} from './exitCodes';
import {DualAuthMethod, printDualAuth, printDualAuthSuccess, printError, printResult, printTraceWithError, setBasicPrintingParams} from './messaging';

type CliCommandFunction<T = void> = (args: CLIArguments, progress: ProgressCallback) => Promise<T>;
type CliCommandFunctionWithScraperProxy<T = void> = (scraperProxy: ScraperProxy, args: CLIArguments, progress: ProgressCallback) => Promise<T>;

function commandWithScraperProxy<T>(func: CliCommandFunctionWithScraperProxy<T>): CliCommandFunction<T> {
    return async (args, progress): Promise<T> => {
        const paramsMap: Map<string, any> = new Map(Object.entries(args.credentials ?? {}));
        paramsMap.set('ignoredProducts', args.excludedSkus?.length ? args.excludedSkus : []);
        paramsMap.set('ignoredOrganizations', args.excludedOrgs?.length ? args.excludedOrgs : []);
        paramsMap.set('featureFlags', args.featureFlags?.length ? args.featureFlags : []);
        const sessionFile = new SessionFile(args.sessionFile!, args.encrypt ? args.encryptionToken : undefined);
        let outputSessionFile = sessionFile;
        if (args.outputSessionFile) {
            outputSessionFile = new SessionFile(args.outputSessionFile, args.encrypt ? args.encryptionToken : undefined);
        }
        const proxyUrl = args.proxyUrl;
        useProxy(proxyUrl);

        const scraper = await ScraperProxy.create({
            source: args.source.toLowerCase(),
            progress,
            paramsMap,
            sessionFile,
            outputSessionFile,
            proxyUrl
        });

        try {
            return await func(scraper, args, progress);
        } finally {
            await scraper.close();
        }
    };
}

const loginCommand: CliCommandFunctionWithScraperProxy<SourceIdentifier> = async (scraperProxy) => {
    return scraperProxy.login();
};

const checkSessionCommand: CliCommandFunctionWithScraperProxy<SourceIdentifier> = async (scraperProxy) => {
    return scraperProxy.checkSession();
};

const getSourceSideOrganizationsCommand: CliCommandFunctionWithScraperProxy<SourceSideOrganization[]> = async (scraperProxy) => {
    return scraperProxy.getSourceSideOrganizations();
};

const getManualLoginDataCommand: CliCommandFunction<ManualLoginDetails | undefined> = async (args: CLIArguments, progress: ProgressCallback) => {
    progress('Getting manual login data');
    const scraper = await ScraperProxy.getRawScraperWithoutContext(args.source as Source);
    return scraper.manualLoginDetails;
};

const getExampleOutputCommand: CliCommandFunction<SourceIdentifier> = async (_args: CLIArguments, progress: ProgressCallback) => {
    progress('Generate example output');
    log.debug('Example debug with additional data', {data: [1, 2, 3]});
    log.info('Example info log');
    log.debug('Example debug log');
    log.warning('Example warning log');
    //log.error('Example error log');

    printDualAuth({portal: Portal.EPIC, attempt: 1, maxAttempts: 5});
    printDualAuth({portal: Portal.STEAM, attempt: 1, maxAttempts: 1, authMethod: DualAuthMethod.MOBILE_APP_APPROVAL});
    printDualAuthSuccess({attempt: 1, authMethod: DualAuthMethod.MOBILE_APP_APPROVAL});

    const exception = new CustomException({message: 'test', additionalErrorData: {asd: [1, 2, 4]}, errorType: errorTypes.SESSION_EXPIRED});
    printError(exception);
    printResult(null);
    return {id: 'example-payload'};
};

const scrapeCommand: CliCommandFunctionWithScraperProxy<Report[]> = async (scraperProxy, args) => {
    try {
        return await scraperProxy.scrape(args.from!, args.to!);
    } catch (error) {
        log.debug('Scrape command failed', sanitizeArgsForDebug(args));
        printTraceWithError(error);
        throw error;
    }
};

const commands: Record<Command, CliCommandFunction<unknown>> = {
    [Command.LOGIN]: commandWithScraperProxy(loginCommand),
    [Command.CHECK_SESSION]: commandWithScraperProxy(checkSessionCommand),
    [Command.GET_SOURCE_SIDE_ORGANIZATIONS]: commandWithScraperProxy(getSourceSideOrganizationsCommand),
    [Command.SCRAPE]: commandWithScraperProxy(scrapeCommand),
    [Command.GET_MANUAL_LOGIN_DETAILS]: getManualLoginDataCommand,
    [Command.EXAMPLE_OUTPUT]: getExampleOutputCommand
};

export async function main(args: CLIArguments, runWithinTests = false): Promise<void | unknown> {
    const appVersion = getAppVersion();

    setBasicPrintingParams({
        source: args.source,
        originId: `Scraper-js-${appVersion}`
    });

    if (args.apiUrl) {
        //This should be removed once api url is passed as a whole
        const apiUrlVersionString = '/1';
        const apiUrl = args.apiUrl.endsWith(apiUrlVersionString) ? args.apiUrl : args.apiUrl + apiUrlVersionString;
        initAxiosInstance(apiUrl);
    }

    if (args.sentryParams) {
        log.debug('Initializing sentry connection with CLI options');
        init({version: appVersion, ...args.sentryParams});
    } else {
        log.debug('Initializing sentry connection with default options');
        init(prepareDefaultSentryInitOptions(appVersion));
    }

    const result = await retryAction({
        target: async () => commands[args.command](args, log.info),
        maxAttempts: 4,
        delay: 2000,
        retryCondition: (error) => (error instanceof CustomException ? error.canBeFixedByRetry : true),
        logger: log.info,
        label: `${args.command} command for ${args.source}`
    });

    printResult(result);

    if (runWithinTests) {
        return result;
    } else {
        process.exit(exitCode.SUCCESS);
    }
}
