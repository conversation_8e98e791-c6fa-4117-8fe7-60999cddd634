import * as fs from 'fs/promises';
import * as chai from 'chai';
import * as chaiAsPromised from 'chai-as-promised';
import * as mockfs from 'mock-fs';
import * as td from 'testdouble';
import {EncryptedJsonFile, SessionFile} from '../../../../src/utils/files/JsonFile';

const expect = chai.expect;
chai.use(chaiAsPromised);

const SESSION_FILE_NAME = 'session.json';

describe('Encrypted JSON file test', function () {
    afterEach(() => {
        mockfs.restore();
        td.reset();
    });

    it('should load session from a file', async function () {
        const savedSession = {cookies: [{name: 'foo', value: 'bar'}]};
        mockfs({
            [SESSION_FILE_NAME]: JSON.stringify(savedSession)
        });

        const sessionFile = new EncryptedJsonFile(SESSION_FILE_NAME);
        const session = await sessionFile.load();

        expect(session).to.deep.equal(savedSession);
    });

    it('should fail to load empty file', async function () {
        mockfs({
            [SESSION_FILE_NAME]: '\0'
        });

        const sessionFile = new EncryptedJsonFile(SESSION_FILE_NAME);

        await expect(sessionFile.load()).to.eventually.rejectedWith('Cannot load file');
    });

    it('should return empty object when file does not exist', async function () {
        mockfs();

        const sessionFile = new EncryptedJsonFile(SESSION_FILE_NAME);
        const result = await sessionFile.load();

        expect(result).to.be.deep.equal({});
    });

    it('should return session if validator returns true', async function () {
        const session = {test: 'test'};
        mockfs({
            [SESSION_FILE_NAME]: JSON.stringify(session)
        });
        const sessionFile = new EncryptedJsonFile(SESSION_FILE_NAME);
        const result = await sessionFile.load();
        expect(result).to.be.deep.equal(session);
    });

    it('should save session to a file', async function () {
        mockfs();

        const session = {cookies: [<any>{name: 'foo', value: 'bar'}]};
        const sessionFile = new EncryptedJsonFile(SESSION_FILE_NAME);
        await sessionFile.save(session);

        const readBack = JSON.parse((await fs.readFile(SESSION_FILE_NAME)).toString());

        expect(readBack).to.deep.equal(session);
    });

    it('should fail to save an undefined session', async function () {
        mockfs();

        const session = undefined;
        const sessionFile = new EncryptedJsonFile(SESSION_FILE_NAME);
        await expect(sessionFile.save(session)).to.eventually.be.rejectedWith('Cannot save file');
    });

    it('should not fail to save an undefined session', async function () {
        mockfs();

        const session = undefined;
        const sessionFile = new EncryptedJsonFile(SESSION_FILE_NAME);
        await expect(sessionFile.save(session, false)).to.eventually.not.be.rejected;
    });

    it('should fail to save an undefined session', async function () {
        mockfs();

        const session = undefined;
        const sessionFile = new SessionFile(SESSION_FILE_NAME);
        await expect(sessionFile.save(session)).to.eventually.be.rejectedWith('Cannot save session file');
    });

    it('should not fail to save an undefined session', async function () {
        mockfs();

        const session = undefined;
        const sessionFile = new SessionFile(SESSION_FILE_NAME);
        await expect(sessionFile.save(session, false)).to.eventually.not.be.rejected;
    });

    it('should encrypt a file', async function () {
        mockfs();
        const originalSession = {cookies: [<any>{name: 'foo', value: 'bar'}]};
        const sessionFile1 = new EncryptedJsonFile(SESSION_FILE_NAME, 'rightKey');
        await sessionFile1.save(originalSession);

        // encryption uses a random seed (rightfully so), so the only way to test encryption
        // is to read the file we just wrote
        const sessionFileWrongKey = new EncryptedJsonFile(SESSION_FILE_NAME, 'wrongKey');
        await expect(sessionFileWrongKey.load()).to.be.rejectedWith('Unsupported state or unable to authenticate data');

        const sessionFileRightKey = new EncryptedJsonFile(SESSION_FILE_NAME, 'rightKey');
        const readSession = await sessionFileRightKey.load();

        expect(readSession).to.deep.equal(originalSession);
    });
});
