import {once} from 'events';
import * as fs from 'fs';
import {Readable} from 'stream';
import * as archiver from 'archiver';

/**
 * Build a zip file from readable streams without intermediary files on disk.
 * Writes partial zip data to file instead of keeping the whole zip in memory.
 *
 * @param filename the file name/path of the zip to build.
 * @param callback after the zip is open, zipBuilder will call `callback(append)`,
 * where append is an async function that takes a readable stream and a filename as parameters.
 * Call `await append(content, name)` inside the callback for each zip entry you want to write.
 */
export const zipBuilder = async function (
    filename: fs.PathLike,
    callback: (append: (content: string | Readable | Buffer, name: string) => Promise<void>) => Promise<void>
) {
    const output = fs.createWriteStream(filename);
    const archive = archiver('zip');

    return new Promise((resolve, reject) => {
        archive.on('warning', reject);
        archive.on('error', reject);
        output.on('error', reject);
        output.on('close', resolve);
        archive.pipe(output);

        const append = async function (content: string | Readable | Buffer, name: string) {
            const a = once(archive, 'entry');
            archive.append(content, {name});
            await a;
        };

        callback(append)
            .then(() => {
                return archive.finalize();
            })
            .catch(reject);
    }).catch((err) => {
        if (!output.writableEnded) {
            output.end();
        }
        throw err;
    });
};
