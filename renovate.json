{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:base"], "rebaseWhen": "never", "prConcurrentLimit": 4, "prHourlyLimit": 1, "prCreation": "immediate", "stabilityDays": 7, "packageRules": [{"packagePatterns": ["@typescript-eslint/eslint-plugin", "@typescript-eslint/eslint-plugin-tslint", "@typescript-eslint/parser"], "groupName": "eslint"}, {"packagePatterns": ["@types/fs-extra", "fs-extra"], "groupName": "fs-extra"}, {"packagePatterns": ["@types/jszip", "j<PERSON><PERSON>"], "groupName": "j<PERSON><PERSON>"}, {"packagePatterns": ["@types/mock-fs", "mock-fs"], "groupName": "mock-fs"}, {"packagePatterns": ["@types/lodash", "lodash"], "groupName": "lodash"}, {"packagePatterns": ["@types/nconf", "nconf"], "groupName": "nconf"}, {"packagePatterns": ["@types/puppeteer", "puppeteer"], "groupName": "puppeteer"}, {"packagePatterns": ["@types/rewire", "rewire"], "groupName": "rewire"}, {"packagePatterns": ["@types/sinon", "sinon", "sinon-chai"], "groupName": "sinon"}, {"packagePatterns": ["@types/uuid", "uuid"], "groupName": "uuid"}, {"packagePatterns": ["@types/word-wrap", "word-wrap"], "groupName": "word-wrap"}, {"packagePatterns": ["@types/yargs", "yargs"], "groupName": "yargs"}, {"packagePatterns": ["@types/mocha", "mocha"], "groupName": "mocha"}, {"packagePatterns": ["@types/chai", "chai", "chai-as-promised"], "groupName": "chai"}, {"matchPackagePatterns": ["*"], "matchUpdateTypes": ["minor", "patch"], "groupName": "all non-major dependencies", "groupSlug": "all-minor-patch"}, {"matchDepTypes": ["devDependencies"], "matchUpdateTypes": ["minor", "patch"], "groupName": "all non-major dev dependencies", "groupSlug": "dev-minor-patch"}, {"packagePatterns": ["node-html-parser"], "groupName": "node-html-parser"}]}