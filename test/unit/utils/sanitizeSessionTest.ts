import {expect} from 'chai';
import {sanitizeSession} from '../../../src/utils/sanitizeSession';

const invalidSession = {
    cookies: [
        {
            name: 'deletedCookie',
            value: 'deleted',
            domain: '.steampowered.com',
            path: '/',
            expires: 3081227340.379673
        },
        {
            name: 'cookieWithoutDomain',
            value: 'xxx',
            path: '/',
            expires: 3081227340.379673
        }
    ]
};

const validSession = {
    cookies: [
        {
            name: 'validCookie',
            value: 'xyz',
            domain: '.steampowered.com',
            path: '/',
            expires: 3081227340.379673
        }
    ]
};

describe('sanitizeSession', () => {
    it('should remove all cookies', async () => {
        const sanitizedSession = sanitizeSession(invalidSession);
        expect(sanitizedSession.cookies.length).to.be.eq(0);
    });

    it('should return the same session', async () => {
        const sanitizedSession = sanitizeSession(validSession);
        expect(sanitizedSession.cookies.length).to.be.eq(validSession.cookies.length);
    });
});
