import {log} from '../../utils/logger';

export const foundMultipleOrganizations = (organizationsNames: string[]): void => {
    if (organizationsNames.length > 1) {
        log.debug('Found multiple organizations', {amount: organizationsNames.length, names: organizationsNames});
    }
};

export const ignoringOrganization = (organizationName: string): void => {
    log.debug('Ignoring organization', {organization: organizationName});
};

export const startingDownloadForOrganization = (organizationName: string): void => {
    log.debug('Starting download process for organization', {organization: organizationName});
};

export const downloadForOrganizationFinished = (organizationName: string): void => {
    log.debug('Download process for organization finished successfully', {organization: organizationName});
};
