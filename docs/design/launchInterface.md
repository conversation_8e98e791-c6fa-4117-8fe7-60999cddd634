# Scraper Launch Interface (SLI)

## Design assumptions/constraints

-   In the new scraper integration flow, scrapers are launched as standalone binaries.
-   A single execution of the binary should result in generating a single report for a single studio, single portal and a single date range.
-   Data can be passed to the scraper using **command-line arguments** and **environment variables**. Env vars are preferred for sensitive data (e.g. credentials), since they don't leak in process metadata (for example, in task manager).
-   Command-line arguments and environment variables should be interchangeable, e.g. `--use-feature-x` should be equivalent to setting an env var `NDBI_USE_FEATURE_X=1`.
-   The scraper binary should make no assumptions about files and paths existing around it. All references to files should be passed in as command line arguments.
-   The scraper binary should not implicitly use a config file. Setting options using command-line arguments and env vars is preferred.
-   Scraper binary uses standard pipes to report progress back to the calling app:
    -   stdout for normal output or JSON-formatted status messages (depending on command-line arguments)
    -   stderr for critical errors not reportable through JSON.
-   Scraper binary should make a limited amount of API calls, but is not prohibited from it. It is usually preferred for the calling app to make a request in its name and pass the result as an argument or environment variable.
-   Scraper binary should return an exit code 0 on a success, and a non-zero value on any failure.

## Scraper commands

-   `login` Using given credentials, log in to the portal and create a session file at the specified path.
-   `check-session` Using the cookies from the session file, check against the portal whether the session is valid.
-   `get-source-side-organizations` Using cookies from the session file, scrape all source side organization names. If no organization is found (or multi orgs are not supported) then a default organization will be returned.
-   `scrape` Download a report using the given session file and a specified date range. Fail if not logged in, don't try to log in again.
-   `get-manual-login-details` Prints the manual login details for a given source or a null result if that source does not support manual login.

## Scraper parameters (for all commands)

-   `--source`: source to scrape from.
-   `--sessionFile`: Path to the session file to be used/generated.
-   `--outputSessionFile`: Path to the output session file. default: the same as `--sessionFile`
-   `--credentials`: a JSON-encoded dictionary of portal-specific credentials, used to create a session.
-   `--encrypt`: If set, encrypt the session file with the encryption token. default: `true`
-   `--encryptionToken`: A secret used to encrypt/decrypt the session file. Ideally, should be machine-dependent, like cookie encryption in Google Chrome.
-   `--output`: if `json`, log JSON dictionaries to stdout. If `text`, log normal text.
-   `--apiUrl`: URL used to query IndieBI Scraper API.
-   `--apiToken`: JWT token used to query IndieBI Scraper API.
-   `--headless`: If true, browser windows would not be shown. default: `false`
-   `--chromePath`: Path to the Chromium binary to use for Puppeteer.
-   `--featureFlags`: A list of comma-delimited feature keys that should be enabled.
-   `--dumpDir`: If set, create HTML/JSON dumps and screenshots in the given directory after a scraping error.
-   `--proxyUrl`: URL of a scraper proxy to use.

## Scraper parameters (scrape only)

-   `--from`, `--to`: A date range to scrape from ('YYYY-MM-DD').
-   `--reportPath`: Directory in which to put downloaded report files.
-   `--excludedSkus`: Ids of the products that should NOT be downloaded by the scraper (if supported by platform).
-   `--excludedOrgs`: Ids of organizations that should NOT be included in the reports.

## JSON log format

-   When `--output=json`, each log message is formatted as a JSON dictionary.
-   Each JSON message is separated by a newline. Generated JSON does not contain newlines.
-   Here are the keys to expect in the JSON log format:
    -   `msg`: contains the message text. Required.
    -   `progress`: contains current operation progress value, can contain the following values:
        -   an integer from 0 to 100 to denote specific progress from 0% to 100%
        -   a string `"i"`, denoting indeterminate progress (progress is being made, but we don't know how much work is left)
        -   `null`: job is stopped, no progress is being made.
        -   if this key is absent, progress is assumed to not change from the last status update.
    -   `transient`: if true, this message is trivial/transient in nature and should not be logged. Default: false.
-   only progress messages should be output to stdout. All other information (debug info, telemetry info) should be pushed to to stderr.

## Examples of usage in development :

Get source side organizations for Steam:

`node -r ts-node/register src/scrape.ts get-source-side-organizations --source=steam_sales --sessionFile="../../ms_session.txt" --encrypt=true --encryptionToken=AAC3NzaC1lZDI1NTE5AAAAIEEeNeCKusQdsRESYjf9enY27Spi7A9gFwX1yrMKk`

**Debug** Get source side organizations for Steam:
`node --inspect-brk -r ts-node/register src/scrape.ts get-source-side-organizations --source=steam_sales --sessionFile="../../ms_session.txt" --encrypt=true --encryptionToken=AAC3NzaC1lZDI1NTE5AAAAIEEeNeCKusQdsRESYjf9enY27Spi7A9gFwX1yrMKk --headless=false`
