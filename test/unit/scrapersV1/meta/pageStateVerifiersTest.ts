import {assert} from 'chai';
import * as td from 'testdouble';
import {<PERSON>rowser} from '../../../../src/browser/Browser';
import * as MetaConstants from '../../../../src/scrapersV1/httpDownloaders/meta/MetaConstants';
import {MetaReportDownloader} from '../../../../src/scrapersV1/httpDownloaders/meta/MetaReportDownloader';
import * as testData from './testData';

describe('Meta scraper - page state verifiers tests', () => {
    const {validLogin, validPassword, source} = testData;

    const fakeBrowser = td.object<Browser>();

    afterEach(td.reset);

    it('isOnLoginConfirmationPage should return true if user is on login confirmation page', async () => {
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.loginConfirmationFormXPathSelector)).thenResolve(true);
        const metaReportDownloader = new MetaReportDownloader({
            login: validLogin,
            password: validPassword,
            browser: fakeBrowser,
            source
        });

        assert.isTrue(await metaReportDownloader['isOnLoginConfirmationPage']());
    });

    it('isOnLoginConfirmationPage should return false if user is not on login confirmation page', async () => {
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.loginConfirmationFormXPathSelector)).thenResolve(false);
        const metaReportDownloader = new MetaReportDownloader({
            login: validLogin,
            password: validPassword,
            browser: fakeBrowser,
            source
        });

        assert.isFalse(await metaReportDownloader['isOnLoginConfirmationPage']());
    });

    it('hasLoginFailed should return true if login error appeared', async () => {
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.loginErrorXPathSelector)).thenResolve(true);
        const metaReportDownloader = new MetaReportDownloader({
            login: validLogin,
            password: validPassword,
            browser: fakeBrowser,
            source
        });

        assert.isTrue(await metaReportDownloader['hasLoginFailed']());
    });

    it('hasLoginFailed should return false if login was successful', async () => {
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.loginErrorXPathSelector)).thenResolve(false);
        const metaReportDownloader = new MetaReportDownloader({
            login: validLogin,
            password: validPassword,
            browser: fakeBrowser,
            source
        });

        assert.isFalse(await metaReportDownloader['hasLoginFailed']());
    });

    it('isInvalidCodeEntered should return true if invalid code error appeared', async () => {
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.invalidConfirmationCodeErrorXPathSelector)).thenResolve(true);
        const metaReportDownloader = new MetaReportDownloader({
            login: validLogin,
            password: validPassword,
            browser: fakeBrowser,
            source
        });

        assert.isTrue(await metaReportDownloader['isInvalidCodeEntered']());
    });

    it('isInvalidCodeEntered should return false if invalid code error did not appear', async () => {
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.invalidConfirmationCodeErrorXPathSelector)).thenResolve(false);
        const metaReportDownloader = new MetaReportDownloader({
            login: validLogin,
            password: validPassword,
            browser: fakeBrowser,
            source
        });

        assert.isFalse(await metaReportDownloader['isInvalidCodeEntered']());
    });
});
