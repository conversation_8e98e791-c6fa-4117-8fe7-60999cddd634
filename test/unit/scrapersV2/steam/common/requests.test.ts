import {AxiosHeaders, AxiosResponse} from 'axios';
import {expect} from 'chai';
import {afterEach} from 'mocha';
import * as td from 'testdouble';
import {LoginException, LoginExceptionType} from '../../../../../src/error/exceptions';
import {TemporaryPortalException} from '../../../../../src/error/exceptions/TemporaryPortalException';
import {BaseSteamRequestOptions, steamRequest} from '../../../../../src/scrapersV2/steam/common/requests';
import {steamPoweredUrl} from '../../../../../src/scrapersV2/steam/powered/steamPoweredRequest';
import {HTTPClient} from '../../../../../src/utils/http';
import * as sleepUtil from '../../../../../src/utils/sleep';
import {shouldThrowAsync} from '../../../../utils/shouldThrowAsync';

const baseUrl = steamPoweredUrl;
const defaultPath = '/path';
const httpClient = td.object<HTTPClient>();

const defaultParams: BaseSteamRequestOptions = {
    baseUrl,
    path: defaultPath,
    isStream: false,
    extraCookies: {cookie: 'cookie'},
    httpClient: httpClient,
    followRedirects: true,
    method: 'GET'
};

const properResponse: AxiosResponse = td.object({
    status: 200,
    statusText: 'OK',
    config: {
        url: `${baseUrl}${defaultPath}`,
        headers: td.object<AxiosHeaders>()
    },
    headers: {
        'content-type': 'text/html; charset=UTF-8'
    },
    data: '<html><body><a href="' + baseUrl + '/login/logout"</body></html>'
});

// If user gets logged out, Steam will return login page with goto param to redirect user to the requested page after login
const loginPageWithGotoPathResponse = td.object({
    ...properResponse,
    config: {
        url: `${baseUrl}/login/?goto=${defaultPath}`,
        headers: td.object<AxiosHeaders>()
    }
});

const emptyPageResponse = td.object({
    ...properResponse,
    data: undefined
});

const accessDeniedResponse = td.object({
    ...properResponse,
    status: 403,
    statusText: 'Forbidden'
});

const redirectResponse = td.object({
    status: 301,
    statusText: 'Moved Permanently',
    headers: {
        location: `${baseUrl}/login/?goto=${defaultPath}`
    }
});

const errorPageResponse = td.object({
    ...properResponse,
    data: `<html><body><div id="error_display">Error message</div></body></html>`
});

describe('steamRequest', () => {
    beforeEach(() => {
        // make the test run faster
        td.replace(sleepUtil, 'sleep', () => Promise.resolve());
    });

    afterEach(() => {
        td.reset();
    });

    it('should return response when Steam returns proper content', async () => {
        td.when(httpClient.get(td.matchers.anything())).thenResolve(properResponse);
        const result = await steamRequest(defaultParams);
        expect(result).to.equal(properResponse);
    });

    it('should throw SESSION_EXPIRED when user is redirected to login page with a goto param to requested path', async () => {
        td.when(httpClient.get(td.matchers.anything())).thenResolve(loginPageWithGotoPathResponse);
        const fun = () => steamRequest(defaultParams);
        await shouldThrowAsync(fun, new LoginException(LoginExceptionType.SESSION_EXPIRED));
    });

    it('should throw TemporaryPortalIssue when user receives empty page from Steam', async () => {
        td.when(httpClient.request(td.matchers.anything())).thenResolve(emptyPageResponse);
        const fun = () => steamRequest(defaultParams);
        await shouldThrowAsync(fun, new TemporaryPortalException('Steam returned empty response'));
    });

    it('should throw IP_BANNED when user receives "access denied" page from Steam', async () => {
        td.when(httpClient.request(td.matchers.anything())).thenResolve(accessDeniedResponse);
        const fun = () => steamRequest(defaultParams);
        await shouldThrowAsync(
            fun,
            new LoginException(
                LoginExceptionType.BANNED,
                'Your IP is temporarily blocked by Steam because of too many unsuccessful login attempts. Try again in a few minutes to an hour. If after this time the issue still occurs please contact IndieBI support.'
            )
        );
    });

    it('should make additional request to make sure Steam logged user out in case of 3xx status code', async () => {
        td.when(httpClient.get(td.matchers.anything())).thenResolve(redirectResponse, properResponse, properResponse);
        const result = await steamRequest(defaultParams);
        expect(result).to.equal(properResponse);
    });

    it('should throw TemporaryPortalException when user receives error page from Steam after redirection', async () => {
        td.when(httpClient.get(td.matchers.anything())).thenResolve(redirectResponse, errorPageResponse);
        const fun = () => steamRequest(defaultParams);
        await shouldThrowAsync(fun, new TemporaryPortalException(`Encountered Steam issue: Error message`));
    });
});
