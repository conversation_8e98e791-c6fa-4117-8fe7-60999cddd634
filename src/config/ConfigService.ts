import * as nconf from 'nconf';
import {version} from '../../package.json';
import {CLIArguments} from '../cli/arguments';
import {Source} from '../dataTypes';
import {FeatureFlag} from '../scrapersV2/Scraper';

export const defaults = {
    'puppeteer.headless': true
};
export const legacyConfigStore = new nconf.Provider();
legacyConfigStore.use('memory').defaults(defaults).load();

const getFeatureFlags = (): FeatureFlag[] => legacyConfigStore.get('app.features') ?? [];

export const getIndieBIKey = (): string => legacyConfigStore.get('indiebi.key');
export const downloadDirPath = (): string => legacyConfigStore.get('app.downloadPath');
export const getDumpDirPath = (): string | undefined => legacyConfigStore.get('app.dumpDir');
export const puppeteerHeadless = (): boolean => legacyConfigStore.get('puppeteer.headless');
export const puppeteerSlowMo: number = 50;
export const puppeteerDevtools: boolean = false;
export const chromiumExecutablePath = (): string => legacyConfigStore.get('puppeteer.chromePath');
export const requestTimeout: number = 60000;
export const activeWaitTimeout: number = 15000;

export const isFeatureFlagEnabled = (feature: FeatureFlag): boolean => getFeatureFlags().includes(feature);

export function getAppVersion(): string {
    return version;
}

//TODO this will be affected but is not used ATM ? This needs to be tested!
export function getIgnoredProducts(source: Source): string[] {
    const getResult = legacyConfigStore.get(`${source}.ignoredProductsArray`);
    return getResult ? getResult.map((product) => `${product}`) : [];
}

export function mapToLegacyConfig(args: CLIArguments): void {
    const properties = {
        'puppeteer.headless': args.headless,
        'puppeteer.chromePath': args.chromePath,
        'app.api.baseUrl': args.apiUrl,
        'indiebi.key': args.apiToken,
        'app.downloadPath': args.reportPath,
        'app.dumpDir': args.dumpDir,
        'app.features': args.featureFlags
    };

    for (const [key, value] of Object.entries(properties)) {
        legacyConfigStore.set(key, value);
    }
}
