export function isEveryPropertyFulfilled(obj: Record<string, string>): boolean {
    return Object.keys(obj).every((key: string) => typeof obj[key] !== 'undefined' && obj[key] !== null && obj[key].trim().length !== 0);
}

export function convertMapToParamsObj<T>(paramsMap: Map<string, string>): T {
    return [...paramsMap].reduce((obj, [key, value]) => ({...obj, [key]: value}), {} as T);
}

export function toCamelCase(obj: any): any {
    if (typeof obj !== 'object') {
        return obj;
    }

    if (Array.isArray(obj)) {
        return obj.map(toCamelCase);
    }

    const newObj: Record<string, any> = {};

    for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            const camelCaseKey = key.replace(/_([a-z])/g, function (g) {
                return g[1].toUpperCase();
            });
            newObj[camelCaseKey] = toCamelCase(obj[key]);
        }
    }

    return newObj;
}
