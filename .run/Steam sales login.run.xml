<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Steam sales login" type="NodeJSConfigurationType" application-parameters="login --source=steam_sales --sessionFile=&quot;.private/steam_sales_iil.json&quot; --headless=false --credentials=&quot;{\&quot;user\&quot;:\&quot;USER\&quot;,\&quot;password\&quot;:\&quot;PASS&amp;u{w\&quot;,\&quot;totpSecret\&quot;:\&quot;TOTP\&quot;}&quot; --sentryParams=&quot;&quot;{\&quot;enabled\&quot;:false,\&quot;dsn\&quot;:\&quot;asd\&quot;,\&quot;environment\&quot;:\&quot;kopytko\&quot;}&quot;&quot; --apiUrl=&quot;https://scraper-api.indiebi.dev&quot; --apiToken=&quot;TOKEN&quot;" path-to-js-file="dist/src/scrape.js" working-dir="$PROJECT_DIR$">
    <method v="2">
      <option name="TypeScript.Before.Run" enabled="true" FAIL_ON_ERROR="true" CONFIG_PATH="$PROJECT_DIR$/tsconfig.json" />
    </method>
  </configuration>
</component>