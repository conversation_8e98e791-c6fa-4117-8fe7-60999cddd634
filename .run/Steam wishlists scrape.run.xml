<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Steam wishlists scrape" type="NodeJSConfigurationType" application-parameters="scrape --source=steam_wishlists --sessionFile=&quot;.private/steam_sales_latest.json&quot; --from=&quot;2023-02-28&quot; --to=&quot;2023-03-03&quot; --reportPath=&quot;.private&quot; --encrypt=false --encryptionToken=&quot;AAC3NzaC1lZDI1NTE5AAAAIEEeNeCKusQdsRESYjf9enY27Spi7A9gFwX1yrMKk&quot; --headless=false --excludedSkus=&quot;[\&quot;asd\&quot;,\&quot;zxc\&quot;]&quot;" path-to-js-file="dist/src/scrape.js" working-dir="$PROJECT_DIR$">
    <method v="2">
      <option name="TypeScript.Before.Run" enabled="true" FAIL_ON_ERROR="true" CONFIG_PATH="$PROJECT_DIR$/tsconfig.json" />
    </method>
  </configuration>
</component>