<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Quest Sales Scrape " type="NodeJSConfigurationType" application-parameters="scrape --source=meta_quest_sales --sessionFile=&quot;.private/quest_sales.json&quot; --from=&quot;2024-01-01&quot; --to=&quot;2024-04-01&quot; --reportPath=&quot;.private&quot; --headless=false --credentials=&quot;{\&quot;user\&quot;:\&quot;asd\&quot;,\&quot;password\&quot;:\&quot;passwd\&quot;}&quot; --apiUrl=&quot;https://scraper-api.indiebi.dev&quot; --apiToken=&quot;token&quot;" path-to-js-file="dist/src/scrape.js" working-dir="$PROJECT_DIR$">
    <method v="2">
      <option name="TypeScript.Before.Run" enabled="true" FAIL_ON_ERROR="true" CONFIG_PATH="$PROJECT_DIR$/tsconfig.json" />
    </method>
  </configuration>
</component>