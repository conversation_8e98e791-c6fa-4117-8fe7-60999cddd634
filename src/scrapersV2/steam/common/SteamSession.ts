import {BrowserSession} from '../../../browserV2';
import {SourceSideOrganization} from '../../../dataTypes/SourceSideOrganization';

export interface SteamOrganization extends SourceSideOrganization {
    isLowPermission?: boolean;
}

export interface SteamSession extends BrowserSession {
    orgs?: SteamOrganization[]; //TODO this could be removed?
    orgsLastUpdated?: string; //TODO this could be removed?
    refreshToken?: string;
    steamGuardMachineToken?: string;
}
