import {expect} from 'chai';
import {CLIArguments, Command} from '../../src/cli/arguments';
import {SourceSideOrganization} from '../../src/dataTypes/SourceSideOrganization';
import {runCliMainWithConvertedArgs} from './utils/Utils';

export function createGetSourceSideOrgTest(loginArgs: CLIArguments, expectedOrgs: SourceSideOrganization[], loginAlreadyPerformed = false) {
    return it(`obtain all source side organizations for ${loginArgs.source}`, async function () {
        if (!loginAlreadyPerformed) {
            await runCliMainWithConvertedArgs(loginArgs);
        }
        const result = await runCliMainWithConvertedArgs({...loginArgs, command: Command.GET_SOURCE_SIDE_ORGANIZATIONS});
        expect(result).to.deep.equal(expectedOrgs);
    });
}
