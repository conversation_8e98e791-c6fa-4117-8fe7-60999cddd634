import * as chai from 'chai';
import * as chaiAsPromised from 'chai-as-promised';
import * as mockfs from 'mock-fs';
import * as moment from 'moment';
import * as td from 'testdouble';
import {ReportManifest} from '../../../src/scrapersV2/ReportManifest';

const expect = chai.expect;
chai.use(chaiAsPromised);
const dateTo = moment().startOf('day');
const dateFrom = moment(dateTo).add(-7, 'days');
let manifestClass: ReportManifest;

describe('ReportManifest', function () {
    beforeEach(() => {
        manifestClass = new ReportManifest<{url: string}>(dateFrom, dateTo);
    });

    afterEach(() => {
        td.reset();
        mockfs.restore();
    });

    it('generate manifest with empty metadata', async () => {
        const manifest = manifestClass.getManifest();
        expect(manifest.dateFrom).to.be.equal(dateFrom);
        expect(manifest.dateTo).to.be.equal(dateTo);
        expect(manifest.metadata).to.be.deep.equal({});
    });

    it('generate manifest with few files', async () => {
        const files = ['file1.csv', 'file2.csv', 'file3.csv'];
        for (const file of files) {
            manifestClass.addFile(file, {dateFrom, dateTo});
        }
        const manifest = manifestClass.getManifest();
        expect(manifest.dateFrom).to.be.equal(dateFrom);
        expect(manifest.dateTo).to.be.equal(dateTo);

        const fileNames = manifestClass.getFileNames();
        expect(fileNames).to.be.deep.equal(files);

        for (const file of files) {
            expect(manifest.metadata![file]).to.be.deep.equal({dateTo, dateFrom});
        }
    });

    it('generate manifest with version if provided', async () => {
        const version = '1.0.0';
        const manifest = new ReportManifest(dateFrom, dateTo, version).getManifest();
        expect(manifest.version).to.be.equal(version);
    });
});
