.update-lse-file: &update-lse-file
    stage: deploy
    needs: ['upload windows cli to azure', 'upload linux cli to azure', 'upload macos cli to azure']
    rules:
        - if: '$PIPELINE == "Release"'
          when: manual
    dependencies: []
    before_script:
        - RELEASE_FULL_VERSION=$(./scripts/get-version.sh)
        - RELEASE_SHORT_VERSION=$(echo $RELEASE_FULL_VERSION | sed "s/-.*//g")
        - (curl -fsL "$DEV_SCP_CONTAINER_URL/lse_version.json" || echo '{}' ) > dev_lse_version.json
        - echo -e "Old DEV lse_version.json:\n $(cat dev_lse_version.json)"
        - (curl -fsL "$PRODUCTION_SCP_CONTAINER_URL/lse_version.json" || echo '{}' ) > prod_lse_version.json
        - echo -e "Old PROD lse_version.json:\n $(cat prod_lse_version.json)"

.update-dev-lse-file: &update-dev-lse-file
    <<: *update-lse-file
    after_script:
        - jq . dev_lse_version.json > dev_lse_version_formated.json
        - echo -e "New DEV lse_version.json:\n $(cat dev_lse_version_formated.json)"
        - curl -f -X PUT --data-binary "@dev_lse_version_formated.json" -H "x-ms-blob-type" --header "x-ms-blob-type:BlockBlob" --header "Content-Type:application/json" "$DEV_SCP_CONTAINER_URL/lse_version.json?$SCP_CONTAINER_SAS_TOKEN"

.update-prod-lse-file: &update-prod-lse-file
    <<: *update-lse-file
    after_script:
        - jq . prod_lse_version.json > prod_lse_version_formated.json
        - echo -e "New PROD lse_version.json:\n $(cat prod_lse_version_formated.json)"
        - curl -f -X PUT --data-binary "@prod_lse_version_formated.json" -H "x-ms-blob-type" --header "x-ms-blob-type:BlockBlob" --header "Content-Type:application/json" "$PRODUCTION_SCP_CONTAINER_URL/lse_version.json?$SCP_CONTAINER_SAS_TOKEN"

############################################################################################################

update DEV cli.default scrapers & chromium versions:
    <<: *update-dev-lse-file
    script:
        - echo $(jq ".cli.default.chromium = $CHROMIUM_REVISION" dev_lse_version.json) > dev_lse_version.json
        - echo $(jq ".cli.default.scrapers = \"$RELEASE_FULL_VERSION\"" dev_lse_version.json) > dev_lse_version.json

update PROD cli.default scrapers & chromium versions:
    <<: *update-prod-lse-file
    needs: ['update DEV cli.default scrapers & chromium versions']
    script:
        - echo $(jq ".cli.default.chromium = $CHROMIUM_REVISION" prod_lse_version.json) > prod_lse_version.json
        - echo $(jq ".cli.default.scrapers = \"$RELEASE_FULL_VERSION\"" prod_lse_version.json) > prod_lse_version.json
