<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Steam discounts scrape" type="NodeJSConfigurationType" application-parameters="scrape --source=steam_discounts --sessionFile=&quot;.private/steam_sales_innersloth.json&quot; --from=&quot;2022-01-01&quot; --to=&quot;2022-01-02&quot; --reportPath=&quot;.private&quot; --headless=false --apiUrl=&quot;https://scraper-api.indiebi.dev&quot; " path-to-js-file="dist/src/scrape.js" working-dir="$PROJECT_DIR$">
    <method v="2">
      <option name="TypeScript.Before.Run" enabled="true" FAIL_ON_ERROR="true" CONFIG_PATH="$PROJECT_DIR$/tsconfig.json" />
    </method>
  </configuration>
</component>