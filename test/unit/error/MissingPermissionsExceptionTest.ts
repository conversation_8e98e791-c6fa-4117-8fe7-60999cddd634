import {expect} from 'chai';
import {MissingPermissionsException} from '../../../src/error/common';

describe('MissingPermissionsException should:', () => {
    it('always contain additionalErrorData with organizationsWithMissingPermissions', () => {
        const organizations = [
            {name: 'org1', id: '1'},
            {name: 'org2', id: '2'}
        ];
        const exception = new MissingPermissionsException(organizations);
        expect(exception.additionalErrorData?.organizationsWithMissingPermissions).to.be.deep.equal(organizations);
    });
});
