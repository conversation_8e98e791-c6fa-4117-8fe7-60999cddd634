import {expect} from 'chai';
import * as sinon from 'sinon';
import * as messaging from '../../../src/cli/messaging';
import {CustomException} from '../../../src/error/CustomException';
import {errorTypes} from '../../../src/error/errorTypes';
import globalErrorHandler from '../../../src/utils/globalErrorHandler';

const exceptionMessage = 'fizBaz';

describe('globalErrorHandler should:', () => {
    afterEach(() => {
        sinon.restore();
    });

    it('should properly handle custom exception', async () => {
        const errorSpy = sinon.spy(messaging, 'printError');
        sinon.stub(process, 'exit');
        const exception = new CustomException({message: exceptionMessage, errorType: errorTypes.CONFIGURATION_ISSUE});
        await globalErrorHandler(exception);
        expect(errorSpy.calledOnce).to.be.true;
        expect(errorSpy.firstCall.args[0]).to.be.equal(exception);
    });

    it('should properly handle unexpected Exception', async () => {
        sinon.stub(process, 'exit');
        const errorSpy = sinon.spy(messaging, 'printError');
        const error = new Error(exceptionMessage);
        await globalErrorHandler(error);
        expect(errorSpy.calledOnce).to.be.true;
        expect(errorSpy.firstCall.args).to.be.deep.equal([error]);
    });
});
