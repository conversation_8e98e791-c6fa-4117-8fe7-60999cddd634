import {getAuthCode} from '../../apiCommunication/authCodes';
import {Browser} from '../../browser/Browser';
import {BrowserPage, BrowserSession} from '../../browserV2';
import {printDualAuth, printDualAuthSuccess, printTraceWithError} from '../../cli/messaging';
import {Source} from '../../dataTypes';
import {AuthenticationException} from '../../error/common';
import {CustomException} from '../../error/CustomException';
import {errorTypes} from '../../error/errorTypes';
import {LoginException, LoginExceptionType} from '../../error/exceptions';
import {TooManyInvalid2FAException} from '../../error/exceptions/TooManyInvalid2FAException';
import {Action} from '../../scrapersV1/Action';
import {DownloadReportResult} from '../../scrapersV1/DownloadReportResult';
import {HttpReportDownloader} from '../../scrapersV1/HttpReportDownloader';
import {log} from '../../utils/logger';
import ManualLoginDetails from '../ManualLoginDetails';
import {CheckSessionFunction, GetSourceSideOrganizationsFunction, LoginFunction, ScrapeFunction, Scraper, ScraperParams} from '../Scraper';
import * as DownloaderFactory from './DownloaderFactory';
import {HttpCredentials} from './HttpCredentials';

export interface CookieScraperParams {
    browser: Browser;
    source: Source;
    credentials?: HttpCredentials;
}

export class ReportDownloaderAdapter implements Scraper<ScraperParams, BrowserSession> {
    reportDownloader: HttpReportDownloader;
    source: Source;
    public readonly manualLoginDetails?: ManualLoginDetails;

    constructor(source: Source) {
        this.source = source;
        this.manualLoginDetails = DownloaderFactory.getManualLoginDetails(this.source);
    }

    public getSourceSideOrganizations: GetSourceSideOrganizationsFunction<ScraperParams, BrowserSession> = async ({params, getPage}) => {
        const page = await getPage();
        return await this.getReportDownloader(page, params).getSourceSideOrganizations();
    };

    public login: LoginFunction<ScraperParams, BrowserSession> = async ({params, getPage}) => {
        const page = await getPage();
        const reportDownloader = this.getReportDownloader(page, params);
        const result = await reportDownloader.performLogin();

        switch (result.getCustomAction()) {
            case Action.CAPTCHA:
                throw new LoginException(LoginExceptionType.CAPTCHA_REQUIRED);
            case Action.BANNED_IP:
                throw new LoginException(LoginExceptionType.BANNED, result.toString());
            case Action.MANUAL_ACTION_REQUIRED:
                throw new LoginException(LoginExceptionType.MANUAL_ACTION_REQUIRED);
        }

        if (result.is2FactorAuth()) {
            await handle2FactorAuthentication(this.getCookieScraperParams(page, params), reportDownloader);
        } else if (result.isSessionExpired()) {
            throw new LoginException(LoginExceptionType.SESSION_EXPIRED);
        } else if (result.isPasswordResetError()) {
            throw new LoginException(LoginExceptionType.CREDENTIALS_EXPIRED);
        } else if (result.isFailedLogin()) {
            throw new LoginException(LoginExceptionType.CREDENTIALS_INVALID, result.toString());
        }

        return page.getSession();
    };

    public scrape: ScrapeFunction<ScraperParams> = async ({params, getPage}, startDate, endDate) => {
        const page = await getPage();
        const result = await this.getReportDownloader(page, params).downloadReport(startDate, endDate);
        if (result.isReport()) {
            return result.getReports()!;
        }
        this.handleNonReportResult(result);
        throw new Error(result.getErrorString()); //TODO Error ? Not customException?
    };

    public handleNonReportResult = (result: DownloadReportResult) => {
        if (result.isAuthenticationException()) {
            const authException = result.getAuthenticationException()!;
            const action = authException.getCustomAction();

            switch (action) {
                case Action.SESSION_EXPIRED:
                    throw new LoginException(LoginExceptionType.SESSION_EXPIRED);
                default:
                    throw new AuthenticationException(authException.getAuthenticationErrorString());
            }
        }
    };

    public checkSession: CheckSessionFunction<ScraperParams> = async ({getPage, params}) => {
        return await this.getReportDownloader(await getPage(), params).checkSession();
    };

    private getCookieScraperParams = (page: BrowserPage, {user, password, totpSecret}: ScraperParams): CookieScraperParams => ({
        credentials: {login: user, password, totpSecret},
        browser: new Browser(page.page.browser(), page.page),
        source: this.source
    });

    private getReportDownloader = (page: BrowserPage, params: ScraperParams): HttpReportDownloader => {
        if (this.reportDownloader) {
            return this.reportDownloader;
        }

        const browser = new Browser(page.page.browser(), page.page);
        const {user: login, password, totpSecret, ignoredProducts, ...rest} = params;
        const reportDownloader = DownloaderFactory.getDownloader({
            login,
            password,
            totpSecret,
            browser,
            source: this.source,
            ignoredProductsArray: ignoredProducts,
            ...rest
        }) as HttpReportDownloader;

        this.reportDownloader = reportDownloader;
        return reportDownloader;
    };
}

export async function handle2FactorAuthentication(params: CookieScraperParams, reportDownloader: HttpReportDownloader, attempt = 1): Promise<void> {
    const source = params.source;
    if (attempt > reportDownloader.dualAuthMaxAttempts) {
        throw new TooManyInvalid2FAException();
    }
    if (!params.credentials?.totpSecret) {
        printDualAuth({attempt, maxAttempts: reportDownloader.dualAuthMaxAttempts});
    }
    const code = await getAuthCode({source}, params.credentials?.totpSecret);

    try {
        log.info(`Code for ${source} received`);
        await reportDownloader.fillSecondAuthenticationFactor(code);
        log.info('Two-factor authentication success...');

        if (!params.credentials?.totpSecret) {
            printDualAuthSuccess({attempt});
        }
        return;
    } catch (error) {
        if (error instanceof CustomException) {
            return handle2FactorAuthentication(params, reportDownloader, attempt + 1);
        }
        const unexpected2FAAppeared = new CustomException({
            message: 'The login process encountered a 2FA authentication where it was not expected.',
            error,
            errorType: errorTypes.CONFIGURATION_ISSUE
        });
        printTraceWithError(unexpected2FAAppeared);
        throw unexpected2FAAppeared;
    }
}
