import * as chai from 'chai';
import * as chaiAsPromised from 'chai-as-promised';
import * as td from 'testdouble';
import {InsufficientPrivilegesLevelException} from '../../../../../src/error/exceptions';
import {downloadSteamGamesFile, steamGamesUrl} from '../../../../../src/scrapersV2/steam/games/steamGamesRequest';
import {HTTPClient} from '../../../../../src/utils/http';

chai.use(chaiAsPromised);
const {expect} = chai;

const UnauthorizedStemPageResponse = {
    data: '<div id="appName">Unauthorized</div>',
    headers: {'content-type': 'text/html; charset=UTF-8'}
};

const CsvResponse = {
    data: '"Page / Category","Page / Feature",Impressions,Visits',
    headers: {'content-type': 'text/csv'}
};

const downloadPath = `${steamGamesUrl}/some/steam/path`;
const appName = 'App1';

describe('Test Get Steam Visibility Apps', () => {
    afterEach(td.reset);

    it('should return correct CSV file', async () => {
        const httpClient = td.object<HTTPClient>();
        td.when(httpClient.request(td.matchers.anything())).thenResolve(CsvResponse);
        const response = await downloadSteamGamesFile(httpClient, downloadPath, appName);
        expect(response.data).to.be.equal(CsvResponse.data);
        expect(response.headers).to.be.equal(CsvResponse.headers);
    });

    it('should throw InsufficientPrivilegesLevelException for HTML response with Unauthorized page', async () => {
        const httpClient = td.object<HTTPClient>();
        td.when(httpClient.request(td.matchers.anything())).thenResolve(UnauthorizedStemPageResponse);

        try {
            await downloadSteamGamesFile(httpClient, downloadPath, appName);
            expect.fail('Expected exception not thrown');
        } catch (error) {
            expect(error).to.be.instanceOf(InsufficientPrivilegesLevelException);
            expect(error.message).to.include(appName);
        }
    });
});
