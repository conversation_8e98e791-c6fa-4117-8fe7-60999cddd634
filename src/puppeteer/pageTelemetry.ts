import {log} from '../utils/logger';
import {monotonicTimerMs} from '../utils/timer';
import {PuppeteerPage, Request} from '.';

export const RESPONSE_STATUS_FINISHED = 'Finished (memory cache)';

export function setupPageRequestsTelemetry(page: PuppeteerPage): void {
    const startTimes = new Map<unknown, number>();

    page.on('request', (request) => {
        startTimes.set(request, monotonicTimerMs());
    });

    const onRequestEnd = (request: Request): void => {
        const startTime = startTimes.get(request)!;
        startTimes.delete(request);
        const elapsedTime = monotonicTimerMs() - startTime;

        const responseStatus = request.response() ? request.response()!.status() : RESPONSE_STATUS_FINISHED;
        const resultCode = request.failure() ? request.failure()!.errorText : responseStatus;

        trackHttpDependency(request.url(), elapsedTime, resultCode, request.method(), 'Puppeteer HTTP');
    };

    page.on('requestfailed', onRequestEnd);
    page.on('requestfinished', onRequestEnd);
}

function trackHttpDependency(url: string, duration: number, resultCode: string | number, method: string, type = 'HTTP'): void {
    const path = new URL(url).pathname;
    // eslint-disable-next-line @typescript-eslint/no-magic-numbers
    const success = resultCode === RESPONSE_STATUS_FINISHED || (typeof resultCode === 'number' ? resultCode >= 200 && resultCode < 400 : false);
    const request = {
        dependencyTypeName: type,
        name: `${method} ${path}`,
        data: url,
        duration,
        resultCode,
        success
    };

    if (url.includes('cookie')) {
        log.debug(request.name, {message: 'Content redacted due to possibility of cookie request'});
    } else {
        log.debug(request.name, request);
    }
}
