<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Humble sales login" type="NodeJSConfigurationType" application-parameters="login --source=humble_sales --sessionFile=&quot;.private/humble_sales.json&quot; --encrypt=false --headless=false --credentials=&quot;{\&quot;user\&quot;:\&quot;asd\&quot;,\&quot;password\&quot;:\&quot;asd\&quot;}&quot; --sentryParams=&quot;&quot;{\&quot;enabled\&quot;:false,\&quot;dsn\&quot;:\&quot;asd\&quot;,\&quot;environment\&quot;:\&quot;kopytko\&quot;}&quot;&quot; --apiUrl=&quot;https://scraper-api.indiebi.dev&quot;" path-to-js-file="dist/src/scrape.js" working-dir="$PROJECT_DIR$">
    <method v="2">
      <option name="TypeScript.Before.Run" enabled="true" FAIL_ON_ERROR="true" CONFIG_PATH="$PROJECT_DIR$/tsconfig.json" />
    </method>
  </configuration>
</component>