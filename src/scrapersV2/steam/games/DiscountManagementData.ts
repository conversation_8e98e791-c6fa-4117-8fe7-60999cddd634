export interface DiscountInfo {
    amount: number;
    dateStr: string;
    description: string;
    endDate: number;
    endDateStr: string;
    group: string | boolean;
    id: number;
    name: string;
    percent: number;
    quantity: number;
    startDate: number;
    productId: number;
    publisherId: number;
    class: string;
}

interface Userinfo {
    loggedIn: boolean;
    steamid: string;
    accountid: number;
    accountName: string;
    isSupport: boolean;
    isLimited: boolean;
    isPartnerMember: boolean;
    countryCode: string;
    excludedContentDescriptors: {contentDescriptorid: number; timestampAdded: number}[];
}

interface DiscountEvent {
    name: string;
    startDate: number;
    endDate: number;
    description: string;
    collisionType: string;
    event: string;
    header: string;
    tooltip: string;
    type: string;
    preventWeeklong: string;
    appids: (null | number)[];
    id: string;
}

interface PackageDiscount {
    nDiscountID: number;
    packageID: number;
    nDiscountPct: number;
    strDiscountName: string;
    strDiscountDescription: string;
    rtStartDate: number;
    rtEndDate: number;
    discountEventID: string;
}

interface CurrencyPrices {
    [key: string]: number;
}

interface BasePrices {
    [key: string]: CurrencyPrices;
}

interface MaxDiscountPercentages {
    [key: string]: any;
}

interface PriceIncreaseTimes {
    [key: string]: number;
}

export interface DiscountManagementEntry {
    basePrices: BasePrices;
    discountEvents: DiscountEvent[] | Record<string, any>;
    maxDiscountPercentages: MaxDiscountPercentages;
    packageDiscounts: PackageDiscount[];
    packageIds: number[];
    priceIncreaseTimes: PriceIncreaseTimes;
    publisherid: number;
    userinfo: Userinfo;
}

export interface SteamAjaxEventsResponse {
    success: number; // 1 if the request was successful, error code otherwise
    msg?: string; // In case of failure it will contain the error message
    events?: DiscountEvent[];
}

export interface SteamAjaxDiscountsResponse {
    success: number; // 1 if the request was successful, error code otherwise
    msg?: string; // In case of failure it will contain the error message
    discounts?: PackageDiscount[];
}
