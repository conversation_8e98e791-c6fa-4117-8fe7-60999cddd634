import * as moment from 'moment';
import {DownloadReportResult} from './DownloadReportResult';
import {Report} from './Report';

export abstract class ReportDownloader {
    public abstract downloadReport(startDate: moment.Moment, endDate: moment.Moment): Promise<DownloadReportResult>;
    //TODO not actually used?
    public abstract completeReportDownload(startDate: moment.Moment, endDate: moment.Moment): Promise<Report[] | null>;
}
