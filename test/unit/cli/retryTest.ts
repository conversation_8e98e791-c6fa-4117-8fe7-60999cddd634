import * as chai from 'chai';
import * as chaiAsPromised from 'chai-as-promised';
import * as sinonChai from 'sinon-chai';
import * as td from 'testdouble';
import {CLIArguments, Command} from '../../../src/cli/arguments';
import {main} from '../../../src/cli/cli';
import {Source} from '../../../src/dataTypes';
import {CustomException} from '../../../src/error/CustomException';
import {ScraperProxy} from '../../../src/scrapersV2/ScraperProxy';
import * as sleepUtil from '../../../src/utils/sleep';

chai.use(chaiAsPromised).use(sinonChai);
const expect = chai.expect;

const args: CLIArguments = {
    command: Command.LOGIN,
    source: Source.PLAYSTATION_SALES,
    encrypt: false,
    credentials: {username: 'user', password: 'pass'},
    printTelemetry: false,
    headless: true,
    encryptionToken: 'fake',
    apiUrl: 'http://localhost:3000',
    apiToken: 'fake'
};

describe('Main application retry logic should ', () => {
    beforeEach(() => {
        td.replace(sleepUtil, 'sleep', async () => {});
    });

    afterEach(() => {
        td.reset();
    });

    it('retry the action on Error until the maximum number of attempts is reached', async () => {
        const error = new Error('Forcing login error');
        const loginMock = td.replace(ScraperProxy.prototype, 'login');
        td.when(loginMock()).thenReject(error);
        const fun = main(args, true);
        await expect(fun).to.eventually.be.rejectedWith(error);
        td.verify(loginMock(), {times: 4});
    });

    it('retry the action on CustomException until the maximum number of attempts is reached', async () => {
        const error = new CustomException({
            message: 'Forcing login error',
            errorType: 'TEMPORARY_PORTAL_ISSUE'
        });
        const loginMock = td.replace(ScraperProxy.prototype, 'login');
        td.when(loginMock()).thenReject(error);
        const fun = main(args, true);
        await expect(fun).to.eventually.be.rejectedWith(error);
        td.verify(loginMock(), {times: 4});
    });

    it('not retry on CustomException with canBeFixedByRetry property set to false', async () => {
        const error = new CustomException({
            message: 'Forcing login error',
            errorType: 'TEMPORARY_PORTAL_ISSUE',
            canBeFixedByRetry: false
        });
        const loginMock = td.replace(ScraperProxy.prototype, 'login');
        td.when(loginMock()).thenReject(error);
        const fun = main(args, false);
        await expect(fun).to.eventually.be.rejectedWith(error);
        td.verify(loginMock(), {times: 1});
    });
});
