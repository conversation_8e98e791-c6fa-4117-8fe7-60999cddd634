import * as _ from 'lodash';
import {EAuthSessionGuardType, EAuthTokenPlatformType, EResult, LoginSession} from 'steam-session';
import {getAuthCode} from '../../../apiCommunication/authCodes';
import {DualAuthMethod, printDualAuth, printDualAuthSuccess, printTraceWithError} from '../../../cli/messaging';
import {Portal, Source} from '../../../dataTypes';
import {CustomException} from '../../../error/CustomException';
import {errorTypes} from '../../../error/errorTypes';
import {LoginException, LoginExceptionType} from '../../../error/exceptions';
import {<PERSON>ie} from '../../../puppeteer';
import {HTTPClient, parseCookie} from '../../../utils/http';
import {log} from '../../../utils/logger';
import {ProgressCallback} from '../../ProgressCallback';
import {LoginFunction, ScraperParams} from '../../Scraper';
import {ScraperContext} from '../../ScraperContext';
import {SteamBackendError} from './SteamBackendError';
import {SteamSession} from './SteamSession';

export interface SteamParams extends ScraperParams {
    user: string;
    password: string;
    ignoredProducts?: string[];
    totpSecret?: string;
}

interface LoginContext {
    authMethod?: DualAuthMethod;
}

export const getLoginFunction = (source: Source, checkLoginFunction: (httpClient: HTTPClient) => Promise<boolean>): LoginFunction<SteamParams, SteamSession> => {
    return async function (context): Promise<SteamSession> {
        const {progress, getHttpClient, params} = context;

        return new Promise(async (resolve, reject) => {
            try {
                progress('Validating session');

                context.session = await refreshSessionCookies(context);

                const client = getHttpClient();
                if (await isLoggedIn(client, checkLoginFunction)) {
                    progress('Already logged in');
                    return resolve(context.session);
                }

                await loginToSteamWithCredentials(source, params, context, resolve, reject);
            } catch (error) {
                if (error.eresult) {
                    return reject(convertResultErrorToLoginException(error));
                }
                return reject(error);
            }
        });
    };
};

export async function refreshSessionCookies({session, progress}: ScraperContext<SteamParams, SteamSession>): Promise<SteamSession> {
    if (session.refreshToken) {
        try {
            progress('Refreshing session cookies');
            const loginSession = new LoginSession(EAuthTokenPlatformType.MobileApp);
            loginSession.refreshToken = session.refreshToken;
            const newCookies = await generateCookies(loginSession);
            session.cookies = mergeAndFilterOutOlderDuplicatedCookies(session.cookies || [], newCookies);
            progress('Session cookies refreshed');
        } catch (error) {
            printTraceWithError(error);
            progress(`Failed to refresh session cookies with error: ${error.message} (eresult code: ${error.eresult ?? 'not specified'})`);
        }
    } else {
        progress('No refresh token, skipping session cookies refresh');
    }

    return session;
}

function mergeAndFilterOutOlderDuplicatedCookies(existingCookies: Cookie[], newCookies: Cookie[]): Cookie[] {
    const merged = [...newCookies, ...existingCookies];
    return _.uniqBy(merged, (cookie: Cookie) => cookie.domain + cookie.name); // keeps first occurrence.
}

async function generateCookies(loginSession: LoginSession): Promise<Cookie[]> {
    const cookies = (await loginSession.getWebCookies()).map(parseCookie);
    const steamPoweredCookies = cookies.map((cookie) => ({...cookie, domain: 'partner.steampowered.com'}));
    const steamGamesCookies = cookies.map((cookie) => ({...cookie, domain: 'partner.steamgames.com'}));
    return [...steamPoweredCookies, ...steamGamesCookies];
}

async function isLoggedIn(http: HTTPClient, checkLoginFunction: (httpClient: HTTPClient) => Promise<boolean>): Promise<boolean> {
    try {
        return await checkLoginFunction(http);
    } catch (error) {
        if (error instanceof LoginException && error.type == LoginExceptionType.SESSION_EXPIRED) {
            return false;
        }
        throw error;
    }
}

export const maxSteamEmailCodeAttempts = 5;

async function handle2FAWithCode(
    source: Source,
    progress: ProgressCallback,
    reject: (...args: any[]) => void,
    loginSession: LoginSession,
    loginContext: LoginContext,
    totpSecret?: string | undefined
) {
    for (let attempt = 1; attempt <= maxSteamEmailCodeAttempts; attempt++) {
        if (!totpSecret) {
            printDualAuth({portal: Portal.STEAM, attempt, maxAttempts: maxSteamEmailCodeAttempts, authMethod: loginContext.authMethod});
            progress('Awaiting 2FA code from email or authenticator app');
        }
        const steamGuardCode = await getAuthCode({source}, totpSecret);
        progress('Waiting for Steam response');
        try {
            await loginSession.submitSteamGuardCode(steamGuardCode);
            if (!totpSecret) {
                printDualAuthSuccess({authMethod: loginContext.authMethod, attempt});
            }
            return; // in that case, promise will be resolved by 'authenticated' event handler
        } catch (e) {
            if (attempt < maxSteamEmailCodeAttempts) {
                continue;
            }
            if (totpSecret) {
                return reject(new LoginException(LoginExceptionType.MFA_INVALID, e.message));
            } else {
                return reject(new LoginException(LoginExceptionType.TOO_MANY_2FA_ATTEMPTS, e.message));
            }
        }
    }

    return reject(new CustomException({message: 'The login operation failed after 2FA attempts', errorType: errorTypes.TEMPORARY_PORTAL_ISSUE}));
}

async function loginToSteamWithCredentials(
    source: Source,
    params: SteamParams,
    {session, progress}: ScraperContext<SteamParams, SteamSession>,
    resolve: (session: SteamSession) => void,
    reject: (...args: any[]) => void
): Promise<void> {
    const loginContext: LoginContext = {};

    const loginSessionInput = {
        accountName: params.user,
        password: params.password,
        steamGuardMachineToken: session.steamGuardMachineToken
    };

    progress('Logging in to Steam with credentials');
    const loginSession = createLoginSession(progress, resolve, reject, loginContext);
    const startResult = await loginSession.startWithCredentials(loginSessionInput);

    if (!startResult.actionRequired || !startResult.validActions) {
        progress('No action required, authenticated with machine token');
        return; // in that case, promise will be resolved by 'authenticated' event handler
    }

    log.debug('Received login result', startResult);

    if (requiresDeviceConfirmation(startResult) && !params.totpSecret) {
        loginContext.authMethod = DualAuthMethod.MOBILE_APP_APPROVAL;
        progress('Waiting for device confirmation');
        printDualAuth({authMethod: DualAuthMethod.MOBILE_APP_APPROVAL, attempt: 1, maxAttempts: 1});
        return; // in that case, promise will be resolved by 'authenticated' event handler
    }

    if (requiresDeviceCode(startResult)) {
        loginContext.authMethod = DualAuthMethod.TOTP_CODE;
        return handle2FAWithCode(source, progress, reject, loginSession, loginContext, params.totpSecret);
    }

    if (requiresEmailCode(startResult)) {
        loginContext.authMethod = DualAuthMethod.EMAIL_CODE;
        return handle2FAWithCode(source, progress, reject, loginSession, loginContext);
    }

    return reject(new Error('Unsupported login flow, please login using a different method'));
}

type StartResult = ReturnType<LoginSession['startWithCredentials']> extends Promise<infer U> ? U : never;

function requiresDeviceConfirmation(startResult: StartResult): boolean {
    return startResult.validActions?.some((action) => action.type === EAuthSessionGuardType.DeviceConfirmation) ?? false;
}

function requiresEmailCode(startResult: StartResult): boolean {
    return startResult.validActions?.some((action) => action.type === EAuthSessionGuardType.EmailCode) ?? false;
}

function requiresDeviceCode(startResult: StartResult): boolean {
    return startResult.validActions?.some((action) => action.type === EAuthSessionGuardType.DeviceCode) ?? false;
}

function createLoginSession(
    progress: (message: string) => void,
    resolve: (session: SteamSession) => void,
    reject: (error: Error) => void,
    loginContext: LoginContext
): LoginSession {
    // TODO pass the proxy to the login session
    // https://github.com/DoctorMcKay/node-steam-session
    // We need to provide the Proxy to the Steam login, because the VCR
    // is not recording the network traffic and without this part
    // there are problems with VCR tests (we do not want to send real requests
    // to the Steam every time when test is running).
    const loginSession = new LoginSession(EAuthTokenPlatformType.MobileApp);
    loginSession.loginTimeout = 5 * 60 * 1000; // 5 minutes, applies to DeviceConfirmation and EmailCode
    setupLoginSessionEventHandlers(loginSession, loginContext, progress, resolve, reject);
    return loginSession;
}

function setupLoginSessionEventHandlers(
    loginSession: LoginSession,
    context: LoginContext,
    progress: (message: string) => void,
    resolve: (session: SteamSession) => void,
    reject: (error: Error | LoginException | SteamBackendError) => void
) {
    loginSession.on('authenticated', async () => {
        const {accountName, refreshToken, steamGuardMachineToken} = loginSession;

        if (context.authMethod === DualAuthMethod.MOBILE_APP_APPROVAL) {
            printDualAuthSuccess({authMethod: context.authMethod, attempt: 1});
        }
        progress(`Successfully logged in as ${accountName}`);
        const cookies = await generateCookies(loginSession);
        progress('Cookies retrieved');

        try {
            return resolve({cookies, refreshToken, steamGuardMachineToken});
        } catch (error) {
            reject(error);
        }
    });

    loginSession.on('timeout', () => {
        progress('This login attempt has timed out.');
        if (context.authMethod === DualAuthMethod.MOBILE_APP_APPROVAL) {
            reject(new LoginException(LoginExceptionType.TIMEOUT_2FA_MOBILE_APP_APPROVAL));
        } else {
            reject(new LoginException(LoginExceptionType.TIMEOUT_2FA));
        }
    });

    loginSession.on('error', (err: SteamBackendError) => {
        // This should ordinarily not happen. This only happens in case there's some kind of unexpected error while
        // polling, e.g. the network connection goes down or Steam chokes on something.
        progress(`ERROR: This login attempt has failed! ${err.message}`);
        reject(convertResultErrorToLoginException(err));
    });
}

function convertResultErrorToLoginException(error: SteamBackendError) {
    printTraceWithError(error);
    if ([EResult.RateLimitExceeded, EResult.AccountLoginDeniedThrottle].includes(error.eresult)) {
        return new LoginException(LoginExceptionType.RATE_LIMIT_EXCEEDED);
    }
    if ([EResult.AccessDenied, EResult.Banned].includes(error.eresult)) {
        return new LoginException(LoginExceptionType.BANNED);
    }
    if ([EResult.InvalidPassword, EResult.InvalidEmail].includes(error.eresult)) {
        return new LoginException(LoginExceptionType.CREDENTIALS_INVALID);
    }
    return new LoginException(LoginExceptionType.UNKNOWN, error.message, error);
}
