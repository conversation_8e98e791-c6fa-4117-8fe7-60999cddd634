{"calls": {"https://partner.steampowered.com/login": [{"body": {"data": "https_partner.steampowered.com/login", "encoding": "file"}, "headers": {}}, {"body": {"data": "https_partner.steampowered.com/login_aaf9091f94101d71ae4df31b0e3f22f4", "encoding": "file"}, "headers": {}}, {"body": {"data": "https_partner.steampowered.com/login_16ce693b827072bd67f17111c8a4f83d", "encoding": "file"}, "headers": {}}, {"body": {"data": "https_partner.steampowered.com/login_4c589ecad62ed3042043b3dc54e1d073", "encoding": "file"}, "headers": {}}]}, "defaults": {"request_headers": {"accept": "application/json, text/plain, */*", "cookie": "", "user-agent": "axios/0.27.2", "host": "partner.steampowered.com", "connection": "close"}, "headers": {"server": "nginx", "content-type": "text/html; charset=UTF-8", "content-security-policy": "default-src blob: data: https: 'unsafe-inline' 'unsafe-eval'; connect-src 'self' https://community.akamai.steamstatic.com/ https://store.steampowered.com/ wss://community.steam-api.com/websocket/ https://api.steampowered.com/ https://login.steampowered.com/ https://help.steampowered.com/ https://*.valvesoftware.com https://*.steambeta.net https://*.steamcontent.com; report-uri /actions/CSPReport", "content-encoding": "gzip", "vary": "Accept-Encoding", "connection": "close"}}}