import pytest

from scrapers_vcr.util.scraper_runner import ScraperRunConfig
from scrapers_vcr.util.testing import use_credentials, no_record


@pytest.fixture
def epic_scraper(default_scraper):
    default_scraper.source = "epic_sales"
    return default_scraper


def test_can_get_manual_login_details_epic(epic_scraper: ScraperRunConfig):
    result = epic_scraper.get_manual_login_details()
    result.expect_success()
    result_data = result.data
    assert result_data
    assert result_data["url"] == "https://dev.epicgames.com/portal"
    assert result_data["successSelector"] == {"value": "#dev-portal", "type": "css"}


@no_record
@use_credentials("epic_sales_superhot")
def test_can_login_epic(epic_scraper: ScraperRunConfig):
    a = 1
    result = epic_scraper.login()
    result.expect_success()
