import * as fs from 'fs';
import {AxiosError} from 'axios';
import {expect} from 'chai';
import * as httpStatusCodes from 'http-status-codes';
import * as puppeteer from 'puppeteer';
import * as sinon from 'sinon';
import * as td from 'testdouble';
import * as axios from '../../../src/apiCommunication/axios';
import {Browser} from '../../../src/browser/Browser';
import {ErrorPageValidator} from '../../../src/browser/ErrorPageValidator';
import {DownloadFailedDueToExternalServiceIssuesException, UnableToDownloadReportException} from '../../../src/error/exceptions';
import {UnableToLoadUrlException} from '../../../src/error/exceptions/UnableToLoadUrlException';
import {<PERSON><PERSON>, PuppeteerBrowser, PuppeteerPage} from '../../../src/puppeteer';
import * as fileUtils from '../../../src/utils/files/fileUtils';
import {sleep} from '../../../src/utils/sleep';
import {shouldThrowAsync} from '../../utils/shouldThrowAsync';

describe('Browser should:', () => {
    const errorDuringDownload = new Error('Error while downloading');
    const thirdPartyError = new Error('It is not our fault');
    const externalError: Partial<AxiosError> = {response: {data: {}, status: httpStatusCodes.INTERNAL_SERVER_ERROR, statusText: '', config: <any>{}, headers: {}}};
    const cookies = [];
    const fakeUrl = 'http://fake.url';
    const fakeResponseObject = {url: () => 'becadlo'};
    function createResponseObject(shouldThrowErrorDuringDownload: boolean): any {
        let errorHandler;
        let endHandler;

        return {
            headers: {
                'content-type': 'text/csv'
            },
            data: {
                pipe: (): void => {
                    if (shouldThrowErrorDuringDownload) {
                        errorHandler(errorDuringDownload);
                    } else {
                        endHandler();
                    }
                },
                on: (name: string, func: () => void): void => {
                    if (name === 'end') {
                        endHandler = func;
                    } else {
                        errorHandler = func;
                    }
                }
            }
        };
    }

    function prepareBrowserForJsonResponse(url: string, status: number, jsonString: string, timeout?: number): void {
        const page = td.object<PuppeteerPage>();
        td.replace(page, 'on', async (_text, func) => {
            if (timeout) {
                await sleep(timeout);
            }
            await func({
                url: (): string => url,
                async text(): Promise<string> {
                    return new Promise((resolve): void => resolve(jsonString));
                },
                status: (): number => status
            });
        });
        const browser = td.object<PuppeteerBrowser>();
        td.when(browser.newPage()).thenResolve(page);
        sinon.stub(puppeteer, 'launch').resolves(browser);
    }

    afterEach(() => {
        sinon.restore();
        td.reset();
    });

    it('properly download file', async () => {
        const get = td.replace(axios, 'get');
        const saveStreamToFile = td.replace(fileUtils, 'saveStreamToFile');
        const link = 'http://fake.co/notreal';
        const stream = td.object<fs.ReadStream>();
        td.when(get(td.matchers.anything(), td.matchers.anything())).thenResolve({
            data: stream,
            headers: {
                'content-type': 'text/csv'
            },
            status: httpStatusCodes.OK
        });
        const browser = new Browser(td.object<PuppeteerBrowser>(), td.object<PuppeteerPage>());
        td.replace(browser, 'cookies', async (): Promise<Cookie[]> => [td.object<Cookie>()]);
        await browser.downloadReportFile(link, 'file.txt');
        td.verify(saveStreamToFile(stream, 'file.txt'));
    });

    it('properly handle error while downloading file', async () => {
        const get = td.replace(axios, 'get');
        const link = 'http://fake.co/notreal';
        const error = new Error('Error while downloading');
        td.when(get(td.matchers.anything(), td.matchers.anything())).thenReject(new Error('Error while downloading'));
        const browser = new Browser(td.object<PuppeteerBrowser>(), td.object<PuppeteerPage>());
        td.replace(browser, 'cookies', async (): Promise<Cookie[]> => [td.object<Cookie>()]);
        await shouldThrowAsync(async () => browser.downloadReportFile(link, 'file.txt'), new UnableToDownloadReportException(error, link), true);
    });

    it('properly handle error while making request', async () => {
        const fakeResponse = createResponseObject(true);
        const pipeSpy = sinon.spy(fakeResponse.data, 'pipe');
        sinon.stub(axios, 'get').throws(thirdPartyError);
        sinon.stub(fs, 'createWriteStream').resolves(null);
        const browser = new Browser(td.object<PuppeteerBrowser>(), td.object<PuppeteerPage>());
        const fakeCookie = td.object<Cookie>();
        sinon.stub(browser, 'cookies').resolves([fakeCookie]);
        try {
            await browser.downloadReportFile('http://fake.co/notreal', 'file.txt');
        } catch (error) {
            expect(error).to.be.deep.equal(new UnableToDownloadReportException(error?.originalError, 'http://fake.co/notreal'));
        }

        expect(pipeSpy.called).to.be.false;
    });

    it('properly handle internal server error while making request', async () => {
        const fakeResponse = createResponseObject(true);
        const pipeSpy = sinon.spy(fakeResponse.data, 'pipe');
        sinon.stub(axios, 'get').throws(externalError);
        sinon.stub(fs, 'createWriteStream').resolves(null);
        const browser = new Browser(td.object<PuppeteerBrowser>(), td.object<PuppeteerPage>());
        const fakeCookie = td.object<Cookie>();
        sinon.stub(browser, 'cookies').resolves([fakeCookie]);
        try {
            await browser.downloadReportFile('http://fake.co/notreal', 'file.txt');
        } catch (error) {
            expect(error).to.be.deep.equal(
                new DownloadFailedDueToExternalServiceIssuesException({reportId: 'file.txt', status: httpStatusCodes.INTERNAL_SERVER_ERROR, data: {}})
            );
        }

        expect(pipeSpy.called).to.be.false;
    });

    it('should properly access page without error page validator', async () => {
        prepareBrowserForJsonResponse(fakeUrl, httpStatusCodes.NOT_FOUND, JSON.stringify(fakeResponseObject));
        const browser = await Browser.launchNewBrowser(cookies);
        const fakePage = td.object<PuppeteerPage>();
        td.when(fakePage.goto(td.matchers.anything(), td.matchers.anything())).thenResolve(fakeResponseObject);
        (browser as any).page = fakePage;

        const result = await browser.goto(fakeUrl);

        expect(result).to.be.deep.equal(fakeResponseObject);
    });

    it('should properly access page', async () => {
        prepareBrowserForJsonResponse(fakeUrl, httpStatusCodes.NOT_FOUND, JSON.stringify(fakeResponseObject));
        const browser = await Browser.launchNewBrowser(cookies);
        const fakePage = td.object<PuppeteerPage>();
        const fakeErrorPageValidator = td.function<ErrorPageValidator>();
        td.when(fakePage.goto(td.matchers.anything(), td.matchers.anything())).thenResolve(fakeResponseObject);
        td.when(fakeErrorPageValidator(td.matchers.anything())).thenResolve(false);
        (browser as any).page = fakePage;

        const result = await browser.goto(fakeUrl, fakeErrorPageValidator);

        expect(result).to.be.deep.equal(fakeResponseObject);
    });

    it('should retry after reaching error page', async () => {
        prepareBrowserForJsonResponse(fakeUrl, httpStatusCodes.NOT_FOUND, JSON.stringify(fakeResponseObject));
        const browser = await Browser.launchNewBrowser(cookies);
        const fakePage = td.object<PuppeteerPage>();
        const fakeErrorPageValidator = td.function<ErrorPageValidator>();
        td.when(fakePage.goto(td.matchers.anything(), td.matchers.anything())).thenResolve(fakeResponseObject);
        td.when(fakeErrorPageValidator(td.matchers.anything())).thenResolve(true, false);
        (browser as any).page = fakePage;

        const result = await browser.goto(fakeUrl, fakeErrorPageValidator);

        expect(result).to.be.deep.equal(fakeResponseObject);
    });

    it('should throw error after reaching max retries count', async () => {
        prepareBrowserForJsonResponse(fakeUrl, httpStatusCodes.NOT_FOUND, JSON.stringify(fakeResponseObject));
        const browser = await Browser.launchNewBrowser(cookies);
        const fakePage = td.object<PuppeteerPage>();
        const fakeErrorPageValidator = td.function<ErrorPageValidator>();
        td.when(fakePage.goto(td.matchers.anything(), td.matchers.anything())).thenResolve(fakeResponseObject);
        td.when(fakeErrorPageValidator(td.matchers.anything())).thenResolve(true, true, true, true, true);
        (browser as any).page = fakePage;
        const wrapperFn = async () => browser.goto(fakeUrl, fakeErrorPageValidator);

        await shouldThrowAsync(wrapperFn, new UnableToLoadUrlException(fakeUrl));
    });
});
