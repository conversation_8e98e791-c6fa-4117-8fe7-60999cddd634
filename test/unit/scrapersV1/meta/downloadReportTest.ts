import * as chai from 'chai';
import * as chaiAsPromised from 'chai-as-promised';
import {Moment} from 'moment';
import {<PERSON><PERSON><PERSON>and<PERSON>} from 'puppeteer';
import {restore, stub} from 'sinon';
import * as td from 'testdouble';
import {<PERSON>rowser} from '../../../../src/browser/Browser';
import * as configService from '../../../../src/config/ConfigService';
import {Source} from '../../../../src/dataTypes';
import {SourceSideOrganization, defaultSourceSideOrganization} from '../../../../src/dataTypes/SourceSideOrganization';
import {TooManyRequestsException} from '../../../../src/error/exceptions/TooManyRequestsException';
import * as MetaConstants from '../../../../src/scrapersV1/httpDownloaders/meta/MetaConstants';
import {GraphQLPersistedQuery} from '../../../../src/scrapersV1/httpDownloaders/meta/MetaConstants';
import {MetaReportDownloader} from '../../../../src/scrapersV1/httpDownloaders/meta/MetaReportDownloader';
import {Report} from '../../../../src/scrapersV1/Report';
import * as zip from '../../../../src/scrapersV2/zip';
import * as fileUtils from '../../../../src/utils/files/fileUtils';
import {populateFakeElementHandle} from '../../utils/mocking';
import * as testData from './testData';
import {gameId, gameName, graphqlRequestMatcher} from './testData';

chai.use(chaiAsPromised);
const {expect, assert} = chai;

describe('Meta scraper - downloadReport', () => {
    const {startDate, endDate, validLogin, validPassword, source} = testData;

    // let prepareZipReportsStub: SinonStub;

    beforeEach(() => {
        stub(fileUtils, 'zipFiles').resolves(undefined);
        stub(fileUtils, 'deleteFiles').resolves(undefined);
        td.replace(configService, 'downloadDirPath', () => '.private');
    });

    afterEach(() => {
        restore();
        td.reset();
    });

    it('should return report result if the user is already logged in', async () => {
        const fakeBrowser = td.object<Browser>();
        testData.prepareFakeCookiesAndMetaOrganizations(fakeBrowser);

        td.when(fakeBrowser.elementExists(MetaConstants.manageAppsListLinksSelector)).thenResolve(true);
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.userSettingsAvailableXPathSelector)).thenResolve(true);
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.accessibleAppsXPathSelector)).thenResolve(true);
        td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_APP_LIFETIME_REVENUE))).thenResolve({
            data: {app_store_item: {overview_analytics_lifetime_data: {lifetime_revenue: 123}}}
        });
        td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_APP_REAL_TIME))).thenResolve(
            testData.noSalesValidResponse
        );

        stub(zip, 'prepareZipReports').callsFake(
            async (
                startDate: Moment,
                endDate: Moment,
                fileNames: string[],
                source: Source,
                _progress: (message: string) => void,
                metadata?: Record<string, unknown>,
                noData?: boolean
            ): Promise<Report[]> => {
                expect(fileNames).to.be.deep.equal(['meta_rift_sales-2025-01-10_2025-01-11-sampleRiftGame.csv']);
                expect(noData).to.be.true;
                expect(metadata).to.be.deep.equal({
                    fileMetaData: {
                        'meta_rift_sales-2025-01-10_2025-01-11-sampleRiftGame.csv': {
                            skuId: gameId,
                            humanName: gameName,
                            platform: 'rift'
                        }
                    }
                });
                return [new Report(source, 'meta_rift_sales-2025-01-10_2025-01-11.zip', startDate, endDate, noData)];
            }
        );

        const metaReportDownloader = new MetaReportDownloader({
            login: validLogin,
            password: validPassword,
            browser: fakeBrowser,
            source: Source.META_RIFT_SALES
        });
        const downloadResult = await metaReportDownloader.downloadReport(startDate, endDate);
        td.verify(fakeBrowser.goto(MetaConstants.dashboardLoginUrl, td.matchers.anything()));
        assert.isTrue(downloadResult.isReport());
    });

    describe('should get meta organizations', () => {
        it('should return default organization', async () => {
            const fakeBrowser = td.object<Browser>();
            testData.prepareFakeCookiesAndMetaOrganizations(fakeBrowser, []);

            const metaReportDownloader = new MetaReportDownloader({
                login: validLogin,
                password: validPassword,
                browser: fakeBrowser,
                source: Source.META_RIFT_SALES
            });

            const orgs = await metaReportDownloader.getSourceSideOrganizations();
            expect(orgs).to.be.deep.equal([defaultSourceSideOrganization]);
        });

        it('should return all organizations', async () => {
            const fakeOrgs: SourceSideOrganization[] = [
                {name: 'Org1', id: '1'},
                {name: 'Org2', id: '2'}
            ];
            const fakeBrowser = td.object<Browser>();
            testData.prepareFakeCookiesAndMetaOrganizations(fakeBrowser, fakeOrgs);

            const metaReportDownloader = new MetaReportDownloader({
                login: validLogin,
                password: validPassword,
                browser: fakeBrowser,
                source: Source.META_RIFT_SALES
            });

            const orgs = await metaReportDownloader.getSourceSideOrganizations();
            expect(orgs).to.be.deep.equal(fakeOrgs);
        });
    });

    it('should throw login failure exception if invalid login credentials are entered', async () => {
        const fakeBrowser = td.object<Browser>();
        const invalidLogin = 'foo';
        const invalidPassword = 'foo';

        td.when(fakeBrowser.elementExists(MetaConstants.manageAppsListLinksSelector)).thenResolve(true);
        td.when(fakeBrowser.elementExists(MetaConstants.passwordSelector)).thenResolve(true);
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.userSettingsAvailableXPathSelector)).thenResolve(false);
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.loginErrorXPathSelector)).thenResolve(true);
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.loginWithEmailButtonXPathSelector)).thenResolve(true);

        const metaReportDownloader = new MetaReportDownloader({login: invalidLogin, password: invalidPassword, browser: fakeBrowser, source});
        const downloadResult = await metaReportDownloader.downloadReport(startDate, endDate);

        td.verify(fakeBrowser.goto(MetaConstants.dashboardLoginUrl, td.matchers.anything()));
        td.verify(fakeBrowser.type(MetaConstants.emailSelector, invalidLogin));
        td.verify(fakeBrowser.type(MetaConstants.passwordSelector, invalidPassword));
        assert.isTrue(downloadResult.isAuthenticationException());
    });

    it('should return a 2FA result if redirected to login confirmation page', async () => {
        const fakeBrowser = td.object<Browser>();

        td.when(fakeBrowser.elementExists(MetaConstants.manageAppsListLinksSelector)).thenResolve(true);
        td.when(fakeBrowser.elementExists(MetaConstants.passwordSelector)).thenResolve(true);
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.userSettingsAvailableXPathSelector)).thenResolve(false);
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.loginErrorXPathSelector)).thenResolve(false);
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.loginWithEmailButtonXPathSelector)).thenResolve(true);
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.loginConfirmationFormXPathSelector)).thenResolve(true);

        const metaReportDownloader = new MetaReportDownloader({login: validLogin, password: validPassword, browser: fakeBrowser, source});
        const downloadResult = await metaReportDownloader.downloadReport(startDate, endDate);

        td.verify(fakeBrowser.goto(MetaConstants.dashboardLoginUrl, td.matchers.anything()));
        td.verify(fakeBrowser.type(MetaConstants.emailSelector, validLogin));
        td.verify(fakeBrowser.type(MetaConstants.passwordSelector, validPassword));
        assert.isDefined(downloadResult.getAuthenticationException());
        assert.isTrue(downloadResult.getAuthenticationException()!.is2FactorAuth());
    });

    it('should save cookies and proceed to report downloading if login went fine and there was no need for 2FA', async () => {
        const fakeBrowser = td.object<Browser>();
        testData.prepareFakeCookiesAndMetaOrganizations(fakeBrowser);

        td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_APP_LIFETIME_REVENUE))).thenResolve({
            data: {app_store_item: {overview_analytics_lifetime_data: {lifetime_revenue: 123}}}
        });
        td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_APP_REAL_TIME))).thenResolve(
            testData.noSalesValidResponse
        );
        td.when(fakeBrowser.elementExists(MetaConstants.manageAppsListLinksSelector)).thenResolve(true);
        td.when(fakeBrowser.elementExists(MetaConstants.passwordSelector)).thenResolve(true);
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.accessibleAppsXPathSelector)).thenResolve(true);
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.loginErrorXPathSelector)).thenResolve(false);
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.loginConfirmationFormXPathSelector)).thenResolve(false);
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.userSettingsAvailableXPathSelector)).thenResolve(false, true);
        td.when(fakeBrowser.queryMultipleSelector(MetaConstants.manageAppsListLinksSelector)).thenResolve([
            populateFakeElementHandle(td.object<ElementHandle>(), {href: `/one/two/three/four/${gameId}/`})
        ]);
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.loginWithEmailButtonXPathSelector)).thenResolve(true);

        const metaReportDownloader = new MetaReportDownloader({
            login: validLogin,
            password: validPassword,
            browser: fakeBrowser,
            source: Source.META_RIFT_SALES
        });
        const downloadResult = await metaReportDownloader.downloadReport(startDate, endDate);

        td.verify(fakeBrowser.goto(MetaConstants.dashboardLoginUrl, td.matchers.anything()));
        td.verify(fakeBrowser.type(MetaConstants.emailSelector, validLogin));
        td.verify(fakeBrowser.type(MetaConstants.passwordSelector, validPassword));
        assert.isTrue(downloadResult.isReport());
    });

    const timeoutSafetyFactor = 2;
    const timeout = 6000 * timeoutSafetyFactor;

    it('should stop scraping and throw an exception if too many requests were made', async () => {
        const fakeBrowser = td.object<Browser>();
        testData.prepareFakeCookiesAndMetaOrganizations(fakeBrowser);
        td.when(fakeBrowser.elementExists(MetaConstants.manageAppsListLinksSelector)).thenResolve(false);
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.loginWithEmailButtonXPathSelector)).thenResolve(true);
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.userSettingsAvailableXPathSelector)).thenResolve(true);
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.accessibleAppsXPathSelector)).thenResolve(false);
        td.when(fakeBrowser.elementExists(MetaConstants.passwordSelector)).thenResolve(true);
        const metaReportDownloader = new MetaReportDownloader({login: validLogin, password: validPassword, browser: fakeBrowser, source});
        await expect(metaReportDownloader.downloadReport(startDate, endDate)).to.be.rejectedWith(TooManyRequestsException);
    }).timeout(timeout);

    it('should results with InsufficientPrivilegeLevelError in case user is not verified', async () => {
        const fakeBrowser = td.object<Browser>();

        testData.prepareFakeCookiesAndMetaOrganizations(fakeBrowser, undefined, false);

        td.when(fakeBrowser.elementExists(MetaConstants.passwordSelector)).thenResolve(true);

        const metaReportDownloader = new MetaReportDownloader({login: validLogin, password: validPassword, browser: fakeBrowser, source});
        const downloadResult = await metaReportDownloader.downloadReport(startDate, endDate);

        td.verify(fakeBrowser.goto(MetaConstants.dashboardLoginUrl, td.matchers.anything()));
        assert.isTrue(downloadResult.isAuthenticationException());
        assert.isTrue(downloadResult.getAuthenticationException()?.isInsufficientPrivilegeLevelError());
    });
});
