import Axios, {AxiosError} from 'axios';
import * as moment from 'moment';
import {Portal} from '../dataTypes';
import {CustomException} from '../error/CustomException';
import {ErrorType, errorTypes} from '../error/errorTypes';
import {stringifyAndFilterSensitiveFields} from '../telemetry/filterSensitiveData';
import {LogLevel} from '../utils/logger';

export type AllowedDataType = Record<string, unknown> | Array<unknown> | object | null | undefined;

type CommonEntryParams = {
    logLevel: LogLevel;
    timestamp: string;
    source: string;
    originId: string;
};

/**
 * Used to pass user visible output information.
 */
type OutputEntry = {
    type: 'output';
    message: string;
    progress?: number | 'i';
} & CommonEntryParams;

/**
 * Used to pass information related to a successful scraping process.
 */
type ResultEntry = {
    type: 'result';
    data: AllowedDataType;
} & CommonEntryParams;

/**
 * Used to pass information related to an error that occurred during a scraping process.
 * When used the scraping process will be treated as a failed process.
 */
type ErrorEntry = {
    type: 'error';
    errorType: ErrorType;
    data: AllowedDataType;
} & CommonEntryParams;

/**
 * Used to pass information related to a dual authentication process.
 */
type DualAuthEntry = {
    type: 'dualAuth';
    portal?: Portal;
    success?: boolean;
    attempt: number;
    maxAttempts?: number;
    authMethod?: DualAuthMethod;
} & CommonEntryParams;

export enum DualAuthMethod {
    /**
     * User receives a verification code via email
     */
    EMAIL_CODE = 'EMAIL_CODE',

    /**
     * User enters a time-based code from an authenticator app
     * (Google Authenticator, KeePass, Bitwarden, etc.)
     */
    TOTP_CODE = 'TOTP_CODE',

    /**
     * User approves the login attempt through a push notification
     * in their mobile app (i.e. Steam)
     */
    MOBILE_APP_APPROVAL = 'MOBILE_APP_APPROVAL'
}

/**
 * Used to pass trace (non-user visible) information related to the inner workings of a scraping process.
 */
type TraceEntry = {
    type: 'trace';
    message: string;
    data: AllowedDataType;
} & CommonEntryParams;

type Entry = OutputEntry | ResultEntry | ErrorEntry | DualAuthEntry | TraceEntry;
type EntryParams<T extends Entry> = Omit<T, 'type' | 'logLevel' | 'timestamp' | 'originId' | 'source'>;

interface BasicParams {
    source: string;
    originId: string;
}

const notSetup: BasicParams = {
    source: 'NOT_SPECIFIED',
    originId: 'NOT_SPECIFIED'
};
let _basicParams: BasicParams = notSetup;

/**
 * This function should be used in tests only!
 */
export function _resetBasicPrintingParams() {
    _basicParams = notSetup;
}

export function setBasicPrintingParams(basicParams: BasicParams): void {
    _basicParams = basicParams;
}

function printToStandardOutput(entry: Entry): void {
    console.log(stringifyAndFilterSensitiveFields({...entry, version: 2}));
}

export function printOutput(message: string, logLevel: LogLevel = LogLevel.INFO) {
    printToStandardOutput({message, logLevel, type: 'output', ..._basicParams, timestamp: moment().toISOString()});
}

export function printTrace(message: string, data?: AllowedDataType): void {
    printToStandardOutput({type: 'trace', message, data, logLevel: LogLevel.INFO, ..._basicParams, timestamp: moment().toISOString()});
}

export function printTraceWithError(error: Error | CustomException): void {
    const errorType = error instanceof CustomException ? error.errorType : errorTypes.UNEXPECTED_ERROR;
    const logLevel = error instanceof CustomException ? error.telemetryLogLevel : LogLevel.ERROR;

    printTrace(error.message, {
        errorType,
        logLevel,
        additionalErrorData: extractAdditionalErrorData(error)
    });
}

export const printResult = function (result: unknown): void {
    const data: AllowedDataType = result === undefined ? null : result;
    printToStandardOutput({data, type: 'result', logLevel: LogLevel.INFO, ..._basicParams, timestamp: moment().toISOString()});
};

export const printError = function (error: Error | CustomException): void {
    const message = error instanceof CustomException ? error.suggestedAction : `Unhandled exception occurred: ${error.message}`;
    const errorType = error instanceof CustomException ? error.errorType : errorTypes.UNEXPECTED_ERROR;
    const logLevel = error instanceof CustomException ? error.telemetryLogLevel : LogLevel.ERROR;

    printToStandardOutput({
        data: {
            message,
            additionalErrorData: extractAdditionalErrorData(error),
            stack: error instanceof CustomException ? error?.originalError?.stack || error.stack : error.stack
        },
        type: 'error',
        errorType,
        logLevel,
        ..._basicParams,
        timestamp: moment().toISOString()
    });
};

export const printDualAuth = function (entry: EntryParams<DualAuthEntry>): void {
    printToStandardOutput({
        ...entry,
        type: 'dualAuth',
        logLevel: LogLevel.INFO,
        ..._basicParams,
        timestamp: moment().toISOString()
    });
};

export const printDualAuthSuccess = function (entry: EntryParams<DualAuthEntry>): void {
    printDualAuth({...entry, success: true});
};

const extractAdditionalErrorData = (error: Error | CustomException | AxiosError): Record<string, unknown> | undefined => {
    if (error instanceof CustomException) {
        return error.additionalErrorData;
    }

    if (Axios.isAxiosError(error)) {
        return {
            request: {
                method: error.request?.method,
                protocol: error.request?.protocol,
                host: error.request?.host,
                url: error.request?.url
            },
            response: {
                status: error.response?.status,
                statusText: error.response?.statusText,
                data: error.response?.data
            }
        };
    }

    return undefined;
};
