import {chunk} from 'lodash';
import * as moment from 'moment';
import {getAuthCode} from '../../../apiCommunication/authCodes';
import {Browser} from '../../../browser/Browser';
import {Source} from '../../../dataTypes';
import {SourceIdentifier, SourceSideOrganization, defaultSourceSideOrganization} from '../../../dataTypes/SourceSideOrganization';
import {CustomException} from '../../../error/CustomException';
import {errorTypes} from '../../../error/errorTypes';
import {InsufficientPrivilegesLevelException, InvalidContentTypeException, LoginException, LoginExceptionType} from '../../../error/exceptions';
import {Invalid2FACode} from '../../../error/exceptions/Invalid2FACode';
import ManualLoginDetails from '../../../scrapersV2/ManualLoginDetails';
import {IReportManifest} from '../../../scrapersV2/ReportManifest';
import {DynamicFlagConfig, processFeatureFlags} from '../../../scrapersV2/Scraper';
import {packReportsToZip} from '../../../scrapersV2/zip';
import {isManualSession} from '../../../utils/conditions';
import {FileExtension} from '../../../utils/files/FileExtension';
import {convertNintendoJSONOrCSVToCSV, generateFileName} from '../../../utils/files/fileUtils';
import {log} from '../../../utils/logger';
import {retryAction} from '../../../utils/retryAction';
import {DownloadReportResult} from '../../DownloadReportResult';
import {HttpReportDownloader} from '../../HttpReportDownloader';
import {LoginResult} from '../../LoginResult';
import {PrivilegesChecker} from '../../PrivilegesChecker';
import {Report} from '../../Report';
import {ReportDownloaderProperties} from '../../ReportDownloaderProperties';
import {validateDownloader} from '../validateDownloader';
import * as nintendoConstants from './NintendoConstants';

const DEFAULT_PRODUCT_IDS_PER_REPORT = 20;
const DEFAULT_MAX_CSV_DOWNLOAD_ATTEMPTS = 20;

export class NintendoReportDownloader implements HttpReportDownloader, PrivilegesChecker {
    private readonly login: string;
    private readonly password: string;
    private readonly totpSecret?: string;
    private readonly browser: Browser;

    private max_concurrent_downloads: number = 5;
    private product_ids_per_report: number = DEFAULT_PRODUCT_IDS_PER_REPORT;
    private max_csv_download_attempts: number = DEFAULT_MAX_CSV_DOWNLOAD_ATTEMPTS;

    private readonly dynamicFlagConfigs: DynamicFlagConfig<string, number | boolean>[] = [
        {
            prefix: 'nintendo-sales-product-ids-per-report-',
            setter: (value: number) => (this.product_ids_per_report = value),
            parser: (value: string) => parseInt(value, 10)
        },
        {
            prefix: 'nintendo-sales-max-csv-download-attempts-',
            setter: (value: number) => (this.max_csv_download_attempts = value),
            parser: (value: string) => parseInt(value, 10)
        }
    ];

    constructor({login, password, totpSecret, browser, featureFlags}: ReportDownloaderProperties) {
        validateDownloader(Source.NINTENDO_SALES, {browser});
        this.login = login!;
        this.password = password!;
        this.browser = browser!;
        this.totpSecret = totpSecret;

        if (featureFlags) {
            processFeatureFlags(featureFlags, this.dynamicFlagConfigs);
        }
    }

    public static manualLoginDetails: ManualLoginDetails = {
        url: nintendoConstants.reportWebsiteUrl,
        successSelector: {value: nintendoConstants.loggedInXpathSelector, type: 'xpath'}
    };

    private static async prepareZipReport(startDate: moment.Moment, endDate: moment.Moment, manifest: IReportManifest, reportFileNames: string[]): Promise<Report[]> {
        const zipFilename = generateFileName(Source.NINTENDO_SALES, startDate, endDate, FileExtension.ZIP);
        await packReportsToZip(reportFileNames, zipFilename, manifest);
        return [new Report(Source.NINTENDO_SALES, zipFilename, startDate, endDate)];
    }

    public async downloadReport(startDate: moment.Moment, endDate: moment.Moment): Promise<DownloadReportResult> {
        const loginResult = await this.performLogin();
        if (loginResult.isFailedLogin()) {
            return DownloadReportResult.createLoginErrorResult(loginResult);
        }
        log.info('Logged in...');
        return DownloadReportResult.createReportResult(await this.completeReportDownload(startDate, endDate));
    }

    public async fillSecondAuthenticationFactor(code: string): Promise<void> {
        if (!(await this.browser.elementExists(nintendoConstants.mfaCodeInputSelector))) {
            log.info('2FA input page not loaded properly. Redirecting to login page...');
            return;
        }
        if (code.length != 6) {
            throw new Invalid2FACode();
        }

        log.info('Filling two factor authentication...');

        log.debug('Typing in the verification code');
        await this.browser.type(nintendoConstants.mfaCodeInputSelector, code);
        log.debug('Clicking submit');
        await this.browser.click(nintendoConstants.submitButton);

        // Sometimes the page is just empty after 2FA submission, so lets reload it to be sure
        log.debug("Reloading the page to make sure we didn't get stuck");
        await this.browser.goto(nintendoConstants.reportWebsiteUrl, this.isOnEmptyPage);

        if (!(await this.isLoggedIn())) {
            throw new Invalid2FACode();
        }
    }

    public get dualAuthMaxAttempts(): number {
        return nintendoConstants.dualAuthMaxAttempts;
    }

    public async completeReportDownload(startDate: moment.Moment, endDate: moment.Moment): Promise<Report[]> {
        const filenames = await this.downloadReportsWithDirectLinks(startDate, endDate);
        return this.createFinalReport(startDate, endDate, filenames);
    }

    private async getProductIds(): Promise<string[]> {
        return retryAction({
            target: async () => {
                await this.browser.goto(nintendoConstants.reportWebsiteUrl, this.isOnEmptyPage);
                await this.browser.goto(nintendoConstants.authorizationRenewalUrl, this.isOnEmptyPage);
                const productIds = await this.browser.get<string[] | string>(nintendoConstants.productIdsApiRequestUrl);

                if (!Array.isArray(productIds)) {
                    log.info('Invalid response from the server.');
                    throw new LoginException(LoginExceptionType.SESSION_EXPIRED, 'Session expired.');
                }

                if (productIds.length === 0) {
                    throw new InsufficientPrivilegesLevelException();
                }

                for (const productId of productIds) {
                    if (!nintendoConstants.productCodeRegex.test(productId)) {
                        log.info(`Invalid product ID: ${productId}`);
                        throw new LoginException(LoginExceptionType.SESSION_EXPIRED, 'Session expired.');
                    }
                }

                log.info(`We found ${productIds.length} product IDs.`);
                return productIds;
            },
            maxAttempts: 5,
            delay: 5000,
            retryCondition: (error: Error): boolean => !(error instanceof InsufficientPrivilegesLevelException)
        });
    }

    private createGroupsOfMultipleProductIdsForEveryReport(productIds: string[]): string[][] {
        return chunk(productIds, this.product_ids_per_report);
    }

    private createGroupsForConcurrentDownloads(reportGroups: string[][]): string[][][] {
        return chunk(reportGroups, this.max_concurrent_downloads);
    }

    private async downloadAllReportsByDownloadingFromLinks(startDate: moment.Moment, endDate: moment.Moment): Promise<string[]> {
        const allProductIds = await this.getProductIds();
        const groupsOfReportIDs = this.createGroupsOfMultipleProductIdsForEveryReport(allProductIds);
        const concurrentDownloadGroups = this.createGroupsForConcurrentDownloads(groupsOfReportIDs);

        const downloadedFilenames: string[] = [];

        for (const [groupIndex, downloadGroup] of concurrentDownloadGroups.entries()) {
            const groupResults = await Promise.all(
                downloadGroup.map((groupOfReportIDs, index) =>
                    this.downloadSingleReportWithMultipleProductIds(startDate, endDate, groupOfReportIDs, groupIndex * this.max_concurrent_downloads + index + 1)
                )
            );

            downloadedFilenames.push(...groupResults);
            this.logDownloadProgress(downloadedFilenames.length, allProductIds.length);
        }

        return downloadedFilenames;
    }

    private async downloadSingleReportWithMultipleProductIds(
        startDate: moment.Moment,
        endDate: moment.Moment,
        productIds: string[],
        reportIndex: number
    ): Promise<string> {
        const fileName = generateFileName(Source.NINTENDO_SALES, startDate, endDate, FileExtension.CSV, reportIndex);
        const downloadUrl = NintendoReportDownloader.generateNintendoReportUrl(startDate, endDate, productIds);

        log.info(`Downloading report ${reportIndex} with ${productIds.length} product IDs...`);

        try {
            return await retryAction({
                target: async () => {
                    await this.browser.downloadReportFile(downloadUrl, fileName, ['application/octet-stream;charset=UTF-16', 'application/json;charset=UTF-8']);
                    if (!(await convertNintendoJSONOrCSVToCSV(fileName, {delimiter: ',', skipEmptyLines: true}))) {
                        throw new Error(`Downloaded file ${fileName} is not a valid CSV`);
                    }
                    return fileName;
                },
                maxAttempts: this.max_csv_download_attempts,
                delay: 10000
            });
        } catch (error) {
            if (error instanceof InvalidContentTypeException) {
                throw new LoginException(LoginExceptionType.SESSION_EXPIRED, 'Session expired.', error);
            }
            throw error;
        }
    }

    private async downloadReportsWithDirectLinks(startDate: moment.Moment, endDate: moment.Moment): Promise<string[]> {
        log.info('Downloading reports using direct link');
        // visit page to refresh session
        await this.browser.goto(nintendoConstants.reportWebsiteUrl, this.isOnEmptyPage);
        await this.browser.goto(nintendoConstants.authorizationRenewalUrl, this.isOnEmptyPage);

        return this.downloadAllReportsByDownloadingFromLinks(startDate, endDate);
    }

    private async createFinalReport(startDate: moment.Moment, endDate: moment.Moment, filenames: string[]): Promise<Report[]> {
        log.info(`All downloads completed. Total files: ${filenames.length}`);
        log.info('Preparing manifest file');
        const manifest: IReportManifest = {dateFrom: startDate, dateTo: endDate};

        log.info('Preparing report zip');
        return NintendoReportDownloader.prepareZipReport(startDate, endDate, manifest, filenames);
    }

    private logDownloadProgress(downloadedCount: number, totalProductIds: number): void {
        const totalReports = Math.ceil(totalProductIds / this.product_ids_per_report);
        const progress = this.formatProgress(Math.min(downloadedCount, totalReports), totalReports);
        log.info(`Downloaded ${progress} of reports (${Math.min(downloadedCount, totalReports)} files so far)`);
    }

    private formatProgress(current: number, total: number): string {
        const progress = Math.round((current / total) * 100);
        return `${progress}%`;
    }

    public async isLoggedIn(): Promise<boolean> {
        log.info('Checking if user is required to log in...');
        const [selector] = await this.browser.waitForAnySelector({...nintendoConstants.loginStateSelectors});
        const isUserNameVisible = selector === nintendoConstants.loggedInXpathSelector;
        if (!isUserNameVisible) {
            return false;
        }
        try {
            await this.getProductIds();
        } catch (error) {
            if (!(error instanceof LoginException)) {
                throw error;
            }
            log.debug('User is half-logged-in. Clicking logout...');
            try {
                await this.browser.clickXpath(nintendoConstants.logOutButton);
            } catch (error) {
                log.debug(`Failed to logout user. Reason: ${error.message}`);
            }
            return false;
        }

        return true;
    }

    private async isOnEmptyPage(browser: Browser): Promise<boolean> {
        // Sometimes the page served by Nintendo is just empty
        const body = await browser.getElementText('body');
        return body === '';
    }

    public async performLogin(): Promise<LoginResult> {
        log.info('Opening login page...');
        await this.browser.goto(nintendoConstants.reportWebsiteUrl, this.isOnEmptyPage);
        if (await this.isLoggedIn()) {
            return LoginResult.createSuccessfulLoginResult();
        } else if (isManualSession(this.login, this.password)) {
            return LoginResult.createExpiredSessionResult();
        }

        await this.redirectToLoginFormIfNecessary();

        log.info('Checking if language change is required...');

        const [selector] = await this.browser.waitForAnySelector(nintendoConstants.languageStateSelectors);
        if (selector === nintendoConstants.isEnglishLanguageSelector) {
            log.info('Changing language...');
            const navigationPromise = this.browser.waitForNavigation();
            await this.browser.click(nintendoConstants.isEnglishLanguageSelector);
            await navigationPromise;
            log.debug('Checking if language change succeeded.');
            if (!(await this.browser.elementExists(nintendoConstants.isJapaneseLanguageSelector))) {
                return LoginResult.createAuthenticationExceptionResult("Couldn't change language.");
            }
        }

        if (this.login === undefined || this.password == undefined) {
            throw new CustomException({
                message: 'Missing credentials.',
                suggestedAction: 'Please log in using valid credentials or the manual option.',
                errorType: errorTypes.CONFIGURATION_ISSUE
            });
        }

        log.info('Typing in the username');
        await this.browser.type(nintendoConstants.loginInputSelector, this.login);
        log.info('Typing in the password');
        await this.browser.type(nintendoConstants.passwordInputSelector, this.password);
        log.info('Logging in...');
        await this.browser.click(nintendoConstants.submitButton);

        log.info('Checking if properly logged in...');
        return this.checkIfLoginWasSuccessful();
    }

    private async redirectToLoginFormIfNecessary() {
        log.info('Checking if we need to redirect to the login form...');
        const [selector] = await this.browser.waitForAnySelector(nintendoConstants.redirectStateSelectors);
        if (selector == nintendoConstants.signInButtonSelector) {
            await this.browser.click(nintendoConstants.signInButtonSelector);
        }
    }

    public async validatePrivileges(): Promise<void> {
        await this.getProductIds();
    }

    public async checkSession(): Promise<SourceIdentifier> {
        log.info('Opening dashboard page...');
        await this.browser.goto(nintendoConstants.reportWebsiteUrl, this.isOnEmptyPage);
        await this.browser.goto(nintendoConstants.authorizationRenewalUrl, this.isOnEmptyPage);
        if (!(await this.isLoggedIn())) {
            throw new LoginException(LoginExceptionType.SESSION_EXPIRED);
        }

        const user = (await this.browser.getElementText('.nav.pull-right>li>span.navbar-text')).replace('User: ', '');

        return {
            id: user
        };
    }

    private async handle2FA(): Promise<LoginResult> {
        if (!this.totpSecret) {
            return LoginResult.create2FactorAuthResult();
        }
        const code = await getAuthCode({source: Source.NINTENDO_SALES}, this.totpSecret);
        await this.fillSecondAuthenticationFactor(code);
        return this.checkIfLoginWasSuccessful();
    }

    private async checkIfLoginWasSuccessful(): Promise<LoginResult> {
        const [selector] = await this.browser.waitForAnySelector(nintendoConstants.afterLoginAttemptStateSelectors);

        switch (selector) {
            case nintendoConstants.signInButtonSelector:
                await this.redirectToLoginFormIfNecessary();
                return this.checkIfLoginWasSuccessful();
            case nintendoConstants.invalidCredentialsSelector:
                return LoginResult.createAuthenticationExceptionResult('Wrong credentials, please enter correct username and/or password.');
            case nintendoConstants.noLoginPermissionsSelector:
                return LoginResult.createInsufficientPrivilegeLevelErrorResult(
                    'Your Nintendo account does not have login permission. Check the User Guide on how to set it up correctly.'
                );
            case nintendoConstants.mfaCodeInputSelector:
                log.info('Verification Code is required');
                return this.handle2FA();
            case nintendoConstants.mfaSkipVerification:
                log.info('MFA configuration detected, skipping verification.');
                await this.browser.click(nintendoConstants.mfaSkipVerification);
                return this.checkIfLoginWasSuccessful();
            case nintendoConstants.loggedInXpathSelector:
                return LoginResult.createSuccessfulLoginResult();
            default:
                break;
        }

        return LoginResult.createAuthenticationExceptionResult('Login failed for unknown reason.');
    }

    /**
     * Multi orgs not supported by this source.
     * We could extract the account name and call it the organization name but that would be very confusing for both us and the end users.
     */
    public async getSourceSideOrganizations(): Promise<SourceSideOrganization[]> {
        return [defaultSourceSideOrganization];
    }

    private static generateNintendoReportUrl(startDate: moment.Moment, endDate: moment.Moment, productIds: string[]): string {
        const baseUrl = 'https://sst.mng.nintendo.net/shoptools/switchLicenseeReports/titleReport/search';

        const params = new URLSearchParams({
            period: 'DAILY',
            beginYear: startDate.format('YYYY'),
            beginMonth: startDate.format('MM'),
            endYear: endDate.format('YYYY'),
            endMonth: endDate.format('MM'),
            begin: startDate.format('YYYY/MM/DD'),
            end: endDate.format('YYYY/MM/DD'),
            paid: '',
            searchUnit: '0',
            searchPrice: 'true',
            detail: 'none',
            searchTitle: '',
            downloadCsv: 'downloadCsv',
            searchTitles: '',
            searchCodes: productIds.join('\r\n')
        });

        const multiValueParams = {
            regions: ['JPN', 'USA', 'EUR', 'AUS', 'KOR', 'CHN', 'TWN', 'Other'],
            types: ['TITLE', 'TRIAL', 'AOC', 'SERVICE_TICKET', 'BUNDLE'],
            codes: ['P', 'M', 'V', 'Y', 'H', 'X'],
            devices: ['HAC', 'BEE'],
            additionals: ['NSUID']
        };

        Object.entries(multiValueParams).forEach(([key, values]) => {
            values.forEach((value) => params.append(key, value));
        });

        const url = `${baseUrl}?${params.toString()}`;

        if (url.length > 2048) {
            // Chromium url limit is 2MB, however 2048 is considered a safe limit, which should be supported by all browsers and servers
            log.warning('URL exceeds 2048 characters, it may not work properly.');
        }

        log.debug(`Generated URL: ${url}`);
        return url;
    }
}
