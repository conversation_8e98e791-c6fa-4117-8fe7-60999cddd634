import * as chai from 'chai';
import * as chaiAsPromised from 'chai-as-promised';
import * as mockfs from 'mock-fs';
import * as moment from 'moment';
import * as td from 'testdouble';
import {TestDouble} from 'testdouble';
import {BrowserPage, BrowserSession, BrowserV2} from '../../../src/browserV2';
import {Source} from '../../../src/dataTypes';
import {SourceIdentifier} from '../../../src/dataTypes/SourceSideOrganization';
import {epicSalesScraper} from '../../../src/scrapersV2/epic/epic';
import {ReportDownloaderAdapter} from '../../../src/scrapersV2/reportDownloadAdapter/ReportDownloaderAdapter';
import {ScraperProxy} from '../../../src/scrapersV2/ScraperProxy';
import {v2Scrapers} from '../../../src/scrapersV2/V2ScrapersList';
import {SessionFile} from '../../../src/utils/files/JsonFile';

const expect = chai.expect;
chai.use(chaiAsPromised);

describe('ScraperProxy', function () {
    const silentProgress = () => {
        // empty progress function for silent tests
    };

    const sourceIdentifier = {id: 'userX'};
    const params = {user: 'x', password: 'x'};
    const paramsMap = new Map(Object.entries(params));
    const sessionFileName = 'session.json';

    let page: BrowserPage;
    let sessionFile: SessionFile;
    const startDate = moment();
    const endDate = moment().add(7, 'days');

    const session: BrowserSession = {
        cookies: [
            {
                name: 'baz',
                value: 'bar',
                domain: 'foo',
                path: '/',
                expires: 100,
                size: 100,
                httpOnly: false,
                session: false,
                secure: false,
                sameSite: 'Strict',
                priority: 'Medium',
                sourceScheme: 'Unset',
                sourcePort: 0,
                sameParty: true
            }
        ]
    };

    beforeEach(() => {
        sessionFile = new SessionFile(sessionFileName);
        td.replace(epicSalesScraper, 'checkSession', (): SourceIdentifier => sourceIdentifier);
        const launch: TestDouble<any> = td.replace(BrowserV2, 'launch');
        const browserV2: BrowserV2 = td.object<BrowserV2>();
        page = td.object<BrowserPage>();
        (browserV2 as any).page = page;
        td.when(page.getSession()).thenResolve({});
        td.when(launch()).thenResolve(browserV2);
        mockfs({
            [sessionFileName]: JSON.stringify(session)
        });
    });

    afterEach(() => {
        td.reset();
        mockfs.restore();
    });

    async function createTestProxy(): Promise<ScraperProxy> {
        return ScraperProxy.create({source: Source.EPIC_SALES, progress: silentProgress, paramsMap, sessionFile});
    }

    it('calls login with correct parameters', async () => {
        td.replace(sessionFile, 'save', td.func());
        const epicLogin = td.replace(epicSalesScraper, 'login');
        const proxy = await createTestProxy();
        const result = await proxy.login();
        td.verify(epicLogin(td.matchers.contains({params})));
        expect(result).to.be.equal(sourceIdentifier);
    });

    it('calls scrape with correct parameters', async () => {
        const epicScrape = td.replace(epicSalesScraper, 'scrape');

        const proxy = await createTestProxy();
        await proxy.scrape(startDate, endDate);

        td.verify(epicScrape(td.matchers.contains({params}), startDate, endDate));
    });

    Object.values(Source).forEach((source) => {
        const expectV2 = [...v2Scrapers.keys()].includes(source);

        it(`maps source ${source} to ${expectV2 ? 'scraper v2' : 'reportdownloader'}`, async () => {
            const proxy = await ScraperProxy.create({source: source, progress: silentProgress, paramsMap, sessionFile});
            if (expectV2) {
                expect(proxy.scraper).not.instanceOf(ReportDownloaderAdapter);
            } else {
                expect(proxy.scraper).instanceOf(ReportDownloaderAdapter);
            }
        });
    });

    // TODO JENGA logically this is correct but this should be fixed
    it('throws error if source is incorrect', async () => {
        const invalidSource = 'anything';
        await expect(
            ScraperProxy.create({
                source: invalidSource,
                progress: silentProgress,
                paramsMap,
                sessionFile
            })
        ).to.eventually.rejectedWith(`Operation requiring manual login is not supported for ${invalidSource}`);
    });

    it("doesn't initialize the browser with a session file, for V2 Scrapers", async () => {
        await createTestProxy();
        expect(td.explain(page.setSession).callCount).to.be.equal(0);
    });

    it('saves session to file after a successful login', async () => {
        td.replace(sessionFile, 'save', td.func());
        td.replace(epicSalesScraper, 'login', () => session);
        const proxy = await createTestProxy();
        const result = await proxy.login();
        td.verify(sessionFile.save(session));
        expect(result).to.be.equal(sourceIdentifier);
    });

    it('calls emergency actions if scraping fails', async () => {
        const epicScrape: TestDouble<any> = td.replace(epicSalesScraper, 'scrape');
        td.when(epicScrape(), {ignoreExtraArgs: true}).thenReject(new Error('foo'));
        const proxy = await createTestProxy();
        const context = td.replace(proxy, 'context');
        const emergencyClose = td.replace(context, 'emergencyClose');

        await expect(proxy.scrape(startDate, endDate)).to.be.rejectedWith('foo');

        td.verify(emergencyClose());
    });

    it('calls emergency actions if logging in fails', async () => {
        const epicLogin: TestDouble<any> = td.replace(epicSalesScraper, 'login');
        td.when(epicLogin(), {ignoreExtraArgs: true}).thenReject(new Error('foo'));
        const proxy = await createTestProxy();
        const context = td.replace(proxy, 'context');
        const emergencyClose = td.replace(context, 'emergencyClose');

        await expect(proxy.login()).to.be.rejectedWith('foo');

        td.verify(emergencyClose());
    });

    it('close context when proxy is closed', async () => {
        const proxy = await createTestProxy();
        const context = td.replace(proxy, 'context');
        const contextClose = td.replace(context, 'close');
        await proxy.close();

        td.verify(contextClose());
    });

    it('should fail login when it tries to save an empty session.', async () => {
        td.replace(epicSalesScraper, 'login', () => '\0');
        const proxy = await createTestProxy();
        await expect(proxy.login()).to.eventually.rejectedWith('Cannot save session file');
    });

    it('should NOT fail scrape when the operation updates the session to an empty value', async () => {
        td.replace(epicSalesScraper, 'scrape', (context, _startDate, _endDate) => {
            context.session = '\0';
            return [];
        });
        const proxy = await createTestProxy();
        const result = await proxy.scrape(moment(), moment());
        expect(result).to.be.deep.equal([]);
    });
});
