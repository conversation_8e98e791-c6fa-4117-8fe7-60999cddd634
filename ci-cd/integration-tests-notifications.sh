#!/bin/bash

if [[ $# -lt 2 ]] ; then
    echo 'Please provide mattermost webhook url and failed scrapers list'
    exit 1
fi

FILE_WITH_NOTIFICATION_CONTENT="message_to_webhook.json"
FAILURE_LIST=$(echo "$2" | awk '{printf " * %s\\n", $0}')

MESSAGE='
{"text": "
## Javascript Scrapers Integration tests failed\n\n

Pipeline: [#'${CI_PIPELINE_ID}']('${CI_PIPELINE_URL}')\n\n

## Failures:\n
'$FAILURE_LIST'"}'

echo "$MESSAGE" > ${FILE_WITH_NOTIFICATION_CONTENT}

curl -g -k -i -X POST -H "Content-Type:application/json" --url "$1" -d @${FILE_WITH_NOTIFICATION_CONTENT}
