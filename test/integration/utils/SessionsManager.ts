import {existsSync, mkdirSync, readFileSync, writeFileSync} from 'fs';
import path = require('path');
import {FileCrypter} from './FileCrypter';

export const DEFAULT_ENCRYPTED_SESSIONS_PATH = './test/integration/encrypted_sessions.blob';
export const DEFAULT_TEST_SESSIONS_PATH = '.private/integration_tests_sessions';

export function unpackSessionsToFiles(): void {
    const content = readFileSync(DEFAULT_ENCRYPTED_SESSIONS_PATH).toString();
    const decryptedContent = new FileCrypter().decrypt(content);
    const decrypted = JSON.parse(decryptedContent);

    if (!existsSync(DEFAULT_TEST_SESSIONS_PATH)) {
        mkdirSync(DEFAULT_TEST_SESSIONS_PATH, {recursive: true});
    }

    for (const [key, value] of Object.entries(decrypted)) {
        writeFileSync(path.join(DEFAULT_TEST_SESSIONS_PATH, key + '.json'), JSON.stringify(value));
    }
}

export const sessionsManager = {
    get(fileName: string) {
        return path.join(DEFAULT_TEST_SESSIONS_PATH, fileName);
    }
};
