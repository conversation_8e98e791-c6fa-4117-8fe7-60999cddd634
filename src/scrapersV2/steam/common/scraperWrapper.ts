import {BrowserSession} from '../../../browserV2';
import {Scraper} from '../../Scraper';
import {SteamParams, refreshSessionCookies} from './login';

// This wrapper is created to maintain most of generic logic for all steam sources (like refreshing cookies)
export const steamScraperWrapper = ({
    login,
    scrape,
    getSourceSideOrganizations,
    checkSession,
    manualLoginDetails
}: Scraper<SteamParams, BrowserSession>): Scraper<SteamParams, BrowserSession> => {
    return {
        login,
        scrape: async (context, startDate, endDate) => {
            context.session = await refreshSessionCookies(context);
            return scrape(context, startDate, endDate);
        },
        getSourceSideOrganizations: async (context) => {
            context.session = await refreshSessionCookies(context);
            return getSourceSideOrganizations(context);
        },
        checkSession: async (context) => {
            context.session = await refreshSessionCookies(context);
            return checkSession(context);
        },
        manualLoginDetails
    };
};
