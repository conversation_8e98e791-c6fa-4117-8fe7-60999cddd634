import {expect} from 'chai';
import {PromiseState, waitSettled} from '../../../src/utils/promises';
import {runInFakeTime} from '../../utils/fakeTime';

const asyncFunctionTest = async (shouldThrow: boolean, value?: any, errorMessage?: string) =>
    new Promise((resolve, reject) =>
        setTimeout(() => {
            if (shouldThrow) {
                reject(errorMessage);
            }
            resolve(value);
        }, 50)
    );

describe('waitSettled', () => {
    it('should resolve a promise', async () => {
        const value = 'asd';
        const output = await runInFakeTime(async () => waitSettled(asyncFunctionTest(false, value)));
        expect(output).to.haveOwnProperty('status');
        expect(output).to.haveOwnProperty('value');
        expect(output.status).to.be.equal(PromiseState.FULFILLED);
        expect(output.value).to.be.equal(value);
    });

    it('should reject a promise', async () => {
        const reason = 'Example error message';
        const output = await runInFakeTime(async () => waitSettled(asyncFunctionTest(true, null, reason)));
        expect(output).to.haveOwnProperty('status');
        expect(output).to.haveOwnProperty('reason');
        expect(output.status).to.be.equal(PromiseState.REJECTED);
        expect(output.reason).to.be.equal(reason);
    });
});
