import {Source} from '../dataTypes';
import {epicSalesScraper} from './epic/epic';
import {playStationSalesScraper} from './playStation/scrapers/playStationSales';
import {playStationWishlistActionsScraper} from './playStation/scrapers/playStationWishlistActions';
import {Scraper} from './Scraper';
import {steamDiscountsScraper} from './steam/games/scrapers/steamDiscounts';
import {steamImpressionsScraper} from './steam/games/scrapers/steamImpressions';
import {steamSalesScraper} from './steam/powered/scrapers/steamSales';
import {steamWishlistBalanceScraper} from './steam/powered/scrapers/steamWishlistBalance';
import {steamWishlistsScraper} from './steam/powered/scrapers/steamWishlists';

export const v2Scrapers = new Map<Source | string, Scraper>([
    [Source.EPIC_SALES, epicSalesScraper],
    [Source.PLAYSTATION_SALES, playStationSalesScraper],
    [Source.PLAYSTATION_WISHLIST_ACTIONS, playStationWishlistActionsScraper],
    [Source.STEAM_SALES, steamSalesScraper],
    [Source.STEAM_WISHLISTS, steamWishlistsScraper],
    [Source.STEAM_IMPRESSIONS, steamImpressionsScraper],
    [Source.STEAM_DISCOUNTS, steamDiscountsScraper],
    [Source.STEAM_WISHLIST_BALANCE, steamWishlistBalanceScraper]
]);
