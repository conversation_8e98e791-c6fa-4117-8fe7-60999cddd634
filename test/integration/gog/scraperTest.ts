import * as moment from 'moment';
import {Source} from '../../../src/dataTypes';
import {FileExtension} from '../../../src/utils/files/FileExtension';
import {createCheckSessionTest} from '../checkSessionTestGenerator';
import {createScrapeTest} from '../scrapeTestGenerator';
import {LOGIN_ARGS, getScrapeArgs} from '../utils/ArgsProvider';
import {removeIfFilesExist, runCliMainWithConvertedArgs} from '../utils/Utils';

describe('GOG runner:', () => {
    const defaultStartDateString = '2021-02-28';
    const defaultEndDateString = '2021-03-03';
    const recentStartDate = moment.utc().subtract(4, 'days').format('YYYY-MM-DD');
    const recentEndDate = moment.utc().subtract(2, 'days').format('YYYY-MM-DD');

    const loginArgs = LOGIN_ARGS.for_gog;
    const scrapeArgs = getScrapeArgs(loginArgs, defaultStartDateString, defaultEndDateString, FileExtension.ZIP);
    const recentScrapeArgs = getScrapeArgs(loginArgs, recentStartDate, recentEndDate, FileExtension.ZIP);

    const produceFileNames = (startDateString, endDateString) => {
        // last part of the filename is product name so we can hardcode it until we don't change the account or it doesn't get new products
        return [
            // csv files
            `${Source.GOG_SALES}-${startDateString}_${endDateString}-SUPERHOT.csv`,
            `${Source.GOG_SALES}-${startDateString}_${endDateString}-SUPERHOT.json`,
            `${Source.GOG_SALES}-${startDateString}_${endDateString}-SUPERHOT-MIND-CONTROL-DELETE.csv`,
            `${Source.GOG_SALES}-${startDateString}_${endDateString}-SUPERHOT-MIND-CONTROL-DELETE.json`,
            `${Source.GOG_SALES}-${startDateString}_${endDateString}-SUPERHOT-ONE-OF-US-BUNDLE.csv`,
            `${Source.GOG_SALES}-${startDateString}_${endDateString}-SUPERHOT-ONE-OF-US-BUNDLE.json`
        ];
    };

    before(async () => {
        await removeIfFilesExist(scrapeArgs.testedFilePath!, recentScrapeArgs.testedFilePath!);
        await runCliMainWithConvertedArgs(loginArgs);
    });

    createCheckSessionTest(loginArgs, {id: '<EMAIL>'}, true);

    createScrapeTest('should download GOG zipped data and check number of files', scrapeArgs, produceFileNames(defaultStartDateString, defaultEndDateString));

    createScrapeTest('should download GOG file with recent data', recentScrapeArgs, produceFileNames(recentStartDate, recentEndDate));
});
