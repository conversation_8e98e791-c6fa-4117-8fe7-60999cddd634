import * as chai from 'chai';
import * as chaiAsPromised from 'chai-as-promised';
import * as moment from 'moment';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, J<PERSON><PERSON><PERSON><PERSON>} from 'puppeteer';
import * as td from 'testdouble';
import {Browser} from '../../../../src/browser/Browser';
import {defaultSourceSideOrganization} from '../../../../src/dataTypes/SourceSideOrganization';
import {InsufficientPrivilegesLevelException} from '../../../../src/error/exceptions';
import {Invalid2FACode} from '../../../../src/error/exceptions/Invalid2FACode';
import {DownloadReportResult} from '../../../../src/scrapersV1/DownloadReportResult';
import * as humbleElementConstants from '../../../../src/scrapersV1/httpDownloaders/humbleBundle/HumbleBundleConstants';
import {HumbleBundleReportDownloader} from '../../../../src/scrapersV1/httpDownloaders/humbleBundle/HumbleBundleReportDownloader';
import {LoginResult} from '../../../../src/scrapersV1/LoginResult';
import * as fileUtils from '../../../../src/utils/files/fileUtils';
import {shouldThrowAsync} from '../../../utils/shouldThrowAsync';
import {mockHttpResponse} from '../../utils/mocking';

chai.use(chaiAsPromised);
const {expect} = chai;

describe('HumbleBundleReportDownloader should:', function (): void {
    const login = 'login';
    const password = 'password';
    const startDate = moment('2019-06-06').utc();
    const endDate = moment().utc();
    const secondAuthenticationFactor = 'false2faCode';
    const pageTitleSelector = '.page_title';
    const fakeHrefs = [
        '',
        'https://www.humblebundle.com/accounting/store/sales_reportsundefined',
        'https://www.humblebundle.com/accounting/store/sales_reports/export/job_vDGtWj3FEa7Ve0nSL2Xx1kWsgqlf?date=05/17/2017%20-%2010/01/2018&date_range_report=1',
        'https://www.humblebundle.com/accounting/store/sales_reports/export/job_nyNKZxuP7UvWUtgVhpXczwGm5kDz?date=05/17/2017%20-%2010/01/2018&date_range_report=1'
    ];

    beforeEach(() => {
        const checkFileExists = td.replace(fileUtils, 'checkFileExists');
        td.when(checkFileExists(td.matchers.isA(String), td.matchers.anything())).thenResolve(true);
    });

    afterEach(() => {
        td.reset();
    });

    function prepareBrowserForDownload(): Browser {
        const fakeBrowser = td.object<Browser>();
        const fakeHeaderElement = td.object<ElementHandle>();
        const fakeLinkElements: ElementHandle[] = [];
        for (const fakeHref of fakeHrefs) {
            const fakeLinkElement = td.object<ElementHandle>();
            const fakePropertyElement = td.object<JSHandle>();
            td.when(fakePropertyElement.jsonValue()).thenResolve(fakeHref);
            td.when(fakeLinkElement.getProperty('href')).thenResolve(fakePropertyElement);
            fakeLinkElements.push(fakeLinkElement);
        }
        td.when(fakeHeaderElement.$$('a')).thenResolve(fakeLinkElements as any);
        td.when(fakeBrowser.querySelector(humbleElementConstants.headerSectionSelector)).thenResolve(fakeHeaderElement);

        td.when(fakeBrowser.getElementText(pageTitleSelector)).thenResolve('some fake text without issue');
        return fakeBrowser;
    }

    it('should return report downloader with info that user needs to input mail code ', async () => {
        const fakeBrowser = td.object<Browser>();
        td.when(fakeBrowser.elementExists(humbleElementConstants.usernameInputSelector)).thenResolve(true);
        td.when(fakeBrowser.elementExists(humbleElementConstants.mailCodeInputSelector)).thenResolve(true);

        const reportDownloader = new HumbleBundleReportDownloader({login, password, browser: fakeBrowser});
        const result: LoginResult = await reportDownloader.performLogin();

        td.verify(fakeBrowser.goto(humbleElementConstants.dashboardUrl));
        td.verify(fakeBrowser.type(humbleElementConstants.usernameInputSelector, login));
        td.verify(fakeBrowser.type(humbleElementConstants.passwordInputSelector, password));
        td.verify(fakeBrowser.click(humbleElementConstants.loginButtonSelector));
        expect(result.is2FactorAuth()).to.be.true;
    });

    it('should return report downloader with info that user needs to input mail code while downloading report ', async () => {
        const fakeBrowser = td.object<Browser>();
        td.when(fakeBrowser.elementExists(humbleElementConstants.usernameInputSelector)).thenResolve(true);
        td.when(fakeBrowser.elementExists(humbleElementConstants.mailCodeInputSelector)).thenResolve(true);

        const reportDownloader = new HumbleBundleReportDownloader({login, password, browser: fakeBrowser});
        const result: DownloadReportResult = await reportDownloader.downloadReport(startDate, endDate);

        td.verify(fakeBrowser.goto(humbleElementConstants.dashboardUrl));
        td.verify(fakeBrowser.type(humbleElementConstants.usernameInputSelector, login));
        td.verify(fakeBrowser.type(humbleElementConstants.passwordInputSelector, password));
        td.verify(fakeBrowser.click(humbleElementConstants.loginButtonSelector));
        expect(result.isAuthenticationException()).to.be.true;
    });

    it('properly download report when user is already logged in', async () => {
        const fakeBrowser = prepareBrowserForDownload();
        td.replace(fileUtils, 'renameFile');
        const reportDownloader = new HumbleBundleReportDownloader({login, password, browser: fakeBrowser});
        const result = await reportDownloader.downloadReport(startDate, endDate);

        td.verify(fakeBrowser.goto(humbleElementConstants.dashboardUrl));
        const downloadedReportFileName = humbleElementConstants.getDownloadedReportFileName(startDate, moment(endDate).add(1, 'd'));

        td.verify(fakeBrowser.downloadReportFile(fakeHrefs[3], downloadedReportFileName));
        expect(result.isReport()).to.be.true;
    });

    it('properly complete report download', async () => {
        const fakeBrowser = prepareBrowserForDownload();
        const reportDownloader = new HumbleBundleReportDownloader({login, password, browser: fakeBrowser});
        td.replace(fileUtils, 'renameFile');
        await reportDownloader.completeReportDownload(startDate, endDate);

        td.verify(fakeBrowser.downloadReportFile(fakeHrefs[3], humbleElementConstants.getDownloadedReportFileName(startDate, moment(endDate).add(1, 'd'))));
        td.verify(fakeBrowser.waitForSelector(humbleElementConstants.reportDownloadButtonSelector, humbleElementConstants.pageLoadWaitTime));
    });

    it('throws InsufficientPrivilegesLevelException when report page returns 403', async () => {
        const fakeBrowser = prepareBrowserForDownload();
        const reportDownloader = new HumbleBundleReportDownloader({login, password, browser: fakeBrowser});
        td.replace(humbleElementConstants, 'generateReportPageLink');

        td.when(humbleElementConstants.generateReportPageLink(startDate, endDate)).thenReturn('mocked-report-url');
        td.when(fakeBrowser.goto('mocked-report-url')).thenResolve(mockHttpResponse(403));

        await expect(reportDownloader.completeReportDownload(startDate, endDate)).to.be.rejectedWith(InsufficientPrivilegesLevelException);
    });

    it('properly fill second authentication factor', async () => {
        const fakeBrowser = td.object<Browser>();

        const reportDownloader = new HumbleBundleReportDownloader({login, password, browser: fakeBrowser});
        await reportDownloader.fillSecondAuthenticationFactor(secondAuthenticationFactor);

        td.verify(fakeBrowser.clearAndType(humbleElementConstants.mailCodeInputSelector, secondAuthenticationFactor));
        td.verify(fakeBrowser.click(humbleElementConstants.mailCodeConfirmationButtonSelector));
    });

    it('throw error when improper 2FA code', async () => {
        const fakeBrowser = td.object<Browser>();
        td.when(fakeBrowser.elementExists(humbleElementConstants.mailCodeErrorSelector)).thenResolve(true);

        const reportDownloader = new HumbleBundleReportDownloader({login, password, browser: fakeBrowser});
        const wrapperFn = async () => reportDownloader.fillSecondAuthenticationFactor(secondAuthenticationFactor);

        await shouldThrowAsync(wrapperFn, new Invalid2FACode());
        td.verify(fakeBrowser.clearAndType(humbleElementConstants.mailCodeInputSelector, secondAuthenticationFactor));
        td.verify(fakeBrowser.click(humbleElementConstants.mailCodeConfirmationButtonSelector));
    });

    it('return sourceSideOrganizations', async () => {
        const reportDownloader = new HumbleBundleReportDownloader({login, password, browser: td.object<Browser>()});
        const result = await reportDownloader.getSourceSideOrganizations();
        expect(result).to.deep.equal([defaultSourceSideOrganization]);
    });

    it('properly return authentication error when wrong credentials provided', async () => {
        const fakeBrowser = td.object<Browser>();
        td.when(fakeBrowser.elementExists(humbleElementConstants.usernameInputSelector)).thenResolve(true);
        td.when(fakeBrowser.elementExists(humbleElementConstants.mailCodeInputSelector)).thenResolve(true);
        td.when(fakeBrowser.elementExists(humbleElementConstants.loginFailedSelector)).thenResolve(true);

        const reportDownloader = new HumbleBundleReportDownloader({login, password, browser: fakeBrowser});
        const result: LoginResult = await reportDownloader.performLogin();

        expect(result.isAuthenticationException()).to.be.true;
    });

    ['401 Unauthorized', '403 error, access denied'].forEach((text) => {
        it(`properly detect unauthorized page when a page with title: ${text} is visible`, async () => {
            const fakeBrowser = td.object<Browser>();
            td.when(fakeBrowser.elementExists(pageTitleSelector)).thenResolve(true);
            td.when(fakeBrowser.getElementText(pageTitleSelector)).thenResolve(text);

            const reportDownloader = new HumbleBundleReportDownloader({login, password, browser: fakeBrowser});

            await expect(reportDownloader.checkIfUserIsUnauthorized()).to.be.rejectedWith(
                "Account has insufficient privileges or you don't have any products available to download."
            );
        });
    });

    it(`properly detect that the page is not an unauthorized page`, async () => {
        const fakeBrowser = td.object<Browser>();
        td.when(fakeBrowser.elementExists(pageTitleSelector)).thenResolve(true);
        td.when(fakeBrowser.getElementText(pageTitleSelector)).thenResolve('all clear!');

        const reportDownloader = new HumbleBundleReportDownloader({login, password, browser: fakeBrowser});

        await expect(reportDownloader.checkIfUserIsUnauthorized()).to.be.fulfilled;
    });
});
