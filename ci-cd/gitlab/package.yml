package cli:
    stage: package
    interruptible: true
    needs: ['build', 'lint', 'unit test']
    script:
        - ./package/package-cli.sh package_cli_prod node20-linux-x64,node20-macos-x64,node20-win-x64
    artifacts:
        name: unsigned_cli_package_prod
        paths:
            - package_cli_prod
        expire_in: 10 days
    rules:
        - if: '$PIPELINE == "Release"'
    variables:
        PKG_CACHE_PATH: ./.pkg-cache
    cache:
        - key: pkg
          paths: ['.pkg-cache']

sign windows cli:
    stage: package
    needs: ['package cli']
    tags:
        - indiebi
        - linux
        - ev-cert
    dependencies: ['package cli']
    script:
        - ./package/sign/sign.sh 'package_cli_prod/scrape-win.exe'
    artifacts:
        name: signed_windows_cli_package_prod
        paths:
            - package_cli_prod
    rules:
        - if: '$PIPELINE == "Release"'

upload windows cli to azure:
    stage: package
    needs: ['sign windows cli']
    script:
        - 'curl -f -X PUT --data-binary ''@package_cli_prod/scrape-win.exe'' -H ''x-ms-blob-type'' --header ''x-ms-blob-type: BlockBlob'' --header ''Content-Type: application/x-msdos-program'' "$PRODUCTION_SCP_CONTAINER_URL/bin/scrapers-cli-win-$(scripts/get-version.sh).exe?$SCP_CONTAINER_SAS_TOKEN"'
    rules:
        - if: '$PIPELINE == "Release"'

upload linux cli to azure:
    stage: package
    needs: ['package cli']
    script:
        - 'curl -f -X PUT --data-binary ''@package_cli_prod/scrape-linux'' -H ''x-ms-blob-type'' --header ''x-ms-blob-type: BlockBlob'' --header ''Content-Type: application/octet-stream'' "$PRODUCTION_SCP_CONTAINER_URL/bin/scrapers-cli-linux-$(scripts/get-version.sh)?$SCP_CONTAINER_SAS_TOKEN"'
    rules:
        - if: '$PIPELINE == "Release"'

upload macos cli to azure:
    stage: package
    needs: ['package cli']
    script:
        - 'curl -f -X PUT --data-binary ''@package_cli_prod/scrape-macos'' -H ''x-ms-blob-type'' --header ''x-ms-blob-type: BlockBlob'' --header ''Content-Type: application/octet-stream'' "$PRODUCTION_SCP_CONTAINER_URL/bin/scrapers-cli-mac-$(scripts/get-version.sh)?$SCP_CONTAINER_SAS_TOKEN"'
    rules:
        - if: '$PIPELINE == "Release"'
