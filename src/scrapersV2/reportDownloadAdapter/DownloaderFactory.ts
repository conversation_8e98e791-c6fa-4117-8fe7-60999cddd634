import {Source} from '../../dataTypes';
import {CustomException} from '../../error/CustomException';
import {errorTypes} from '../../error/errorTypes';
import {GOGReportDownloader} from '../../scrapersV1/httpDownloaders/gog/GOGReportDownloader';
import {HumbleBundleReportDownloader} from '../../scrapersV1/httpDownloaders/humbleBundle/HumbleBundleReportDownloader';
import {MetaReportDownloader} from '../../scrapersV1/httpDownloaders/meta/MetaReportDownloader';
import {NintendoReportDownloader} from '../../scrapersV1/httpDownloaders/nintendo/NintendoReportDownloader';
import {ReportDownloader} from '../../scrapersV1/ReportDownloader';
import {ReportDownloaderProperties} from '../../scrapersV1/ReportDownloaderProperties';
import ManualLoginDetails from '../ManualLoginDetails';
import {v2Scrapers} from '../V2ScrapersList';

/** @deprecated */
// We need this type to use in map, because the "ReportDownloader" interface requires the class instance in map, but we need to use only class type.
type InterfaceReportDownloader = typeof MetaReportDownloader | typeof HumbleBundleReportDownloader | typeof NintendoReportDownloader | typeof GOGReportDownloader;

/** @deprecated */
const v1SourceMap = new Map<Source, InterfaceReportDownloader>([
    [Source.META_RIFT_SALES, MetaReportDownloader],
    [Source.META_QUEST_SALES, MetaReportDownloader],
    [Source.HUMBLE_SALES, HumbleBundleReportDownloader],
    [Source.NINTENDO_SALES, NintendoReportDownloader],
    [Source.GOG_SALES, GOGReportDownloader]
]);
/** @deprecated */

export function getDownloader(args: ReportDownloaderProperties): ReportDownloader {
    const downloader = v1SourceMap.get(args.source!);
    if (!downloader) {
        throw new CustomException({
            message: `No such source: ${args.source!}`,
            errorType: errorTypes.NOT_SUPPORTED
        });
    }
    return new downloader(args);
}

/** @deprecated */
export function getManualLoginDetails(source: Source): ManualLoginDetails {
    const reportDownloaderClass = v1SourceMap.get(source)!;

    if (reportDownloaderClass && 'manualLoginDetails' in reportDownloaderClass) {
        return reportDownloaderClass.manualLoginDetails;
    }

    const scraperV2 = v2Scrapers.get(source);
    if (scraperV2?.manualLoginDetails) {
        return scraperV2?.manualLoginDetails;
    }

    throw new CustomException({
        message: `Operation requiring manual login is not supported for ${source}`,
        errorType: errorTypes.NOT_SUPPORTED
    });
}
