import click
from cryptography.fernet import Fernet
import os


@click.command
@click.option("--encrypt", "mode", flag_value="encrypt", default=True)
@click.option("--decrypt", "mode", flag_value="decrypt")
def main(mode):

    if mode == "encrypt":
        encrypt()
    else:
        decrypt()


def encrypt():
    with open("credentials.json", "rb") as inf:
        with open("credentials_encrypted.json", "wb") as outf:
            payload = inf.read()
            f = Fernet(os.environ["SCP_CREDENTIALS_ENCRYPTION_KEY"])
            outf.write(f.encrypt(payload))


def decrypt():
    with open("credentials_encrypted.json", "rb") as inf:
        with open("credentials.json", "wb") as outf:
            payload = inf.read()
            f = Fernet(os.environ["SCP_CREDENTIALS_ENCRYPTION_KEY"])
            outf.write(f.decrypt(payload))


if __name__ == "__main__":
    main()
