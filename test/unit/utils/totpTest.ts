import {expect} from 'chai';
import * as moment from 'moment';
import * as totp from '../../../src/utils/totp';

describe('totp', () => {
    it('should calculate a valid TOTP code', async () => {
        const password = totp.generate('LBDUUVBWJFIVMUBSJ43VIV2GKRMVEN2HJ5IUIWSGIJFUQS2VJRJA', moment(1615975864840));
        expect(password).to.be.equal('783650');
    });

    it('should throw that authenticator key should be made only from base32 characters', async () => {
        expect(() => totp.generate('X6WM 24S2 BVID CTE5', moment(1615975864840))).to.throw(
            "Invalid authenticator key. Invalid character: ' '. Try to remove all spaces from your authenticator key."
        );
    });

    it('should calculate a valid Steam Guard code from base64-encoded secret', async () => {
        const code = totp.generateSteamGuard('mVVHG/f0kZDcBRhEuTcy9k1nq2A=', moment(1617034142680));
        expect(code).to.be.equal('KCJ8B');
    });

    it('should calculate a valid Steam Guard code from base32-encoded secret', async () => {
        const code = totp.generateSteamGuard('TFKUOG7X6SIZBXAFDBCLSNZS6ZGWPK3A', moment(1617034142680));
        expect(code).to.be.equal('KCJ8B');
    });

    it('should calculate a valid Steam Guard code from hex-encoded secret', async () => {
        const code = totp.generateSteamGuard('9955471bf7f49190dc051844b93732f64d67ab60', moment(1617034142680));
        expect(code).to.be.equal('KCJ8B');
    });
});
