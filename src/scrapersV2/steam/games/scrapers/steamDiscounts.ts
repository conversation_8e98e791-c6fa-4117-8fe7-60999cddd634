import * as _ from 'lodash';
import * as moment from 'moment'; // non default import must be used
import {<PERSON><PERSON>er<PERSON><PERSON>, BrowserSession} from '../../../../browserV2';
import {Source} from '../../../../dataTypes';
import {SourceSideOrganization} from '../../../../dataTypes/SourceSideOrganization';
import {MissingPermissionsException} from '../../../../error/common';
import {TemporaryPortalException} from '../../../../error/exceptions/TemporaryPortalException';
import {buildFilePath} from '../../../../utils/files/fileUtils';
import {JsonFile} from '../../../../utils/files/JsonFile';
import {HTTPClient} from '../../../../utils/http';
import {toCamelCase} from '../../../../utils/objectOperations';
import {ProgressCallback} from '../../../ProgressCallback';
import {ReportManifest} from '../../../ReportManifest';
import {ScrapeFunction, Scraper} from '../../../Scraper';
import {createZipBasedOnManifest} from '../../../zip';
import {SteamParams, getLoginFunction} from '../../common/login';
import {steamScraperWrapper} from '../../common/scraperWrapper';
import {checkSteamGamesLogin, checkSteamGamesSession} from '../checkSession';
import {DiscountInfo, DiscountManagementEntry, SteamAjaxDiscountsResponse, SteamAjaxEventsResponse} from '../DiscountManagementData';
import {getSourceSideOrganizations} from '../getSourceSideOrganizations';
import {convertToCSVsFiles} from '../steamDiscountsJson2CsvConverters';
import {steamGamesCheerioRequest, steamGamesRequest, steamGamesUrl} from '../steamGamesRequest';

const showAllWeeklongDiscounts = "//span[contains(text(), 'Show all weeklong discounts')]";
const exportCSVButtonXPath = "//button[contains(text(), 'Import/Export CSV...')]";
const downloadCSVButtonXPath = "//button[contains(text(), 'Download all') and not(contains(@class,'Disabled'))]";
const downloadCSVButtonIsAvailableButDisabled = "//button[contains(text(), 'Download all') and contains(@class,'Disabled')]";
const missingPermissionsXPath = "//div[@id='message' and h3[contains(text(), 'You do not have any packages available for pricing')]]";
const discountManagementDataFileName = 'discountManagementData.json';
const discountHistoryFileName = 'discountHistory.json';

const scrape: ScrapeFunction<SteamParams, BrowserSession> = async (context, dateFrom, dateTo) => {
    const organizations = await getSourceSideOrganizations(context);
    const {progress} = context;
    const manifest = new ReportManifest(dateFrom, dateTo);

    const httpClient = context.getHttpClient();
    const page = await context.getPage();
    const discountManagementEntries: DiscountManagementEntry[] = [];
    const discountInfoHistory: DiscountInfo[] = [];
    const invalidOrganizations: SourceSideOrganization[] = [];
    for (const {name, id: publisherId} of organizations) {
        try {
            if (context.params?.ignoredOrganizations?.find((ignored: string | number) => ignored.toString() === publisherId || ignored.toString() === name)) {
                progress(`Skipping organization: ${name} (${publisherId})`);
                continue;
            }
            progress(`Beginning of scraping data from organization: ${name} (${publisherId})`);
            await setActiveOrganization(publisherId, httpClient);

            progress('Validating permissions...');
            await validatePermissions(publisherId, name, httpClient);

            const allDiscountsCSVFileName = await getAllDiscountsCSV(publisherId, name, page);
            manifest.addFile(allDiscountsCSVFileName, {organization: publisherId, dateFrom, dateTo, rawData: true});

            const discountManagementEntry = await getDiscountManagementData(publisherId, name, httpClient, progress);
            discountManagementEntries.push(discountManagementEntry);

            const publisherDiscountHistory = await getDiscountsHistory(discountManagementEntry.packageIds, publisherId, httpClient, progress);
            discountInfoHistory.push(...publisherDiscountHistory);
        } catch (e) {
            if (!(e instanceof MissingPermissionsException)) {
                throw e;
            }
            // This is to proceed in case of MissingPermissionsException thrown for one of the organizations, to get more data from other organizations
            invalidOrganizations.push({name, id: publisherId});
        }
    }
    if (invalidOrganizations.length === organizations.length || organizations.length === 0) {
        throw new MissingPermissionsException(invalidOrganizations);
    }

    await new JsonFile(buildFilePath(discountManagementDataFileName)).save(discountManagementEntries);
    await new JsonFile(buildFilePath(discountHistoryFileName)).save(discountInfoHistory);

    const csvFilenames = await convertToCSVsFiles(discountManagementEntries, discountInfoHistory);

    [discountManagementDataFileName, discountHistoryFileName, ...csvFilenames].forEach((fileName) => {
        manifest.addFile(fileName, {dateFrom, dateTo, rawData: fileName.endsWith('.json')});
    });

    return createZipBasedOnManifest(manifest, Source.STEAM_DISCOUNTS, discountManagementEntries.length === 0);
};

async function setActiveOrganization(publisherId: string, httpClient: HTTPClient) {
    await steamGamesCheerioRequest(`/dashboard?requestedPrimaryPublisher=${publisherId}`, {httpClient});
}

async function validatePermissions(publisherId: string, organizationName: string, httpClient: HTTPClient): Promise<void> {
    const discountManagementPage = await steamGamesCheerioRequest(`/promotion/discounts/dashboard/${publisherId}`, {httpClient});
    if (discountManagementPage('.errorContainer #message ').text().trim() === 'You do not have any packages available for pricing.') {
        throw new MissingPermissionsException([{id: publisherId, name: organizationName}]);
    }
}

async function getAllDiscountsCSV(publisherId: string, organizationName: string, page: BrowserPage): Promise<string> {
    const fileName = `discounts_all_${publisherId}.csv`;
    await page.goto(`${steamGamesUrl}/promotion/discounts/dashboard/${publisherId}`);

    /**
     * New years empty table bug:
     * In an edge case when in the first week of new year when there are no discounts set up the table is empty
     * If we do not click the showAllWeekLongDiscounts the exportCSVButtonXPath will be visible but not clickable
     * This affected both us and clients.
     */
    await page.clickByXPath(showAllWeeklongDiscounts);
    await page.clickByXPath(exportCSVButtonXPath);

    const [selector] = await page.waitForAnySelector({
        xPaths: [downloadCSVButtonXPath, missingPermissionsXPath, downloadCSVButtonIsAvailableButDisabled]
    });

    if (selector === missingPermissionsXPath || selector === downloadCSVButtonIsAvailableButDisabled) {
        /**
         * Start of month bug:
         * at the beginning of each month (last day of the month or first day of a new month depending on the timezone)
         * the download button is visible but disabled due to some kind of steam shenanigans.
         * Since we do not have any way of handling the issue (manually enabling the button does not help) wa handle it as a temporary portal issue.
         */
        const today = moment();
        const isFirstDayOfMonth = today.isSame(today.startOf('month'), 'day');
        const isLastDayOfMonth = today.isSame(today.endOf('month'), 'day');

        if (selector === downloadCSVButtonIsAvailableButDisabled && (isFirstDayOfMonth || isLastDayOfMonth)) {
            throw new TemporaryPortalException('Download button is available but disabled due to Steam internal issues, the situation should fix itself in a few days.');
        }

        throw new MissingPermissionsException([{id: publisherId, name: organizationName}]);
    }

    await page.downloadByXPathSelector(downloadCSVButtonXPath, fileName);
    return fileName;
}

async function getDiscountManagementData(
    publisherId: string,
    organizationName: string,
    httpClient: HTTPClient,
    progress: ProgressCallback
): Promise<DiscountManagementEntry> {
    progress(`Getting discount management data for publisherId: ${publisherId}. Loading discount management page...`);
    const discountManagementPage = await steamGamesCheerioRequest(`/promotion/discounts/dashboard/${publisherId}`, {httpClient});

    if (discountManagementPage('.errorContainer #message ').text().trim() === 'You do not have any packages available for pricing.') {
        progress(`No discount management data for publisherId: ${publisherId}.`);
        throw new MissingPermissionsException([{id: publisherId, name: organizationName}]);
    }

    const {basePrices, maxDiscountPercentages, packageIds, priceIncreaseTimes, publisherid, userinfo} = toCamelCase(discountManagementPage('#application_config').data());

    const discountEvents = toCamelCase(await getAllDiscountEvents(publisherId, httpClient, progress));
    const packageDiscounts = toCamelCase(await getAllPackageDiscounts(publisherId, packageIds, httpClient, progress));

    progress(`Got discount management data for publisherId: ${publisherId}.`);

    return {
        basePrices,
        discountEvents,
        maxDiscountPercentages,
        packageDiscounts,
        packageIds,
        priceIncreaseTimes,
        publisherid,
        userinfo
    };
}

async function getDiscountsHistory(productIds: number[], publisherId: string, httpClient: HTTPClient, progress: ProgressCallback): Promise<DiscountInfo[]> {
    return Promise.all(
        productIds.map(async (productId) => {
            progress(`Getting discount history for publisherId: ${publisherId}, productId: ${productId}. Loading discount history page...`);
            const $ = await steamGamesCheerioRequest(`/packages/discounts/${productId}`, {httpClient});

            const data = $('tr')
                .filter((_, elem) => $(elem).data('discountData') !== undefined)
                .map((_, elem) => ({
                    ...($(elem).data('discountData') as any),
                    class: $(elem).attr('class'),
                    productId,
                    publisherId: Number(publisherId)
                }))
                .get();

            progress(`Got discount history for publisherId: ${publisherId}, productId: ${productId}.`); // QUESTION: should we replace map with for, and return more detailed progress?
            return toCamelCase(data) as DiscountInfo;
        })
    ).then((discounts) => discounts.flat());
}

async function getAllDiscountEvents(publisherId: string, httpClient: HTTPClient, progress: ProgressCallback) {
    progress(`Getting discount events for publisherId: ${publisherId}.`);
    const {data} = await steamGamesRequest<SteamAjaxEventsResponse>(`/promotion/discounts/ajaxgetalldiscountevents/${publisherId}`, {httpClient, followRedirects: true});
    if (data.success !== 1 || !data.events) {
        throw new TemporaryPortalException(data.msg ?? 'Failed to get discount events from Steam request');
    }
    return data.events;
}

async function getAllPackageDiscounts(publisherId: string, packageIds: number[], httpClient: HTTPClient, progress: ProgressCallback) {
    const STEAM_IDS_PER_REQUEST_LIMIT = 50; // Steam allows only 50 package ids per request
    const chunks = _.chunk(packageIds, STEAM_IDS_PER_REQUEST_LIMIT);

    progress(`Getting package discounts for publisherId: ${publisherId}. Number of package ids: ${packageIds.length}. Going to split into ${chunks.length} requests.`);

    const allPackageDiscounts: any[] = [];
    for (const chunk of chunks) {
        progress(`Getting package discounts for package ids: ${chunk.join(',')}`);
        const {data} = await steamGamesRequest<SteamAjaxDiscountsResponse>(`/promotion/discounts/ajaxgetpackagediscounts/${publisherId}?packageids=${chunk.join(',')}`, {
            httpClient,
            followRedirects: true
        });
        if (data.success !== 1 || !data.discounts) {
            throw new TemporaryPortalException(data.msg ?? 'Failed to get package discounts from Steam request');
        }
        allPackageDiscounts.push(...data.discounts);
    }
    progress(`Got package discounts for publisherId: ${publisherId}.`);
    return allPackageDiscounts;
}

export const steamDiscountsScraper: Scraper<SteamParams, BrowserSession> = steamScraperWrapper({
    login: getLoginFunction(Source.STEAM_DISCOUNTS, checkSteamGamesLogin),
    scrape,
    getSourceSideOrganizations,
    checkSession: checkSteamGamesSession,
    manualLoginDetails: {
        url: `${steamGamesUrl}/dashboard`,
        successSelector: {value: '#landingWelcome', type: 'css'}
    }
});
