import * as chai from 'chai';
import * as chaiAsPromised from 'chai-as-promised';
import * as td from 'testdouble';
import {Browser} from '../../../../src/browser/Browser';
import {UnableToLoadUrlException} from '../../../../src/error/exceptions/UnableToLoadUrlException';
import * as MetaConstants from '../../../../src/scrapersV1/httpDownloaders/meta/MetaConstants';
import {MetaReportDownloader} from '../../../../src/scrapersV1/httpDownloaders/meta/MetaReportDownloader';
import * as testData from './testData';

chai.use(chaiAsPromised);
const {expect} = chai;

describe('Meta scraper - ErrorPage', () => {
    const {source, validLogin: login, validPassword: password} = testData;
    let fakeBrowser: Browser;
    let metaReportDownloader: MetaReportDownloader;

    beforeEach(() => {
        fakeBrowser = td.object<Browser>();
        td.replace(fakeBrowser, 'goto', Browser.prototype.goto);
        metaReportDownloader = new MetaReportDownloader({login, password, browser: fakeBrowser, source});
    });

    afterEach(td.reset);

    it('should throw unableToLoadUrl exception if it is on FacebookErrorPage', async () => {
        td.when(fakeBrowser.elementExists(MetaConstants.facebookLogoSelector)).thenResolve(true);
        td.when(fakeBrowser.elementExists(MetaConstants.facebookPageLinkSelector)).thenResolve(true);
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.errorPageTitleXPathSelector)).thenResolve(false);

        await expect(metaReportDownloader.performLogin()).to.be.rejectedWith(UnableToLoadUrlException);
        td.verify(fakeBrowser.goto(MetaConstants.dashboardLoginUrl, td.matchers.anything()));
    });

    it('should throw unableToLoadUrl exception if it is on MetaErrorPage', async () => {
        td.when(fakeBrowser.elementExists(MetaConstants.facebookLogoSelector)).thenResolve(false);
        td.when(fakeBrowser.elementExists(MetaConstants.facebookPageLinkSelector)).thenResolve(false);
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.errorPageTitleXPathSelector)).thenResolve(true);

        await expect(metaReportDownloader.performLogin()).to.be.rejectedWith(UnableToLoadUrlException);
        td.verify(fakeBrowser.goto(MetaConstants.dashboardLoginUrl, td.matchers.anything()));
    });

    it('should not throw any Exceptions, with no ErrorPage detected', async () => {
        td.when(fakeBrowser.elementExists(MetaConstants.facebookLogoSelector)).thenResolve(false);
        td.when(fakeBrowser.elementExists(MetaConstants.facebookPageLinkSelector)).thenResolve(false);
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.errorPageTitleXPathSelector)).thenResolve(false);

        await metaReportDownloader.performLogin();
        td.verify(fakeBrowser.goto(MetaConstants.dashboardLoginUrl, td.matchers.anything()));
    });
});
