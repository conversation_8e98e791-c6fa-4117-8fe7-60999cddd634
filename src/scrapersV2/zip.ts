import {Moment} from 'moment';
import {Source} from '../dataTypes';
import {Report} from '../scrapersV1/Report';
import {FileExtension} from '../utils/files/FileExtension';
import {deleteFiles, generateFileName, saveFileToDownloads, zipFiles} from '../utils/files/fileUtils';
import {IReportManifest, ReportManifest} from './ReportManifest';

type ManifestFilename = string;

export async function prepareZipReports(
    startDate: Moment,
    endDate: Moment,
    downloadedFilenames: string[],
    source: Source,
    progress: (message: string) => void,
    metadata?: Record<string, unknown>,
    noData?: boolean
): Promise<Report[]> {
    const zipFileName = generateFileName(source, startDate, endDate, FileExtension.ZIP);

    const manifest: IReportManifest = {
        dateFrom: startDate,
        dateTo: endDate,
        metadata
    };
    progress(`Preparing report zip: ${zipFileName}`);
    await packReportsToZip(downloadedFilenames, zipFileName, manifest);
    return [new Report(source, zipFileName, startDate, endDate, noData)];
}

export async function createZipBasedOnManifest(manifestClass: ReportManifest, source: Source, noData?: boolean) {
    const fileNames = manifestClass.getFileNames();
    const manifest = manifestClass.getManifest();
    const {dateFrom, dateTo} = manifest;

    const zipFileName = generateFileName(source, dateFrom, dateTo, FileExtension.ZIP);
    await packReportsToZip(fileNames, zipFileName, manifest);
    return [new Report(source, zipFileName, dateFrom, dateTo, noData)];
}

export async function packReportsToZip(filenames: string[], outputFilename: string, manifest: IReportManifest): Promise<void> {
    const manifestFilename = await createManifestFile(manifest);
    await zipFiles([...filenames, manifestFilename], outputFilename);
    await deleteFiles([...filenames, manifestFilename]);
}

async function createManifestFile(manifest: IReportManifest): Promise<ManifestFilename> {
    const filename: ManifestFilename = 'manifest.json';
    await saveFileToDownloads(filename, manifest);
    return filename;
}
