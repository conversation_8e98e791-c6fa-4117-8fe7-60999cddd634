import * as moment from 'moment';
import * as MetaConstants from './MetaConstants';
import {MetaSalesRow} from './MetaInterfaces';

export function convertLegacyDashboardJSONtoLegacyCSV(data: any): string {
    const header = `Date,Country,App Sales Revenue (USD),Copies Sold,Active Users\n`;

    const jsonData = data ?? {};
    if (!jsonData.app_store_item) {
        return header;
    }
    const completeData: MetaSalesRow[] = [];

    for (const sale of jsonData.app_store_item.revenue_analytics_data.app_revenue.time_series_data) {
        const date = sale.timestamp;
        const country = sale.country || 'Unknown';
        const revenue = sale.revenue || 0;
        const copiesSold = sale.copies_sold || 0;
        const activeUsers = sale.active_users || 0;
        completeData.push({date, country, revenue, copiesSold, activeUsers} as MetaSalesRow);
    }

    let csvContent = header;
    for (const row of completeData) {
        csvContent += `${moment.unix(row.date).format(MetaConstants.timeFormat)},${row.country},$${row.revenue},${row.copiesSold},${row.activeUsers}\n`;
    }

    return csvContent;
}
