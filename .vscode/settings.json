{"[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[python]": {"editor.defaultFormatter": "ms-python.python"}, "python.terminal.activateEnvInCurrentTerminal": false, "python.testing.pytestEnabled": true, "python.testing.pytestArgs": ["--vcr-mode=playback"], "diffEditor.ignoreTrimWhitespace": false, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.formatOnSave": true, "files.eol": "\n", "mochaExplorer.env": {"NODE_ENV": "test"}, "mochaExplorer.exit": true, "mochaExplorer.files": "test/unit/**/*.ts", "mochaExplorer.require": "ts-node/register", "testExplorer.useNativeTesting": true, "typescript.enablePromptUseWorkspaceTsdk": true, "typescript.implementationsCodeLens.enabled": true, "typescript.preferences.importModuleSpecifier": "relative", "typescript.referencesCodeLens.enabled": true, "typescript.referencesCodeLens.showOnAllFunctions": false, "typescript.tsdk": "node_modules/typescript/lib", "python.formatting.provider": "black", "python.analysis.extraPaths": ["./test/vcr"], "python.testing.cwd": "test/vcr", "python.envFile": "${workspaceFolder}/test/vcr/.env"}