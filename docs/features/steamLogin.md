# Browser less Steam login method

## Introduction

Based on third party documentation: https://github.com/DoctorMcKay/node-steam-session

In order to minimize user input required for logging into Steam, we have implemented a browser less login method mimicking a Steam Client application. In order to authenticate requests, we need cookies which we get after successful login.

## How it works

1. We do authenticate user with username and password. First login attempt requires 2FA code.
2. After successful login, we receive cookies, refreshToken and steamGuardMachineToken.
3. We use refreshToken to get valid cookies.
4. We store cookies, refreshToken and steamGuardMachineToken in session file.
5. In case we need to authenticate again, we can use steamGuardMachineToken to bypass 2FA code.

### Refresh token

Refresh token is being used to refresh cookies. It is valid for 6 months. After that time, user will be required to log in again. Refresh token is stored in session file. We're using it on every occasion to generate fresh cookies. 

### Steam Guard Machine Token

Steam Guard Machine Token can be used to bypass 2FA. If we provide valid machine token together with username and password, Steam will not send nor ask for 2FA code. Steam Guard Machine Token is stored in session file after successful login and can be used for consecutive logins without user input.

### Cookies

Cookies are used to authenticate requests. They are valid for random period of time. Fortunately, we can refresh them on every occasion using the refresh token. We do it even in "check session" mechanism to get the newest cookies. Refreshing cookies is done in background and does not require user input.

# Known issues and limitations

1. Both refresh token and steam guard machine token are valid for 6 months. After that time, user will be required to log in again.
2. Both tokens contain the user IP address. We know that Steam does not strictly check the IP, but it often invalidates the tokens if the user logged in one country and then tries to use the tokens in another one. We encountered this issue during testing as we generated sessions locally (PL) and tried to use it on remote machines in the USA. We are not sure if it is possible to bypass this limitation. In such case, the user will see "Access denied" error and will be required to log in again with username, password and 2FA code. Both tokens will not be valid anymore and cannot be used even in the original country. Both need to be regenerated by logging in again.
3. Steam guard machine tokens can be used only once. After successful login, the old token is invalidated and cannot be used again. Steam will return a new one which can be used for consecutive logins.
4. Refresh tokens seems to be connected with machine tokens and will become invalidated if you use the machine token for new login attempt.

# Text diagram to explain the method

```
/**
 *  (LOGIN WITH CREDENTIALS + 2FA) -> REFRESH_TOKEN + MACHINE GUARD TOKEN
 *  (LOGIN_WITH CREDENTIALS + MACHINE GUARD TOKEN) -> REFRESH_TOKEN (no 2FA)
 *  REFRESH_TOKEN -> COOKIES
 */
```
