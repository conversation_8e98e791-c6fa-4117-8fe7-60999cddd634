import {expect} from 'chai';
import * as td from 'testdouble';
import {isValidBrowserSession} from '../../../src/browserV2/sessionValidators';

describe('session validators', function () {
    afterEach(td.reset);

    describe('isValidBrowserSession', () => {
        it('should return true when session is valid', () => {
            isValidBrowserSession({cookies: [{name: 'foo', value: 'bar'}]});
        });

        [{}, {cookies: ''}, {cookies: []}, {cookies: 'test'}, {cookies: {}}].forEach((session: any) => {
            it(`should not pass validation for ${JSON.stringify(session)}`, () => {
                expect(isValidBrowserSession(session)).to.be.false;
            });
        });
    });
});
