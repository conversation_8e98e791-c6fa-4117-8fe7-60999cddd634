{"calls": {"https://partner.steampowered.com/login": [{"req_headers": {"accept": "application/json, text/plain, */*", "user-agent": "axios/0.27.2", "host": "partner.steampowered.com", "connection": "close"}, "body": {"data": "https_partner.steampowered.com/login", "encoding": "file"}, "headers": {"server": "nginx", "content-type": "text/html; charset=UTF-8", "content-security-policy": "default-src blob: data: https: 'unsafe-inline' 'unsafe-eval'; connect-src 'self' https://community.akamai.steamstatic.com/ https://store.steampowered.com/ wss://community.steam-api.com/websocket/ https://api.steampowered.com/ https://login.steampowered.com/ https://help.steampowered.com/ https://*.valvesoftware.com https://*.steambeta.net https://*.steamcontent.com; report-uri /actions/CSPReport", "content-encoding": "gzip", "vary": "Accept-Encoding", "connection": "close"}}]}, "defaults": {"request_headers": {}, "headers": {}}}