#!/bin/bash

SCRAPERS_BIN_VERSION=$1

if [[ -z "$SCRAPERS_BIN_VERSION" ]]; then
    echo "SCRAPERS_BIN_VERSION is not set"
    SCRAPERS_BIN_VERSION=$(curl -fsL https://scraper-api.indiebi.com/1/latest-stable-edition | jq -r '.cli.default.scrapers')
    echo "Using latest stable version: $SCRAPERS_BIN_VERSION"
fi

PLATFORM='mac'
EXTENSION='' # '.exe' for windows, '' for mac and linux

SCRAPER_BIN_FILE=scrapers-cli-${PLATFORM}-${SCRAPERS_BIN_VERSION}${EXTENSION}

curl https://stscraperbinstorag01db.blob.core.windows.net/prod-scraper-client/bin/${SCRAPER_BIN_FILE} -o /tmp/scrape
chmod +x /tmp/scrape

if [[ "$OSTYPE" == "darwin"* ]]; then # Mac OSX
    TARGET_SCRAPE_JS_DIR=$(find ~/Library/Application\ Support/indiebi-desktop/scrapers/binaries/scrapers -name "scrapers-cli-mac-*" ! -name "*-py" -type d)
else # Linux
    TARGET_SCRAPE_JS_DIR=$(find ~/.config/indiebi-desktop/scrapers/binaries/scrapers -name "scrapers-cli-linux-*" ! -name "*-py" -type d)
fi

if [[ -z "$TARGET_SCRAPE_JS_DIR" ]]; then
    echo "Target directory not found!"
    exit 1
fi

cp /tmp/scrape "$TARGET_SCRAPE_JS_DIR"

echo "--------------------------"
echo "Binary $SCRAPERS_BIN_VERSION has been copied to: $TARGET_SCRAPE_JS_DIR"

rm /tmp/scrape
