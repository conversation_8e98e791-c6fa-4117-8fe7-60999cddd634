import {StatusCodes} from 'http-status-codes';
import {getAuthCode} from '../../apiCommunication/authCodes';
import {BrowserPage, BrowserSession} from '../../browserV2';
import {printDualAuth, printDualAuthSuccess} from '../../cli/messaging';
import {Portal, Source} from '../../dataTypes';
import {SourceIdentifier, SourceSideOrganization} from '../../dataTypes/SourceSideOrganization';
import {
    AllOrganizationsIgnoredException,
    InsufficientPrivilegesLevelException,
    LoginException,
    LoginExceptionType,
    NotFoundAnyOrganizationsException,
    RequestFailedException,
    UnableToDownloadReportException
} from '../../error/exceptions';
import {downloadForOrganizationFinished, foundMultipleOrganizations, ignoringOrganization, startingDownloadForOrganization} from '../../telemetry/events/multiorgEvents';
import {stringifyAndFilterSensitiveFields} from '../../telemetry/filterSensitiveData';
import {isManualSession} from '../../utils/conditions';
import {FileExtension} from '../../utils/files/FileExtension';
import {createWriteStreamInDownloadDirectory, generateFileName} from '../../utils/files/fileUtils';
import {filterIgnoredItems} from '../../utils/filters';
import {log} from '../../utils/logger';
import * as totp from '../../utils/totp';
import {ProgressCallback} from '../ProgressCallback';
import {CheckSessionFunction, GetSourceSideOrganizationsFunction, LoginFunction, ScrapeFunction, Scraper, ScraperParams} from '../Scraper';
import {prepareZipReports} from '../zip';
import {hasSessionSteamCookies, loginWithSteam} from './loginWithSteam';

const dashboardPageUrl = 'https://dev.epicgames.com/portal/en-US/';

const apiUrl = 'https://dev.epicgames.com/portal/api/v2/services';

const loginWithEpicButtonXPathSelector = "//button[contains(text(), 'Sign in')]";

const usernameInputSelector = '#email';
const passwordInputSelector = '#password';
const loginButtonSelector = '#sign-in';

const incorrectEmailSelector = '#email-helper-text.Mui-error';
const incorrectPasswordSelector = 'form > div.error > h6.MuiTypography-root';
const incorrectPasswordSelectorTimeout = 10000;

const arcoseCaptchaSelector = 'iframe[title="arkose-enforcement"]';
const talonCaptchaSelector = 'iframe[id="talon_frame_login_prod"]';

const mfaAppChallengeXPath = "//p[contains(text(), 'authenticator app')]";
const mfaEmailChallengeXPath = "//p[contains(text(), 'email address')]";

const mfaOptionsButtonSelector = '#to-mfa-options';
const mfaAuthenticatorAppOptionSelector = '#option-authenticator';
const mfaCodeInputSelector = '#code';
const mfaCodeInputSelectorAlt = 'input[name="code-input-0"]';
const mfaCodeSubmitSelector = '#continue';
const mfaInvalidSelector = `form.invalid ${mfaCodeInputSelector}`;
const mfaInvalidSelectorAlt = `.Mui-error ${mfaCodeInputSelectorAlt}`;
const enterDateOfBirthXPath = "//h6[contains(text(), 'Please Enter Your Date of Birth')]";

const loginSuccessXPath = "//h2[contains(text(), 'Choose your organization')] | //*[@isloggedin='true']";
const mfaAndCaptchaSelectors = [arcoseCaptchaSelector, talonCaptchaSelector, mfaCodeInputSelector, mfaCodeInputSelectorAlt];

const welcomeGuideSkipButtonSelector = 'button#welcome-guide-skip-button';
const welcomeGuideDismissButtonSelector = 'button#welcome-guide-dismiss-button';

interface EpicOrg extends SourceSideOrganization {
    isPublishing: boolean;
    slug: string;
}

export interface EpicParams extends ScraperParams {
    user: string;
    password: string;
    loginWith: 'steam' | '';
    totpSecret?: string;
    ignoredOrganizations: string[];
}

const isOnErrorPage = async function (page: BrowserPage): Promise<boolean> {
    return !(await page.selectorTestMulti({
        successXPaths: ["//title[contains(text(), 'Dashboard') or contains(text(), 'Sign in')]", loginSuccessXPath, loginWithEpicButtonXPathSelector],
        failureXPaths: ["//title[contains(text(), 'Something went wrong')]"],
        visible: false
    }));
};

const isLoggedIn = async (page: BrowserPage): Promise<boolean> => {
    log.debug('Checking if logged in');
    return page.selectorTestMulti({
        failureXPaths: [loginWithEpicButtonXPathSelector],
        failureSelectors: [usernameInputSelector],
        successXPaths: [loginSuccessXPath]
    });
};

const validateSession = async (page: BrowserPage): Promise<void> => {
    await page.goto(dashboardPageUrl, isOnErrorPage);
    if (!(await isLoggedIn(page))) {
        throw new LoginException(LoginExceptionType.SESSION_EXPIRED);
    }
};
const checkSession: CheckSessionFunction<EpicParams> = async ({getPage, progress}): Promise<SourceIdentifier> => {
    progress('Checking if session is valid');
    let hasScrapeBlockingIssues = true;
    const page = await getPage();
    await validateSession(page);

    await handleWelcomeGuideIfNecessary(progress, page);

    progress('Checking for organizations');

    const apiOrgs = await getApiOrgs(page);

    if (apiOrgs.length) {
        hasScrapeBlockingIssues = false;
    }

    await page.goto('https://www.epicgames.com/account/personal');
    const id = await page.getElementProperty('#displayName', 'value');

    return {
        hasScrapeBlockingIssues,
        id
    };
};

const login: LoginFunction<EpicParams, BrowserSession> = async ({getPage, params, progress, session}) => {
    const page = await getPage();
    await page.goto(dashboardPageUrl, isOnErrorPage);
    if (await isLoggedIn(page)) {
        progress('Already logged in');
        return page.getSession();
    }

    if (isManualSession(params.user, params.password)) {
        progress('Epic Manual session expired');
        if (hasSessionSteamCookies(session)) {
            await loginWithSteam(progress, params, page);
        }
        throw new LoginException(LoginExceptionType.SESSION_EXPIRED);
    }
    progress('No login detected, performing login');

    if (params.loginWith === 'steam') {
        await loginWithSteam(progress, params, page);
    } else {
        await loginWithCredentials(progress, params, page);
    }

    await handleCaptchaAnd2FAIfNecessary(progress, params, page);
    await handleWelcomeGuideIfNecessary(progress, page);
    await verifyAccountNotBlocked(page);

    return page.getSession();
};

const getApiOrgs = async (page: BrowserPage): Promise<EpicOrg[]> => {
    const result = await page.goto(`${apiUrl}/users/organizations`);
    const data = await result?.json();
    return data?.data?.map((org) => ({
        ...org,
        id: org.organizationId,
        hasScrapeBlockingIssue: !org.isPublishing
    }));
};

const getSourceSideOrganizations: GetSourceSideOrganizationsFunction<EpicParams> = async ({getPage}) => {
    const page = await getPage();
    await page.goto(dashboardPageUrl, isOnErrorPage);
    const apiOrgs = await getApiOrgs(page);
    return apiOrgs.map(({name, id, hasScrapeBlockingIssues}) => ({name, id, hasScrapeBlockingIssues}));
};

const scrape: ScrapeFunction<EpicParams> = async ({progress, params, getPage}, startDate, endDate) => {
    const page = await getPage();
    await validateSession(page);
    await handleWelcomeGuideIfNecessary(progress, page);
    const apiOrgs = await getApiOrgs(page);
    const reportFiles: string[] = [];

    if (apiOrgs.length === 0) {
        throw new NotFoundAnyOrganizationsException(Source.EPIC_SALES);
    }

    foundMultipleOrganizations(apiOrgs.map((org) => org.name));

    const filteredOrgs = filterIgnoredItems(apiOrgs, params.ignoredOrganizations, 'id', ({name}) => {
        ignoringOrganization(name);
    }).filter(({name, isPublishing}) => {
        if (!isPublishing) {
            progress(`Organization ${name} is not publishing products, skipping`);
            ignoringOrganization(name);
        }
        return isPublishing;
    });

    if (filteredOrgs.length === 0) {
        throw new AllOrganizationsIgnoredException(Source.EPIC_SALES, false);
    }

    progress(`Scraping for ${filteredOrgs.length} of ${apiOrgs.length}: ${filteredOrgs.map((org) => org.name)}`);

    for (const {id, name, slug} of filteredOrgs) {
        const startDateStr = startDate.format('YYYY-MM-DD');
        const endDateStr = endDate.format('YYYY-MM-DD');
        const localFileName = generateFileName(Source.EPIC_SALES, startDate, endDate, FileExtension.CSV, slug);
        const url = `${apiUrl}/sales/${id}/report/download?startDate=${startDateStr}&endDate=${endDateStr}`;
        progress(`Downloading report for ${name}`);
        startingDownloadForOrganization(name);
        await downloadFile(url, localFileName, page);
        downloadForOrganizationFinished(name);
        progress(`Report download for ${name} complete: ${startDateStr} - ${endDateStr}`);
        reportFiles.push(localFileName);
    }

    return prepareZipReports(startDate, endDate, reportFiles, Source.EPIC_SALES, progress);
};

async function receiveResponseStream(data) {
    if (!data) {
        return null;
    }
    let resultData = '';
    await new Promise<string>((resolve, reject): void => {
        data.on('data', (chunk) => {
            resultData += chunk;
        });
        data.on('end', resolve);
        data.on('error', (error: Error) => reject(new RequestFailedException(error)));
    });
    try {
        return JSON.parse(resultData);
    } catch (error) {
        log.warning(`Failed to parse response: ${stringifyAndFilterSensitiveFields(error)}`);
        return null;
    }
}

function handleEpicResponseError(response: any) {
    const {responseStatusCode, statusCode} = response;

    if (statusCode == StatusCodes.UNAUTHORIZED) {
        throw new LoginException(LoginExceptionType.SESSION_EXPIRED);
    }

    if (responseStatusCode == StatusCodes.FORBIDDEN) {
        const {reason, featureSlug} = response.responseData;

        if (reason == 'REQUIRE_FEATURE_ACCESS') {
            throw new InsufficientPrivilegesLevelException(
                `Your account does not have access to the required feature: ${featureSlug}. Make sure you have the correct permissions.`
            );
        }

        if (reason == 'REQUIRE_MEMBER') {
            throw new InsufficientPrivilegesLevelException('Your account does not have access to specific organizations. Make sure you have the correct permissions.');
        }
    }
}

export async function downloadFile(url: string, localFilename: string, page: BrowserPage): Promise<void> {
    try {
        const response = await page.request('GET', url, {options: {responseType: 'stream'}});
        return new Promise<void>((resolve, reject): void => {
            response.pipe(createWriteStreamInDownloadDirectory(localFilename));
            response.on('end', resolve);
            response.on('error', (error: Error) => reject(new UnableToDownloadReportException(error)));
        });
    } catch (error) {
        const response: any = await receiveResponseStream(error.originalError.response?.data);
        if (response) {
            handleEpicResponseError(response);
        }
        throw error;
    }
}

async function loginWithCredentials(progress: ProgressCallback, params: EpicParams, page: BrowserPage) {
    progress('Logging in with credentials');
    progress('Opening login window');
    await page.clickByXPath(loginWithEpicButtonXPathSelector);

    progress('Typing user name');
    await page.input(usernameInputSelector, params.user);

    progress('Typing password');
    await page.input(passwordInputSelector, params.password);

    progress('Logging in');
    await page.click(loginButtonSelector);

    if (
        !(await page.selectorTestMulti({
            successSelectors: mfaAndCaptchaSelectors,
            successXPaths: [loginSuccessXPath],
            failureSelectors: [incorrectEmailSelector, incorrectPasswordSelector],
            timeout: incorrectPasswordSelectorTimeout
        }))
    ) {
        throw new LoginException(LoginExceptionType.CREDENTIALS_INVALID);
    }
}

async function handleWelcomeGuideIfNecessary(progress: ProgressCallback, page: BrowserPage) {
    progress('Clicking through welcome guide if necessary');
    await Promise.all([page.clickIfExists(welcomeGuideSkipButtonSelector), page.clickIfExists(welcomeGuideDismissButtonSelector)]);
}

async function handleCaptchaAnd2FAIfNecessary(progress: ProgressCallback, params: EpicParams, page: BrowserPage) {
    progress('Checking for captcha/2FA');

    if (await isLoggedIn(page)) {
        return;
    }

    const [selector] = await page.waitForAny({cssSelectors: mfaAndCaptchaSelectors});

    if ([arcoseCaptchaSelector, talonCaptchaSelector].includes(selector)) {
        throw new LoginException(LoginExceptionType.CAPTCHA_REQUIRED);
    }

    if ([mfaCodeInputSelector, mfaCodeInputSelectorAlt].includes(selector)) {
        let epicOtp: string;

        progress('Entering code for 2FA');
        if (params.totpSecret) {
            const isAuthWithAuthenticatorAppSelected = await page.selectorTest({
                successXPath: mfaAppChallengeXPath,
                failureXPath: mfaEmailChallengeXPath
            });
            if (!isAuthWithAuthenticatorAppSelected) {
                // change auth method to authenticator app
                await page.click(mfaOptionsButtonSelector);
                await page.click(mfaAuthenticatorAppOptionSelector);
            }
            epicOtp = totp.generate(params.totpSecret);
        } else {
            printDualAuth({portal: Portal.EPIC, attempt: 1, maxAttempts: 1});
            epicOtp = await getAuthCode({source: Source.EPIC_SALES, portal: Portal.EPIC});
        }

        if (
            await page.selectorTest({
                successSelector: mfaCodeInputSelector,
                failureSelector: mfaCodeInputSelectorAlt
            })
        ) {
            await page.input(mfaCodeInputSelector, epicOtp);
        } else {
            await page.input(mfaCodeInputSelectorAlt, epicOtp);
        }

        await page.click(mfaCodeSubmitSelector);

        if (
            !(await page.selectorTestMulti({
                successXPaths: [loginSuccessXPath],
                failureSelectors: [mfaInvalidSelector, mfaInvalidSelectorAlt]
            }))
        ) {
            throw new LoginException(LoginExceptionType.MFA_INVALID);
        }

        if (!params.totpSecret) {
            printDualAuthSuccess({portal: Portal.EPIC, attempt: 1});
        }
    }
}

async function verifyAccountNotBlocked(page: BrowserPage) {
    if (
        !(await page.selectorTestMulti({
            successXPaths: [loginSuccessXPath],
            failureXPaths: [enterDateOfBirthXPath]
        }))
    ) {
        throw new LoginException(LoginExceptionType.MANUAL_ACTION_REQUIRED);
    }
}

export const epicSalesScraper: Scraper<EpicParams> = {
    getSourceSideOrganizations,
    login,
    scrape,
    checkSession,
    manualLoginDetails: {
        url: dashboardPageUrl,
        successSelector: {
            value: loginSuccessXPath,
            type: 'xpath'
        }
    }
};
