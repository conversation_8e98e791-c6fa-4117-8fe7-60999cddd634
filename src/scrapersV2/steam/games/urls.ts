import {isNil} from 'lodash';
import {CustomException} from '../../../error/CustomException';
import {errorTypes} from '../../../error/errorTypes';

class InvalidAppUrlException extends CustomException {
    constructor(url: string, position: number) {
        super({
            message: `Invalid app url: '${url}' at position ${position}`,
            errorType: errorTypes.TEMPORARY_PORTAL_ISSUE
        });
    }
}

export function getUrlPartAtPosition(url: string, position: number): string {
    if (isNil(url)) {
        throw new InvalidAppUrlException(url, position);
    }
    const parts = url.split('/');
    if (parts.length <= position) {
        throw new InvalidAppUrlException(url, position);
    }
    return parts[Number(position)];
}
