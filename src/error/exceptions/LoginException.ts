import {LogLevel} from '../../utils/logger';
import {CustomException} from '../CustomException';
import {ErrorType, errorTypes} from '../errorTypes';

export enum LoginExceptionType {
    CREDENTIALS_INVALID,
    CREDENTIALS_EXPIRED,
    SESSION_EXPIRED,
    CAPTCHA_REQUIRED,
    MFA_REQUIRED,
    MFA_INVALID,
    TIMEOUT_2FA,
    TIMEOUT_2FA_MOBILE_APP_APPROVAL,
    TOO_MANY_2FA_ATTEMPTS,
    STEAM_GUARD_NOT_SUPPORTED,
    BANNED,
    UNKNOWN,
    MANUAL_ACTION_REQUIRED,
    RATE_LIMIT_EXCEEDED,
    UNSUPPORTED_LOGIN_ACTION,
    TOTP_CODES_FAILED
}

const errorDefaults: Map<LoginExceptionType, [errorType: ErrorType, friendlyMessage: string, suggestedAction?: string]> = new Map([
    [
        LoginExceptionType.UNSUPPORTED_LOGIN_ACTION,
        [
            errorTypes.NOT_SUPPORTED,
            'The application was required to perform a login action that is currently not supported.',
            'Please contact IndieBI support. For a short term solution use the browser login.'
        ]
    ],
    [
        LoginExceptionType.CREDENTIALS_INVALID,
        [errorTypes.INCORRECT_CREDENTIALS, 'Invalid credentials. Did you enter them correctly?', 'Please provide valid credentials.']
    ],
    [LoginExceptionType.CREDENTIALS_EXPIRED, [errorTypes.CREDENTIALS_EXPIRED, 'Password expired.', 'Please set a new password in your browser then try again.']],
    [
        LoginExceptionType.SESSION_EXPIRED,
        [errorTypes.SESSION_EXPIRED, 'Session expired.', 'Please log in again using credentials. If this also fails try using the manual login via browser']
    ],
    [LoginExceptionType.CAPTCHA_REQUIRED, [errorTypes.MISSING_CAPTCHA, 'Logging in requires solving captcha.', 'Please log in manually.']],
    [LoginExceptionType.MFA_REQUIRED, [errorTypes.MISSING_2FA, 'Logging in requires 2-factor authentication.', ' Please log in manually.']],
    [LoginExceptionType.MFA_INVALID, [errorTypes.INCORRECT_2FA, 'Provided 2-factor authentication code is invalid. Did you provide correct shared secret?', undefined]],
    [LoginExceptionType.TIMEOUT_2FA, [errorTypes.TIMEOUT_2FA, '2-factor authentication timed out.', 'Please try again later.']],
    [
        LoginExceptionType.TIMEOUT_2FA_MOBILE_APP_APPROVAL,
        [
            errorTypes.TIMEOUT_2FA_MOBILE_APP_APPROVAL,
            '2-factor authentication timed out. Sign in request was not approved in time in Steam Mobile App',
            'Please try again later.'
        ]
    ],
    [
        LoginExceptionType.STEAM_GUARD_NOT_SUPPORTED,
        [errorTypes.MISSING_2FA, 'Logging in using the Steam Guard mobile app is currently not supported', 'Use an account that uses email codes.']
    ],
    [
        LoginExceptionType.BANNED,
        [
            errorTypes.IP_BANNED,
            'Your IP is temporarily blocked by the portal.',
            'The issue will resolve itself in up to 3 days. If after this time the issue still occurs please contact IndieBI support.'
        ]
    ],
    [LoginExceptionType.UNKNOWN, [errorTypes.UNEXPECTED_ERROR, 'Unknown login issue encountered.', undefined]],
    [
        LoginExceptionType.MANUAL_ACTION_REQUIRED,
        [
            errorTypes.MANUAL_ACTION_REQUIRED,
            'Manual action required.',
            'Log in using your browser and answer additional questions to unlock your account or use another account.'
        ]
    ],
    [LoginExceptionType.RATE_LIMIT_EXCEEDED, [errorTypes.RATE_LIMIT_EXCEEDED, 'Rate limit exceeded.', 'Please try again later.']],
    [LoginExceptionType.TOO_MANY_2FA_ATTEMPTS, [errorTypes.TOO_MANY_2FA_ATTEMPTS, 'Too many 2FA attempts.', 'Please try again later.']]
]);
export class LoginException extends CustomException {
    type: LoginExceptionType;
    cause: Error | undefined;

    constructor(type: LoginExceptionType, message?: string, cause?: Error) {
        const [errorType, defaultMessage, suggestedAction] = errorDefaults.get(type)!;
        let messageToUse = message || defaultMessage;

        if (cause) {
            messageToUse = `${messageToUse} ${cause}`;
        }

        super({
            message: messageToUse,
            telemetryLogLevel: LogLevel.WARN,
            suggestedAction,
            errorType,
            error: cause
        });

        this.type = type;
        this.cause = cause;
    }
}
