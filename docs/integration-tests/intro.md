At the time of writing integration tests are designed to test actual working connections with external services.
They do NOT operate at an end to end level. They are not run on build binaries but rather trigger mechanisms in code that do the "work".

## Running integration tests
To run integration tests locally all you have to do is run from the MAIN directory:

```bash
./ci-cd/debug-integration-test.sh {platform}/{test-name}
```

For example
```bash
./ci-cd/debug-integration-test.sh meta/questScraper
```

To run these tests in debug mode add an env variable DEBUG with a value of 1

```bash
DEBUG=1 ./ci-cd/debug-integration-test.sh meta/questScraper
```

## Adding new integration tests

All new sources/modes should be covered in integration tests!

## Updating integration test credentials

sessions and credentials
both stored in separate encrypted files, to decrypt them you will need to set up a specific env variable {???}

due to VCR dependency (and python'ish shenanigans) the jobs to update both of those are in a make file.
To check all the available commands just run 
```bash
make
```
and hit the autocomplete button twice for a list of all commands.

To update the credentials you need to decrypt something -> change it -> encrypt it again

### Known quirks

#### Global

A lot of sources have their credentials set up but actually require a session to be created manually because of integration tests not handling 2FA.
The lists below are incomplete and **might** be updated as we go. It's purpose is mostly to inform the reader of the fact that updating credentials might not be enough since we use pure sessions as well. 

Tests that require sessions:
- steam
- gog
- humble
- ...

Tests that do NOT require sessions:
- meta sales
- ...

#### Steam
Steam sessions are invalidated if run from a different geographical region. That can be two different cities in the same country (which is rather seldom)
) or two different countries (which is more common). To avoid this problem we run runners in the same geographical region as the one we are located in.

Other ways of handling this could be
- running via proxy
- generating sessions on the fly in tests
- etc.

##### In order to update STEAM integration tests sessions, you need to:

1. Run `./ci-cd/run-integration-test.sh steam/*` (this will run all steam integration tests)
2. Run `make encryptSessions`, commit and push changes to gitlab
3. Profit

#### Epic 

Login does not work automatically due to captcha issues on Epic side. It is known and understood issue. It is not a priority to fix it at the moment.

In order to refresh the session you need to run integration tests locally and provide Steam 2FA code received on email.
The Epic session will expire quickly but if you use login via steam (which is the default login mechanism) you will be able to bypass the faulty captcha 
and input the 2fa generated in code to login.

##### In order to update EPIC integration tests session, you need to:

1. Run `make decryptCredentials` to get necessary credentials and API token
2. Find API token in `credentials.json` (it's `indie_bi_api.key`) and put it in `.env` file as `INDIEBI_JWT` in `scripts` folder
3. Run `./ci-cd/debug-integration-test.sh epic/scraper` and wait until you see the 2FA prompt
4. Using a separate terminal provide necessary 2FA code with `./scripts/send_2fa_code.sh epic_sales XXXXX`, where XXXXX is the code received on email
5. Run `make encryptSessions`, commit and push changes to repository
