import {ReadStream} from 'fs';
import * as chai from 'chai';
import * as chaiAsPromised from 'chai-as-promised';
import {StatusCodes} from 'http-status-codes';
import * as moment from 'moment';
import * as td from 'testdouble';
import {Source} from '../../../../src/dataTypes';
import {CustomException} from '../../../../src/error/CustomException';
import {errorTypes} from '../../../../src/error/errorTypes';
import {LoginException, LoginExceptionType} from '../../../../src/error/exceptions';
import {playStationSalesScraper} from '../../../../src/scrapersV2/playStation/scrapers/playStationSales';
import {ProgressCallback} from '../../../../src/scrapersV2/ProgressCallback';
import {ScraperContext} from '../../../../src/scrapersV2/ScraperContext';
import * as zip from '../../../../src/scrapersV2/zip';
import {FileExtension} from '../../../../src/utils/files/FileExtension';
import * as fileUtils from '../../../../src/utils/files/fileUtils';
import {HTTPClient} from '../../../../src/utils/http';
import {createErrorResponse} from '../../../utils/stubAxios';

chai.use(chaiAsPromised);
const {expect} = chai;

const credentials = {
    clientId: 'clientId',
    clientSecret: 'clientSecret'
};
const mockedProgress = td.func<ProgressCallback>();

let context: ScraperContext<any, any>;

describe('Playstation scraper', () => {
    const datasetId = '9bbbe3ed-36d9-4537-a638-a01cf2b3f0aa';
    const accessToken = 'Acc3ssTok3n';
    const userId = '641480091';

    let httpClient: HTTPClient;

    beforeEach(() => {
        httpClient = td.object<HTTPClient>();
        context = {params: credentials, progress: mockedProgress, getHttpClient: () => httpClient} as unknown as ScraperContext<any, any>;
    });
    afterEach(td.reset);

    describe('login', () => {
        it('should return valid PlayStation session', async () => {
            td.when(httpClient.get(td.matchers.anything())).thenResolve({
                data: {
                    access_token: 'access_token',
                    expires_in: 60 * 60,
                    userId
                }
            });
            const session = await playStationSalesScraper.login(context);
            expect(playStationSalesScraper.sessionObjectSyntaxValidator!(session)).to.be.true;
        });

        it('should throw LoginException if acquiring access token failed because of wrong credentials', async () => {
            td.when(httpClient.get(td.matchers.anything())).thenReject(createErrorResponse(null, StatusCodes.UNAUTHORIZED));
            await expect(playStationSalesScraper.login(context)).to.be.rejectedWith(new LoginException(LoginExceptionType.CREDENTIALS_INVALID).message);
        });

        it('should throw an error if acquiring access token failed because of other reasons', async () => {
            const error = new Error('Some error');
            td.when(httpClient.get(td.matchers.anything())).thenReject(error);
            await expect(playStationSalesScraper.login(context)).to.be.rejectedWith(
                new LoginException(LoginExceptionType.UNKNOWN, 'Unknown login issue encountered.', error).message
            );
        });
    });

    describe('scrape', () => {
        let saveStreamToFile;
        let prepareZipReports;
        const stream = td.object<ReadStream>();
        const startDate = moment('2019-01-01').utc();
        const endDate = moment('2019-01-31').utc();
        const reportFileName = fileUtils.generateFileName(Source.PLAYSTATION_SALES, startDate, endDate, FileExtension.JSON);

        beforeEach(() => {
            saveStreamToFile = td.replace(fileUtils, 'saveStreamToFile');
            prepareZipReports = td.replace(zip, 'prepareZipReports');
        });

        afterEach(() => {
            td.reset();
        });

        it('should download report, while reusing accessToken from provided session', async () => {
            context.session = {userId, accessToken, expirationDate: moment().add(1, 'minute').utc()};
            td.when(httpClient.get(td.matchers.contains({url: `https://api.domo.com/v1/datasets`})), {ignoreExtraArgs: true}).thenResolve({
                data: [{name: 'PROD|Daily Sales Dataset - MVP Partners', id: datasetId}]
            });
            td.when(httpClient.post(td.matchers.contains({url: `https://api.domo.com/v1/datasets/query/execute/${datasetId}`})), {ignoreExtraArgs: true}).thenResolve({
                data: stream
            });
            await playStationSalesScraper.scrape(context, startDate, endDate);
            td.verify(saveStreamToFile(stream, reportFileName));
            td.verify(prepareZipReports(startDate, endDate, [reportFileName], Source.PLAYSTATION_SALES, td.matchers.anything()));
            const urls = td.explain(httpClient.get).calls.map(({args}) => args[0]);
            expect(urls.length).to.be.greaterThan(0);
            expect(urls.some(({url}) => url.startsWith('https://api.domo.com/oauth/token'))).to.be.false;
        });

        it('should download report, while getting new accessToken, if no session is provided', async () => {
            td.when(httpClient.get(td.matchers.contains({url: `https://api.domo.com/oauth/token?grant_type=client_credentials&scope=data`})), {
                ignoreExtraArgs: true
            }).thenResolve({
                data: {access_token: accessToken, userId, expires_in: 60 * 60}
            });
            td.when(httpClient.get(td.matchers.contains({url: `https://api.domo.com/v1/datasets`})), {ignoreExtraArgs: true}).thenResolve({
                data: [{name: 'PROD|Daily Sales Dataset - MVP Partners', id: datasetId}]
            });
            td.when(httpClient.post(td.matchers.contains({url: `https://api.domo.com/v1/datasets/query/execute/${datasetId}`})), {ignoreExtraArgs: true}).thenResolve({
                data: stream
            });
            await playStationSalesScraper.scrape(context, startDate, endDate);
            td.verify(saveStreamToFile(stream, reportFileName));
            td.verify(prepareZipReports(startDate, endDate, [reportFileName], Source.PLAYSTATION_SALES, td.matchers.anything()));
            td.verify(httpClient.get(td.matchers.contains({url: `https://api.domo.com/oauth/token?grant_type=client_credentials&scope=data`})), {ignoreExtraArgs: true});
        });
    });

    describe('Get SourceSideOrganizations: ', () => {
        const testId = 123;
        const fake = 'fake';
        it('should properly parse acquired data', async () => {
            td.when(httpClient.get(td.matchers.contains({url: `https://api.domo.com/oauth/token?grant_type=client_credentials&scope=data`})), {
                ignoreExtraArgs: true
            }).thenResolve({
                data: {access_token: accessToken, userId, expires_in: 60 * 60}
            });
            td.when(httpClient.get(td.matchers.contains({url: `https://api.domo.com/v1/datasets`})), {ignoreExtraArgs: true}).thenResolve({
                data: [{name: 'PROD|Daily Sales Dataset - MVP Partners', id: datasetId}]
            });

            td.when(httpClient.post(td.matchers.contains({url: `https://api.domo.com/v1/datasets/query/execute/${datasetId}`})), {ignoreExtraArgs: true}).thenResolve({
                data: {rows: [[testId, fake]]}
            });

            const result = await playStationSalesScraper.getSourceSideOrganizations(context);

            expect(result).to.deep.equal([{id: testId.toString(), name: fake}]);
        });

        it('should throw error when no org data is acquired', async () => {
            td.when(httpClient.get(td.matchers.contains({url: `https://api.domo.com/oauth/token?grant_type=client_credentials&scope=data`})), {
                ignoreExtraArgs: true
            }).thenResolve({
                data: {access_token: accessToken, userId, expires_in: 60 * 60}
            });
            td.when(httpClient.get(td.matchers.contains({url: `https://api.domo.com/v1/datasets`})), {ignoreExtraArgs: true}).thenResolve({
                data: [{name: 'PROD|Daily Sales Dataset - MVP Partners', id: datasetId}]
            });

            td.when(httpClient.post(td.matchers.contains({url: `https://api.domo.com/v1/datasets/query/execute/${datasetId}`})), {ignoreExtraArgs: true}).thenResolve({
                data: {rows: []}
            });

            await expect(playStationSalesScraper.getSourceSideOrganizations(context)).to.be.rejectedWith(
                new CustomException({
                    message: 'The response contained no sales organization data',
                    errorType: errorTypes.CONFIGURATION_ISSUE
                }).message
            );
        });

        it('should throw error when acquired data is not full', async () => {
            td.when(httpClient.get(td.matchers.contains({url: `https://api.domo.com/oauth/token?grant_type=client_credentials&scope=data`})), {
                ignoreExtraArgs: true
            }).thenResolve({
                data: {access_token: accessToken, userId, expires_in: 60 * 60}
            });
            td.when(httpClient.get(td.matchers.contains({url: `https://api.domo.com/v1/datasets`})), {ignoreExtraArgs: true}).thenResolve({
                data: [{name: 'PROD|Daily Sales Dataset - MVP Partners', id: datasetId}]
            });

            const broken = 'broken';
            td.when(httpClient.post(td.matchers.contains({url: `https://api.domo.com/v1/datasets/query/execute/${datasetId}`})), {ignoreExtraArgs: true}).thenResolve({
                data: {rows: [[testId, fake], [broken]]}
            });

            await expect(playStationSalesScraper.getSourceSideOrganizations(context)).to.be.rejectedWith(
                new CustomException({
                    message: `There was an issue with the obtained partner raw data. Problematic data: ["${broken}"]. Whole data set [[${testId},"${fake}"],["${broken}"]]`,
                    errorType: errorTypes.CONFIGURATION_ISSUE
                }).message
            );
        });
    });
});
