import {Action} from './Action';

export class LoginResult {
    private readonly authenticationExceptionString?: string;
    private readonly customAction?: Action;
    private readonly permanentlyBlocked: boolean;

    private constructor(action?: Action, authenticationExceptionString?: string, permanentlyBlocked = false) {
        this.authenticationExceptionString = authenticationExceptionString;
        this.customAction = action;
        this.permanentlyBlocked = permanentlyBlocked;
    }

    public static createSuccessfulLoginResult(): LoginResult {
        return new LoginResult();
    }

    public static create2FactorAuthResult(): LoginResult {
        return new LoginResult(Action.DUAL_AUTH);
    }

    public static createExpiredSessionResult(): LoginResult {
        return new LoginResult(Action.SESSION_EXPIRED);
    }

    public static createAccountNotFoundResult(): LoginResult {
        return new LoginResult(Action.ACCOUNT_NOT_FOUND);
    }

    public static createAuthenticationExceptionResult(authenticationErrorString: string, terminateLoginProcess = false, action?: Action): LoginResult {
        return new LoginResult(action, authenticationErrorString, terminateLoginProcess);
    }

    public static createPasswordChangeResult(authenticationErrorString: string): LoginResult {
        return new LoginResult(Action.CHANGE_PASSWORD, authenticationErrorString);
    }

    public static createBannedIPResult(authenticationErrorString: string): LoginResult {
        return new LoginResult(Action.BANNED_IP, authenticationErrorString);
    }

    public static createCaptchaErrorResult(): LoginResult {
        return new LoginResult(Action.CAPTCHA);
    }

    public static createInsufficientPrivilegeLevelErrorResult(authenticationErrorString: string): LoginResult {
        return new LoginResult(Action.INSUFFICIENT_PRIVILEGE_LEVEL, authenticationErrorString, true);
    }

    public static createManualActionRequiredResult(authenticationErrorString: string): LoginResult {
        return new LoginResult(Action.MANUAL_ACTION_REQUIRED, authenticationErrorString, true);
    }

    public isFailedLogin(): boolean {
        return !!this.customAction || !!this.authenticationExceptionString || this.permanentlyBlocked;
    }

    public isSuccessfulLogin(): boolean {
        return !this.isFailedLogin();
    }

    public isPermanentlyBlocked(): boolean {
        return this.permanentlyBlocked;
    }

    public is2FactorAuth(): boolean {
        return this.customAction === Action.DUAL_AUTH;
    }

    public isSessionExpired(): boolean {
        return this.customAction === Action.SESSION_EXPIRED;
    }

    public isAuthenticationException(): boolean {
        return !!this.authenticationExceptionString && !this.customAction;
    }

    public isPasswordResetError(): boolean {
        return this.customAction === Action.CHANGE_PASSWORD;
    }

    public isBannedIPError(): boolean {
        return this.customAction === Action.BANNED_IP;
    }

    public isCaptchaError(): boolean {
        return this.customAction === Action.CAPTCHA;
    }

    public isInsufficientPrivilegeLevelError(): boolean {
        return this.customAction === Action.INSUFFICIENT_PRIVILEGE_LEVEL;
    }

    public getAuthenticationErrorString(): string | undefined {
        return this.authenticationExceptionString;
    }

    public getCustomAction(): Action | undefined {
        return this.customAction;
    }

    public toString(): string {
        return [this.customAction, this.authenticationExceptionString].filter(Boolean).join(' ');
    }
}
