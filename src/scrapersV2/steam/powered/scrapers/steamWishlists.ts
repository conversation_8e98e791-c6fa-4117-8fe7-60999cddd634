import * as AdmZip from 'adm-zip';
import {Moment} from 'moment/moment';
import {BrowserSession} from '../../../../browserV2';
import {Source} from '../../../../dataTypes';
import {Report} from '../../../../scrapersV1/Report';
import {downloadForOrganizationFinished, foundMultipleOrganizations, startingDownloadForOrganization} from '../../../../telemetry/events/multiorgEvents';
import {FileExtension} from '../../../../utils/files/FileExtension';
import {buildFilePath, generateFileName} from '../../../../utils/files/fileUtils';
import {HTTPClient} from '../../../../utils/http';
import {log} from '../../../../utils/logger';
import {retryAction} from '../../../../utils/retryAction';
import {IReportManifest} from '../../../ReportManifest';
import {ScrapeFunction} from '../../../Scraper';
import {FileMetaData} from '../../common/FileMetaData';
import {SteamParams, getLoginFunction} from '../../common/login';
import {SteamAdditionalData, steamDateFormat} from '../../common/requests';
import {steamScraperWrapper} from '../../common/scraperWrapper';
import {SteamOrganization} from '../../common/SteamSession';
import {checkSteamPoweredLogin, checkSteamPoweredSession} from '../checkSession';
import {getSteamPoweredSourceSideOrganizations, retrieveSourceOrgs} from '../multiorg';
import {steamPoweredManualLoginDetails} from '../steamPoweredManualLoginDetails';
import {downloadSteamPoweredFile, steamPoweredCheerioRequest} from '../steamPoweredRequest';

// eslint-disable-next-line security/detect-unsafe-regex
export const appIdRegExp = /(?<=\/app\/wishlist\/)(.*)(?=\/)/;

interface WishlistDownloadParams {
    productId: string;
    startDate: Moment;
    endDate: Moment;
    httpClient: HTTPClient;
    zip: AdmZip;
    orgId: SteamOrganization['id'];
}

const scrape: ScrapeFunction<SteamParams, BrowserSession> = async ({progress, getHttpClient, params}, startDate, endDate) => {
    const httpClient = getHttpClient();
    log.debug('Starting download the complete wishlist ...', {startDate, endDate});
    progress('Getting the sub-accounts list...');
    const fileMetaData = {};
    const orgs = await retrieveSourceOrgs(httpClient);
    foundMultipleOrganizations(orgs.map((org) => org.name));

    const productsAlreadyDownloaded = new Set<string>();
    const zip = new AdmZip();

    for (const subAccount of orgs) {
        startingDownloadForOrganization(subAccount.name);
        progress(`Going to download wishlist for sub-account: ${subAccount.name}.`);
        const productIds = await getProductIdsToDownload(subAccount, params.ignoredProducts || [], {
            progress,
            httpClient
        });
        const productsToDownload = productIds.filter((id) => !productsAlreadyDownloaded.has(id));
        progress(`Going to download wishlists for ${productsToDownload.length} products.`);
        for (const i in productsToDownload) {
            const productId = productsToDownload[i];
            progress(`Downloading wishlists for product: ${productId} (${Number(i) + 1}/${productsToDownload.length})`);
            const fileNames = await directlyDownloadAllAvailableWishlists({
                productId,
                startDate,
                endDate,
                httpClient,
                zip,
                orgId: subAccount.id
            });
            productsAlreadyDownloaded.add(productId);
            fileNames.forEach((fileName) => (fileMetaData[fileName] = {productId}));
        }
        log.info(`Products to skip (already downloaded):  ${[...productsAlreadyDownloaded]}`);
        downloadForOrganizationFinished(subAccount.name);
    }

    addBackwardCompatibleManifestToZip(zip, startDate, endDate, fileMetaData);
    const zipFileName = generateFileName(Source.STEAM_WISHLISTS, startDate, endDate, FileExtension.ZIP);
    const zipFilePath = buildFilePath(zipFileName);
    zip.writeZip(zipFilePath);

    return [new Report(Source.STEAM_WISHLISTS, zipFileName, startDate, endDate, false)];
};

function addBackwardCompatibleManifestToZip(zip: AdmZip, startDate: Moment, endDate: Moment, fileMetaData: {[p: string]: FileMetaData}) {
    const manifestFileDateFormat = 'YYYY-MM-DD';
    const manifest: IReportManifest & SteamAdditionalData = {
        dateFrom: startDate,
        dateTo: endDate,
        scraper: Source.STEAM_WISHLISTS,
        fileMetaData,
        startDate,
        endDate,
        metadata: fileMetaData
    };
    const manifestFileName = `additionalData_${manifest.scraper}_${startDate.format(manifestFileDateFormat)}_${endDate.format(manifestFileDateFormat)}.${
        FileExtension.JSON
    }`;

    zip.addFile(manifestFileName, Buffer.from(JSON.stringify(manifest), 'utf8'));
}

const wishlistRequestUrl = (productId: string, startDate: Moment, endDate: Moment): string =>
    `/report_csv.php?file=SteamWishlists_${productId}_${startDate.format(steamDateFormat)}_to_${endDate.format(
        steamDateFormat
    )}&params=query=QueryWishlistActionsForCSV^appID=${productId}^dateStart=${startDate.format(steamDateFormat)}^dateEnd=${endDate.format(
        steamDateFormat
    )}^interpreter=WishlistReportInterpreter`;
const cohortRequestUrl = (productId: string, startDate: Moment, endDate: Moment): string =>
    `/report_csv.php?file=SteamWishlistCohorts_${productId}_${startDate.format(steamDateFormat)}_to_${endDate.format(
        steamDateFormat
    )}&params=query=QueryWishlistCohortForCSV^appID=${productId}^dateStart=${startDate.format(steamDateFormat)}^dateEnd=${endDate.format(
        steamDateFormat
    )}^interpreter=WishlistCohortReportInterpreter`;

async function directlyDownloadAllAvailableWishlists({productId, startDate, endDate, httpClient, zip, orgId}: WishlistDownloadParams): Promise<string[]> {
    const fileNames: string[] = [];
    let i = 0;
    for (const url of [wishlistRequestUrl(productId, startDate, endDate), cohortRequestUrl(productId, startDate, endDate)]) {
        const fileName: string = generateFileName(Source.STEAM_WISHLISTS, startDate, endDate, FileExtension.CSV, `${productId}_${i}`);
        i++;
        fileNames.push(fileName);
        const downloadResponse = await retryAction({
            target: async () => downloadSteamPoweredFile(httpClient, url, orgId),
            maxAttempts: 10,
            delay: 3000
        });
        zip.addFile(fileName, Buffer.from(downloadResponse.data, 'utf8'));
    }
    return fileNames;
}

async function getProductIdsToDownload(steamOrganization: SteamOrganization, productsToIgnore: string[], {progress, httpClient}): Promise<string[]> {
    const availableProductIds = await scrapeAvailableProductIds(steamOrganization, {progress, httpClient});
    if (!availableProductIds.length) {
        progress('Scraper was unable to locate any products for downloading');
        return [];
    }

    if (!productsToIgnore.length) {
        return availableProductIds;
    }
    const filteredProducts = availableProductIds.filter((productId) => !productsToIgnore.includes(productId));
    progress(`Number of games after filtering: ${filteredProducts.length}`);
    if (!filteredProducts.length) {
        progress('All available products were ignored because of user configuration');
    }
    return filteredProducts;
}

async function scrapeAvailableProductIds(subaccountId: SteamOrganization, {progress, httpClient}): Promise<string[]> {
    progress('Getting wishlist overview page...');
    const wishlistOverviewPage = await retryAction({
        target: async () => steamPoweredCheerioRequest('/wishlist/overview/', httpClient, subaccountId),
        maxAttempts: 5,
        delay: 5000
    });
    const wishlistProductLinks = wishlistOverviewPage('tr.hover_row > td > a');

    progress(`Found ${wishlistProductLinks.length} products in the wishlist overview page`);
    const links: string[] = [];
    wishlistProductLinks.map((_index, element) => {
        const link = element.attribs.href; //await Browser.getPropertyFromElementHandle(gameElement, 'href');
        const regExpMatchArrayElement = link.match(appIdRegExp)![0];
        links.push(regExpMatchArrayElement); //TODO another ugly cheerio unwrap
        return regExpMatchArrayElement;
    });
    return links;
}

export const steamWishlistsScraper = steamScraperWrapper({
    login: getLoginFunction(Source.STEAM_WISHLISTS, checkSteamPoweredLogin),
    scrape,
    getSourceSideOrganizations: getSteamPoweredSourceSideOrganizations,
    checkSession: checkSteamPoweredSession,
    manualLoginDetails: steamPoweredManualLoginDetails
});
