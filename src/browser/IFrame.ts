import {<PERSON><PERSON><PERSON><PERSON><PERSON>, Frame} from '../puppeteer';
import * as browserMethods from './browserMethods';

export class <PERSON>rame {
    private static readonly defaultSelectorTimeout: number = 10000;

    private readonly iFrame: Frame;

    constructor(iFrame: Frame) {
        this.iFrame = iFrame;
    }

    public async elementExists(selector: string, timeout: number = IFrame.defaultSelectorTimeout): Promise<boolean> {
        return browserMethods.elementExists(this, selector, timeout);
    }

    public async waitForSelector(selector: string, timeout: number = IFrame.defaultSelectorTimeout): Promise<ElementHandle | null> {
        return this.iFrame.waitForSelector(selector, {visible: true, timeout});
    }

    public async querySelector(selector: string): Promise<ElementHandle | null> {
        return browserMethods.querySelector(this.iFrame, selector);
    }

    public async type(selector: string, input: string): Promise<void> {
        return browserMethods.type(this, this.iFrame, selector, input);
    }

    public async clearAndType(selector: string, input: string): Promise<void> {
        return browserMethods.clearAndType(this, this.iFrame, selector, input);
    }

    public async click(selector: string): Promise<void> {
        return browserMethods.click(this, this.iFrame, selector);
    }

    public async isDetached(): Promise<boolean> {
        return this.iFrame.isDetached();
    }
}
