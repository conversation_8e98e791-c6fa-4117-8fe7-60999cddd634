import {expect} from 'chai';
import * as fse from 'fs-extra';
import moment = require('moment');
import {FileExtension} from '../../../src/utils/files/FileExtension';
import {createCheckSessionTest} from '../checkSessionTestGenerator';
import {createScrapeTest} from '../scrapeTestGenerator';
import {LOGIN_ARGS, getScrapeArgs} from '../utils/ArgsProvider';
import {removeIfFilesExist, runCliMainWithConvertedArgs} from '../utils/Utils';

describe('Humble runner:', function () {
    const defaultStartDateString = '2020-01-01';
    const defaultEndDateString = '2020-02-01';
    const recentStartDate = moment.utc().subtract(5, 'days').format('YYYY-MM-DD');
    const recentEndDate = moment.utc().subtract(2, 'days').format('YYYY-MM-DD');

    const loginArgs = LOGIN_ARGS.for_humble;
    const scrapeArgs = getScrapeArgs(loginArgs, defaultStartDateString, defaultEndDateString, FileExtension.CSV);
    const recentScrapeArgs = getScrapeArgs(loginArgs, recentStartDate, recentEndDate, FileExtension.CSV);

    before(async () => {
        await removeIfFilesExist(scrapeArgs.testedFilePath!, recentScrapeArgs.testedFilePath!);
        await runCliMainWithConvertedArgs(loginArgs);
    });

    createCheckSessionTest(loginArgs, {id: '<EMAIL>'}, true);

    it('should download HUMBLE csv file with historical data', async () => {
        // when
        await runCliMainWithConvertedArgs(scrapeArgs);

        //then
        const expectedLines = 256; // 254 product entries + header line + new line at the end of file
        const content = (await fse.readFile(scrapeArgs.testedFilePath!, 'utf-8')).split('\n');
        expect(content).to.have.lengthOf(expectedLines);
    });

    createScrapeTest('should download HUMBLE csv file with recent data', recentScrapeArgs, [`humble_sales-${recentStartDate}_${recentEndDate}.csv`]);
});
