import {CustomException} from '../CustomException';
import {errorTypes} from '../errorTypes';

export class TemporaryPortalException extends CustomException {
    constructor(message: string) {
        super({
            message,
            suggestedAction: 'It looks like a temporary problem with platform. Please try again later.',
            errorType: errorTypes.TEMPORARY_PORTAL_ISSUE
        });
    }
}
