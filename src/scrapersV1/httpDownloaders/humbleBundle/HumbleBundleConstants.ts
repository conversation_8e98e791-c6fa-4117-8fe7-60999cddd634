import * as moment from 'moment';

export const mailCodePopupBodySelector = '.primary-section';
export const mailCodeInputSelector = '.text-input[name="code"]';
export const mailCodeConfirmationButtonSelector = 'button.flat-cta-button';
export const mailCodeErrorSelector = '.js-input-error';
export const usernameInputSelector = 'input[name="username"]';
export const passwordInputSelector = 'input[name="password"]';
export const loginButtonSelector = '.flat-cta-button.blue.no-style-button';
export const loginFailedSelector = '.js-input-error.input-status:not(:empty)';
export const reportDownloadButtonSelector = 'header > a:nth-of-type(2)';
export const headerSectionSelector = 'header';
export const cookieConsentAcceptButtonXPathSelector = '//*[@id="onetrust-accept-btn-handler"]';
export const downloadDateFormat = 'YYYYMMDD';
export const pageLoadWaitTime = 100000;
export const minWaitTime = 2000;
export const maxWaitDuration = 1000;
export const dashboardUrl = 'https://www.humblebundle.com/dashboard';
export const settingsUrl = 'https://www.humblebundle.com/user/settings';

export const reportDownloadLinkRegex = new RegExp(
    '^https://www.humblebundle.com/accounting/store/sales_reports/export/job_.{29}date=\\d{1,2}/\\d{1,2}/\\d{4}%20-%20\\d{1,2}/\\d{1,2}/\\d{4}&date_range_report=1$'
);

export const getDownloadedReportFileName = (startDate: moment.Moment, endDate: moment.Moment): string =>
    `humble_sales_report_${startDate.format(downloadDateFormat)}_${endDate.format(downloadDateFormat)}.csv`;

export const generateReportPageLink = (startDate: moment.Moment, endDate: moment.Moment): string =>
    `https://www.humblebundle.com/accounting/store/sales_reports/date_range?start_date=${startDate.format('MM%2FDD%2FYYYY')}&end_date=${endDate.format(
        'MM%2FDD%2FYYYY'
    )}`;

export const dualAuthMaxAttempts = 5; // NOT confirmed. TODO: confirm.
