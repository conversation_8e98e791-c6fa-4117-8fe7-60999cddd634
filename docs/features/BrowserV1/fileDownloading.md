There are 3 available options on how you could download a file

1. "Normal" - click download button
2. "Download via API"
3. "downloadFileUsingElementHandle"

# Which download option should I use?

If possible always go with the API approach, it's the fastest, easiest and most reliable/flexible approach.
If for some reason the API approach is not available AND you do not need to download files in parallel use the "downloadFileUsingElementHandle" approach
If all else fails try the "Normal" method

## `downloadFileUsingElementHandle` approach

### Why/when should you use it
-   When using BrowserV1 and ...
-   If you do not know how the downloaded file will be named
-   If the downloaded file is huge

### How does the `downloadFileUsingElementHandle` function work step by step:

-   set puppeteer download path to folder `X` (default download folder)
-   click download button
-   wait for file
-   rename the file to expected name
