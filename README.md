<!--- docs
# Metadata used by our doc generator
title: <PERSON><PERSON><PERSON> (JS)
group: scrapers
-->

# Scrapers (JS)

## Overview

**Scrapers**: A standalone client app that can be used to scrape information from specific user revenue sources.

## First time setup

Here's a cheat-sheet on how to start development on a fresh machine. Only Linux environments are supported. On Windows, use WSL2.

```bash

# 0. install unzip
 `sudo apt-get install unzip`
# 1. Configure your local NPM to access Gitlab Package Repository with your own Gitlab Personal Access Token, so that you can access private NPM dependencies.
# Go to `https://gitlab.com/-/profile/personal_access_tokens` and generate a new personal access token with `api` scope. Then, on your machine:
$ npm config set '//gitlab.com/api/v4/packages/npm/:_authToken' "<YOUR_TOKEN>"`

# 2. clone this repo:
$ <NAME_EMAIL>:bluebrick/indiebi/data-platform-team/scrapers-js.git
$ cd scrapers

# 3. Run `nvm use` to make sure you're using the right Node and NPM version: (OR USE asdf)
$ nvm use
# If you skip this by mistake, NPM will scream at you with:
#    npm ERR! code EBADENGINE
#    npm ERR! engine Unsupported engine

# Run `npm install`:
$ npm install

# 4. If you are a WSL user, make sure to install some manual dependancies using:
$ sudo apt install libgtk-3-dev libnotify-dev libgconf-2-4 libnss3 libxss1 libasound2
```

## Running unit tests

You have a few options for running unit tests:

1. Simply `npm run test`. This uses `ts-node`, which can be slow but you don't need to run anything else.
1. In one terminal, run `tsc --watch`. In another terminal, run `npm run test-unit-js`. This runs `mocha` from compiled JS instead of using interpreted Typescript. Thanks to incremental TypeScript compilation, this option gives you the fastest cycle time after save, very useful for TDD.
1. You can also use your IDE's testing UI. Config for VS Code's Mocha Test Explorer is included in project config in `.vscode`.

## Running integration tests

Check the integration test section in the docs folder.

## Building a production package

### Howto

-   Decide on a version number you're going to be releasing. We'll be using `0.23.0` for this example. If version is a POC for internal usage or for single client it should contain suffix after `-`, for example: `0.23.0-client-dev`
-   In JIRA, find the release `WIP Scrapers 0.23.0` and remove the `WIP:` prefix from release name. (If there's no release set up in Jira, create one). This will create an "Upcoming Release" post on the `~releases` Mattermost channel.
-   Create a protected branch `0.23.0`/`0.23.0-client-dev` using [Gitlab UI](https://gitlab.com/bluebrick/indiebi/scrapers/-/branches/new).
-   Gitlab CI will run a production package build and upload binary to azure
-   Let QA know of a new production package to test, send them a link to a successful pipeline run.
-   When QA approves the build, follow the Steam release guidelines under [this link](https://indiebi.atlassian.net/wiki/spaces/INDIEBI/pages/361904/How+To+release+Scraper)
-   If QA flags any issues, merge fixes to `master` first (using the usual MR process) and after that create MR `master`->`0.23.0`. Alternatively, you can delete the release branch and recreate it from the new master head (require admin permissions to remove protected branch).

### More comments

-   See `package.json` and `ci-cd/gitlab/package.yml` for implementation details on how building is done.
-   At the moment the only supported distribution package is a Windows package and it is being built automatically via the gitlab CI server (adding other operating system should not be a problem since pkg supports windows, linux and mac).
-   The specific apps need to be distributed with the additional chromium directory (which as the name implies contains a packaged version of the chromium browser). Each distributable has it's own special chromium version (a different one for linux, windows and mac) all of them are downloaded internally by puppeteer during the npm install step.
-   The distribution package should be build on the target OS. Building a windows exe package on linux is possible but it's execution results in a `Segmentation Fault` error.

### Package Configuration

There are two basic levels of configuration available in the application.

The default.json file, contains api/papertrail url's, api versions and so on - in other words everything that is env specific (env being dev, prod, staging).
TODO not true any longer!
The config.ts file contains all things that should stay the same (at least at startup) regardless of environment.

The configuration is made by rewriting the contents of the default.json file in specific gitlab runner jobs.

### Build process troubleshooting

-   When building the app you might encounter this error:

    `Warning Cannot include directory %1 into executable. The directory must be distributed with executable as %2. node_modules/puppeteer/.local-chromium path-to-executable/puppeteer `

    This is already handled by the chromium folder described above.

-   In case of issues with puppeteer and pkg the below links might be helpful:

*   https://github.com/zeit/pkg/issues/204
*   https://github.com/rocklau/pkg-puppeteer

**No other warnings should be present in the build process!**

## CLI

### Running from code

Running the CLI in development mode is possible with `npm run cli` command which will transpile source code on the fly. You can pass additional arguments after `--`. Example usage:

```shell
npm run cli -- check-session --source humble_sales --sessionFile "session-humble.json" --output json
```

### Building locally

To build CLI binaries locally you can use `npm run build-cli-win` or `npm run build-cli-linux`.

If you want to build CLI for all platforms you can use `npm run build-cli-all`

Be aware that result of build all and build for specific platform will be different and it's normal behavior of `pkg`.

`build-cli-all` will create `./builds/scrape-win.exe`, `./builds/scrape-linux` and `./builds/scrape-macos`, but `build-cli-win`/`build-cli-linux` will create `./builds/scrape.exe`/`./builds/scrape` and platform suffix will be omitted

### Debugging

For VS Code launch configurations has been prepared for you:

![](./docs/images/VSCodeDebugConfigs.png)

To use them, you would first need to set a few variables in .env file:

```bash
# SCRAPER_API_URL=https://scraper-api.indiebi.com  # PROD
SCRAPER_API_URL=https://scraper-api.indiebi.dev  # DEV
INDIEBI_EMAIL=<EMAIL>
INDIEBI_PASSWORD=yourpassword

# You can set CHROME_PATH to Chrome used by your IndieBI desktop app:
#CHROME_PATH='~/.config/indiebi-desktop/scrapers/binaries/chromium/chromium-linux-970485/chrome-linux/chrome'
#CHROME_PATH='~/Library/Application\ Support/indiebi-desktop/scrapers/binaries/chromium/chromium-mac-970485/chrome-mac/Chromium.app/Contents/MacOS/Chromium'
#
# Or you can run `./scripts/download_chrome.sh` to download it and set this path automatically

DATA_DIR='.private'
SCRAPE_FROM_DATE=2023-01-14
SCRAPE_TO_DATE=2023-02-23
STEAM_SALES_CREDENTIALS='{"user":"<user>","password":"<password>"}'
STEAM_DISCOUNTS_CREDENTIALS='{"user":"<user>","password":"<password>"}'
NINTENDO_SALES_CREDENTIALS='{"user":"<user>","password":"<password>"}'
PLAYSTATION_SALES_CREDENTIALS='{"clientId":"<client_id>","clientSecret":"<clientSecret>"}'
# etc.
```

# Adding a new scraper

`Source.ts` - add new source according to convention (what is the convention?? Where is it written down)
add new scraper in proper folder
`scraperProxy.ts` - add the new source, and it's mapping to Scrapers map - this should be rewritten to actually only contain all non v1 scrapers, so this does not need to be modified every time.

IN TESTS
create tests, remember about:

`argsProvider` - another map of providing specific args for specific scrapers — this should be unified to provide a nice default that only needs to be expanded in rare cases (if any at all)
`integration-test.yml` - add new test definition

# Shadow mode

Documentation regarding shadow mode runs can be found inside the Scraper-API repository (which makes a lot of sense once you think about it).
Since it's at least the second time I'm looking for it here I'll add a link here to find it next time!
