import {LogLevel} from '../../utils/logger';
import {CustomException} from '../CustomException';
import {errorTypes} from '../errorTypes';

export class TooManyIterationsException extends CustomException {
    constructor() {
        super({
            message: 'Too many iterations of the loop were executed.',
            telemetryLogLevel: LogLevel.ERROR,
            errorType: errorTypes.INTERNAL_SCRAPER_ISSUE
        });
    }
}
