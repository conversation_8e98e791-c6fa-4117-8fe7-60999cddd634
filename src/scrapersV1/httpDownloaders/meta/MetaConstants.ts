export const emailSelector = 'input[type="text"]';
export const passwordSelector = 'input[type="password"]';
export const loginConfirmationFormXPathSelector = "//span[contains(text(), 'Enter the confirmation code')]";
export const loginConfirmationInputSelector = 'input[type="text"]';
export const loginErrorXPathSelector = '//h2/span[contains(.,"Login Failure")]';
export const invalidConfirmationCodeErrorXPathSelector = '//span[contains(.,"Incorrect Login Code")]';
export const manageAppsListLinksSelector = '//a[@href^="https://developer.oculus.com/manage/organization"]';
export const passwordLoginXPathSelector = '//div/div/span[. = "Enter password instead"]';
export const cookiesButtonTimeout = 20000;
export const userSettingsAvailableXPathSelector = "//div[contains(text(), 'Settings')]";
export const errorPageTitleXPathSelector = "//title[contains(text(), 'Something went wrong')]";
export const facebookPageLinkSelector = '#back';
export const facebookLogoSelector = '#icon';
export const accessibleAppsXPathSelector = "//div[contains(text(), 'My Apps')]";
export const dashboardUrl = 'https://developer.oculus.com/manage/';
export const dashboardLoginUrl = dashboardUrl; // We want to start from redirect screen
export const logInButtonSelector = 'a[href^="https://auth.oculus.com/login"]';
export const loginWithEmailButtonXPathSelector = '//span/span[contains(.,"Log in with email")]';
export const loginContinueButtonXPathSelector = '//span/span[contains(.,"Next")]';
export const loginButtonXPathSelector = '//span/span[contains(.,"Log in")]';
export const cookiesAcceptButtonXPathSelector = '//button[@data-cookiebanner="accept_button" or @data-cookiebanner="close_button"]';
export const essentialCookiesAcceptButtonXPathSelector = '//span/span[contains(.,"Allow essential cookies")]';
export const allowAllCookiesButton = '//span/span[contains(.,"Allow all cookies")]';
export const isEnglishLanguageSelectedLoggedInXPathSelector = `//div/label/span[contains(.,"English (US)")]`;
export const isEnglishLanguageSelectedLoggedOutXPathSelector = `//button/div/span[contains(.,"English (US)")]`;
export const saveYourLoginInfoTextSelector = '//span[contains(.,"Save your login info?")]';

export enum GraphQLPersistedQuery {
    GET_AVAILABLE_APPS = '26696540246626173', // OCDevManageOrganizationAppsLoaderQuery
    GET_AVAILABLE_APPS_NEXT_PAGE = '8163787660365030', // OCDevManageOrganizationAppsGridPaginationQuery
    GET_ORGANIZATIONS = '3708151935937328',
    CHANGE_ORGANIZATION = '4204312409639242',
    GET_CURRENT_USER = '4819977008050586',
    CHANGE_LANGUAGE = '7207248396017210',
    CHANGE_LANGUAGE_LOGGED_IN = '4784477044955251',
    GET_APP_REAL_TIME = '8725122720898947',
    GET_APP_LIFETIME_REVENUE = '6574879059216559',
    GET_USER_METADATA = '8332192116828399',
    GET_APP_REVENUE_LEGACY_DASHBOARD = '6372287432833402',
    GET_APP_UNITS_SOLD_LEGACY_DASHBOARD = '6308235845886250'
}

export enum GraphQLAccessToken {
    LOGGED_OUT = 'OC|1592049031074901|'
}

export const timeFormat = 'YYYY-MM-DD';
export const legacyDashboardDateCutoff = '2024-09-10';
export const dualAuthMaxAttempts = 5; // NOT confirmed. TODO: confirm.
export const nextButtonXpath = '//span[.="Next"]';
