import * as moment from 'moment';
import {Browser} from '../../../browser/Browser';
import {Source} from '../../../dataTypes';
import {SourceIdentifier, SourceSideOrganization, defaultSourceSideOrganization} from '../../../dataTypes/SourceSideOrganization';
import {InsufficientPrivilegesLevelException, LoginException, LoginExceptionType, UnableToDownloadReportException} from '../../../error/exceptions';
import {Invalid2FACode} from '../../../error/exceptions/Invalid2FACode';
import ManualLoginDetails from '../../../scrapersV2/ManualLoginDetails';
import {isManualSession} from '../../../utils/conditions';
import {FileExtension} from '../../../utils/files/FileExtension';
import {checkFileExists, generateFileName, renameFile} from '../../../utils/files/fileUtils';
import {log} from '../../../utils/logger';
import {DownloadReportResult} from '../../DownloadReportResult';
import {HttpReportDownloader} from '../../HttpReportDownloader';
import {LoginResult} from '../../LoginResult';
import {Report} from '../../Report';
import {ReportDownloaderProperties} from '../../ReportDownloaderProperties';
import {validateDownloader} from '../validateDownloader';
import * as humbleElementConstants from './HumbleBundleConstants';

export class HumbleBundleReportDownloader implements HttpReportDownloader {
    private readonly login: string;
    private readonly password: string;
    private readonly browser: Browser;

    constructor({login, password, browser}: ReportDownloaderProperties) {
        validateDownloader(Source.HUMBLE_SALES, {browser});
        this.login = login!;
        this.password = password!;
        this.browser = browser!;
    }

    public static manualLoginDetails: ManualLoginDetails = {
        url: humbleElementConstants.dashboardUrl,
        successSelector: {value: '.dashboard-results', type: 'css'}
    };

    private static randomInt(low: number, high: number): number {
        return Math.floor(Math.random() * (high - low) + low);
    }

    public async checkIfUserIsUnauthorized(): Promise<void> {
        log.info('Checking if user is unauthorized...');

        const pageTitleSelector = '.page_title';
        if (!(await this.browser.elementExists(pageTitleSelector))) {
            return;
        }
        const titleText = await this.browser.getElementText(pageTitleSelector);
        if (['401 Unauthorized', '403 error, access denied'].includes(titleText)) {
            throw new InsufficientPrivilegesLevelException();
        }
    }

    public async fillSecondAuthenticationFactor(secondAuthenticationFactor: string): Promise<void> {
        log.info('Filling in two factor authentication...');
        await this.browser.clearAndType(humbleElementConstants.mailCodeInputSelector, secondAuthenticationFactor);
        await this.browser.click(humbleElementConstants.mailCodeConfirmationButtonSelector);
        if (await this.browser.elementExists(humbleElementConstants.mailCodeErrorSelector)) {
            throw new Invalid2FACode();
        }
        await this.browser.waitForSelectorToDisappear(humbleElementConstants.mailCodePopupBodySelector);
    }

    public get dualAuthMaxAttempts(): number {
        return humbleElementConstants.dualAuthMaxAttempts;
    }

    public async completeReportDownload(startDate: moment.Moment, endDate: moment.Moment): Promise<Report[]> {
        log.info('Opening report page...');

        await this.checkIfUserIsUnauthorized();
        const reportPageUrl = humbleElementConstants.generateReportPageLink(startDate, endDate);
        log.debug(reportPageUrl);
        const response = await this.browser.goto(reportPageUrl);
        if (response?.status() === 403) {
            throw new InsufficientPrivilegesLevelException();
        }
        await this.browser.waitForSelector(humbleElementConstants.reportDownloadButtonSelector, humbleElementConstants.pageLoadWaitTime);
        log.info('Clicking report button...');
        const reportDownloadHref = await this.getReportDownloadHref();
        //because Humble started to add +1 day to the end date for no reason
        const fileName = humbleElementConstants.getDownloadedReportFileName(startDate, moment(endDate).add(1, 'd'));
        log.debug(`Link used to download file: ${reportDownloadHref}`);
        try {
            await this.browser.downloadReportFile(reportDownloadHref, fileName);
        } catch (error) {
            throw new UnableToDownloadReportException(error, fileName);
        }

        if (!(await checkFileExists(fileName, true))) {
            throw new UnableToDownloadReportException();
        }
        const newReportName = generateFileName(Source.HUMBLE_SALES, startDate, endDate, FileExtension.CSV);
        await renameFile(fileName, newReportName);
        log.info('Report download is completed.');
        return [new Report(Source.HUMBLE_SALES, newReportName, startDate, endDate)];
    }

    public async downloadReport(startDate: moment.Moment, endDate: moment.Moment): Promise<DownloadReportResult> {
        log.info('Opening dashboard...');
        await this.browser.goto(humbleElementConstants.dashboardUrl);
        log.info('Checking if user is required to log in...');
        const loginResult: LoginResult = await this.performLogin();
        if (loginResult.isFailedLogin()) {
            return DownloadReportResult.createLoginErrorResult(loginResult);
        }
        return DownloadReportResult.createReportResult(await this.completeReportDownload(startDate, endDate));
    }

    public async performLogin(): Promise<LoginResult> {
        await this.browser.goto(humbleElementConstants.dashboardUrl);
        await this.handleCookieConsentPopup();
        await this.checkIfUserIsUnauthorized();
        if (!(await this.browser.elementExists(humbleElementConstants.usernameInputSelector))) {
            return LoginResult.createSuccessfulLoginResult();
        } else if (isManualSession(this.login, this.password)) {
            return LoginResult.createExpiredSessionResult();
        }
        log.info('Typing user name...');
        await this.browser.type(humbleElementConstants.usernameInputSelector, this.login);
        /**
         * We want to wait a random number of seconds,
         *  because in some cases (not many) is helps to bypass captcha,
         *  and user is not asked to fill it manually
         */
        await this.browser.wait(
            HumbleBundleReportDownloader.randomInt(humbleElementConstants.minWaitTime, humbleElementConstants.minWaitTime + humbleElementConstants.maxWaitDuration)
        );
        log.info('Typing password...');
        await this.browser.type(humbleElementConstants.passwordInputSelector, this.password);
        log.info('Logging in...');
        await this.browser.click(humbleElementConstants.loginButtonSelector);

        if (await this.browser.elementExists(humbleElementConstants.loginFailedSelector)) {
            return LoginResult.createAuthenticationExceptionResult('Wrong credentials, please enter correct username and/or password.');
        }

        log.info('Checking for two factor authentication...');
        if (await this.browser.elementExists(humbleElementConstants.mailCodeInputSelector)) {
            return LoginResult.create2FactorAuthResult();
        }
        log.info('Checking if user needs to fill captcha.');
        if (await this.browser.elementExists(humbleElementConstants.usernameInputSelector)) {
            log.info('You will be asked to login manually soon.');
            return LoginResult.createCaptchaErrorResult();
        }
        return LoginResult.createSuccessfulLoginResult();
    }

    public async checkSession(): Promise<SourceIdentifier> {
        await this.browser.goto(humbleElementConstants.dashboardUrl);
        if (await this.browser.elementExists(humbleElementConstants.usernameInputSelector)) {
            throw new LoginException(LoginExceptionType.SESSION_EXPIRED);
        }

        await this.checkIfUserIsUnauthorized();

        await this.browser.goto(humbleElementConstants.settingsUrl);
        return {
            id: await this.browser.getValueOfProperty('#email', 'value')
        };
    }

    private async getAvailableHrefs(): Promise<string[]> {
        const headerElement = await this.browser.querySelector(humbleElementConstants.headerSectionSelector);
        const hrefElements = await headerElement!.$$('a');
        return Promise.all(hrefElements.map(async (el) => (await el.getProperty('href')).jsonValue() as Promise<string>));
    }

    private async getReportDownloadHref(): Promise<string> {
        return (await this.getAvailableHrefs()).filter((name) => humbleElementConstants.reportDownloadLinkRegex.test(name))[1];
    }

    private async handleCookieConsentPopup(): Promise<void> {
        log.info('Checking if there is a cookie consent popup');
        if (await this.browser.xPathElementExistsAfterTime(humbleElementConstants.cookieConsentAcceptButtonXPathSelector)) {
            log.info('Accepting cookies');
            await this.browser.getHandleAndClick(humbleElementConstants.cookieConsentAcceptButtonXPathSelector, Browser.defaultSelectorTimeout, true);
        }
    }

    /**
     * Multi orgs not supported by this source.
     * We could extract the account name and call it the organization name but that would be very confusing for both us and the end users.
     */
    public async getSourceSideOrganizations(): Promise<SourceSideOrganization[]> {
        return [defaultSourceSideOrganization];
    }
}
