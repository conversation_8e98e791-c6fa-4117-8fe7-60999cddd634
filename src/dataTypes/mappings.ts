import {Portal} from './Portal';
import {Source} from './Source';

//TODO delete this
export function sourceToPortal(source: Source): Portal {
    return (
        {
            [Source.HUMBLE_SALES]: Portal.HUMBLE,
            [Source.GOG_SALES]: Portal.GOG,
            [Source.NINTENDO_SALES]: Portal.NINTENDO,
            [Source.EPIC_SALES]: Portal.EPIC,
            [Source.STEAM_SALES]: Portal.STEAM,
            [Source.STEAM_WISHLISTS]: Portal.STEAM,
            [Source.STEAM_IMPRESSIONS]: Portal.STEAM,
            [Source.STEAM_DISCOUNTS]: Portal.STEAM,
            [Source.META_QUEST_SALES]: Portal.META,
            [Source.META_RIFT_SALES]: Portal.META,
            [Source.PLAYSTATION_SALES]: Portal.PLAYSTATION,
            [Source.PLAYSTATION_WISHLIST_ACTIONS]: Portal.PLAYSTATION,
            [Source.STEAM_WISHLIST_BALANCE]: Portal.STEAM
        } as const
    )[source];
}
