import {expect} from 'chai';
import * as td from 'testdouble';
import {<PERSON>rowser} from '../../../../src/browser/Browser';
import * as MetaConstants from '../../../../src/scrapersV1/httpDownloaders/meta/MetaConstants';
import {GraphQLPersistedQuery} from '../../../../src/scrapersV1/httpDownloaders/meta/MetaConstants';
import {MetaReportDownloader} from '../../../../src/scrapersV1/httpDownloaders/meta/MetaReportDownloader';
import * as testData from './testData';
import {graphqlRequestMatcher} from './testData';

describe('Meta scraper - fill login', () => {
    const {source, validLogin: login, validPassword: password} = testData;
    let fakeBrowser: Browser;
    let metaReportDownloader: MetaReportDownloader;

    beforeEach(() => {
        fakeBrowser = td.object<Browser>();
        metaReportDownloader = new MetaReportDownloader({login, password, browser: fakeBrowser, source});
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.loginWithEmailButtonXPathSelector)).thenResolve(true);
    });

    afterEach(td.reset);

    it('properly fill login', async () => {
        td.when(fakeBrowser.cookies()).thenResolve([{value: 'asda', name: 'oc_ac_at'} as any]);
        td.when(fakeBrowser.post('https://graph.oculus.com/graphql', graphqlRequestMatcher(GraphQLPersistedQuery.GET_USER_METADATA))).thenResolve({
            data: {viewer: {user: {is_verified: true}}}
        });
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.loginErrorXPathSelector)).thenResolve(false);
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.loginConfirmationFormXPathSelector)).thenResolve(false);
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.userSettingsAvailableXPathSelector)).thenResolve(true);
        td.when(fakeBrowser.elementExists(MetaConstants.passwordSelector)).thenResolve(true);
        const loginResult = await metaReportDownloader.performLogin();
        expect(loginResult.isFailedLogin()).to.be.false;
    });

    it('properly test performLogin and handle invalid credentials provided', async () => {
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.loginErrorXPathSelector)).thenResolve(true);
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.loginConfirmationFormXPathSelector)).thenResolve(false);
        td.when(fakeBrowser.elementExists(MetaConstants.passwordSelector)).thenResolve(true);
        const result = await metaReportDownloader.performLogin();
        expect(result.isAuthenticationException()).to.be.true;
        expect(result.getAuthenticationErrorString()).to.be.equal('Login failed. Invalid credentials or too many login attempts in a row.');
    });

    it('properly test performLogin and handle 2 factor authentication', async () => {
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.loginErrorXPathSelector)).thenResolve(false);
        td.when(fakeBrowser.xPathElementExistsAfterTime(MetaConstants.loginConfirmationFormXPathSelector)).thenResolve(true);
        td.when(fakeBrowser.elementExists(MetaConstants.passwordSelector)).thenResolve(true);
        const result = await metaReportDownloader.performLogin();
        expect(result.is2FactorAuth()).to.be.true;
    });
});
