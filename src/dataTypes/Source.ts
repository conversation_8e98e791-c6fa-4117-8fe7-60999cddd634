// please sync with https://gitlab.com/bluebrick/indiebi/data-pipeline/report-processor/-/blob/main/report_processor/dictionaries/sources.py

export enum Source {
    HUMBLE_SALES = 'humble_sales',
    GOG_SALES = 'gog_sales',
    NINTENDO_SALES = 'nintendo_sales',
    EPIC_SALES = 'epic_sales',
    STEAM_SALES = 'steam_sales',
    STEAM_WISHLISTS = 'steam_wishlists',
    STEAM_IMPRESSIONS = 'steam_impressions',
    STEAM_DISCOUNTS = 'steam_discounts',
    STEAM_WISHLIST_BALANCE = 'steam_wishlist_balance',
    META_RIFT_SALES = 'meta_rift_sales',
    META_QUEST_SALES = 'meta_quest_sales',
    PLAYSTATION_SALES = 'playstation_sales',
    PLAYSTATION_WISHLIST_ACTIONS = 'playstation_wishlist_actions'

    // Some sources defined in the business dictionary (e.g. siee_sales)
    // may be missing from here. Only portals actually supported by scrapers included in this repo
    // are defined.
}

export const displaySource: Record<Source, string> = {
    [Source.HUMBLE_SALES]: 'Humble - sales',
    [Source.GOG_SALES]: 'GOG - sales',
    [Source.NINTENDO_SALES]: 'Nintendo - sales',
    [Source.EPIC_SALES]: 'Epic - sales',
    [Source.STEAM_SALES]: 'Steam - sales',
    [Source.STEAM_WISHLISTS]: 'Steam - wishlists',
    [Source.STEAM_IMPRESSIONS]: 'Steam - visibility',
    [Source.STEAM_DISCOUNTS]: 'Steam - discounts',
    [Source.STEAM_WISHLIST_BALANCE]: 'Steam - wishlist balance',
    [Source.META_RIFT_SALES]: 'Meta rift - sales',
    [Source.META_QUEST_SALES]: 'Meta quest - sales',
    [Source.PLAYSTATION_SALES]: 'PlayStation - sales',
    [Source.PLAYSTATION_WISHLIST_ACTIONS]: 'PlayStation - wishlist actions'
};

export function isSteamSource(source: Source): boolean {
    return [Source.STEAM_SALES, Source.STEAM_WISHLISTS, Source.STEAM_IMPRESSIONS, Source.STEAM_DISCOUNTS, Source.STEAM_WISHLIST_BALANCE].includes(source);
}
