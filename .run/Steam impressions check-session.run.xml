<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Steam impressions check-session" type="NodeJSConfigurationType" application-parameters="check-session --source=steam_impressions --sessionFile=&quot;.private/steam_impressions.json&quot; --from=&quot;2022-01-01&quot; --to=&quot;2022-01-02&quot; --reportPath=&quot;.private&quot; --encrypt=false --encryptionToken=AAC3NzaC1lZDI1NTE5AAAAIEEeNeCKusQdsRESYjf9enY27Spi7A9gFwX1yrMKk --headless=false" path-to-js-file="dist/src/scrape.js" working-dir="$PROJECT_DIR$">
    <method v="2">
      <option name="TypeScript.Before.Run" enabled="true" FAIL_ON_ERROR="true" CONFIG_PATH="$PROJECT_DIR$/tsconfig.json" />
    </method>
  </configuration>
</component>