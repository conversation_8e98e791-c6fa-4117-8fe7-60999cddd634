import {LogLevel} from '../../utils/logger';
import {CustomException} from '../CustomException';
import {errorTypes} from '../errorTypes';

export class UnableToLoadUrlException extends CustomException {
    constructor(url: string) {
        super({
            message: `Unable to load url: ${url}`,
            telemetryLogLevel: LogLevel.WARN,
            errorType: errorTypes.TEMPORARY_PORTAL_ISSUE
        });
    }
}
