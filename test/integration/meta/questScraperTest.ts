import * as moment from 'moment';
import {FileExtension} from '../../../src/utils/files/FileExtension';
import {LOGIN_ARGS, getScrapeArgs} from '../utils/ArgsProvider';
import {removeIfFilesExist, runCliMainWithConvertedArgs} from '../utils/Utils';
import {itShouldDownloadZippedDataAndCheckFileFormat} from './common';

describe('Meta (questScraper) runner:', () => {
    const defaultStartDateString = '2020-01-01';
    const defaultEndDateString = '2020-02-01';
    const recentStartDate = moment.utc().subtract(7, 'days').format('YYYY-MM-DD');
    const recentEndDate = moment.utc().subtract(2, 'days').format('YYYY-MM-DD');
    const loginArgs = LOGIN_ARGS.for_meta_quest;
    const scrapeArgs = getScrapeArgs(loginArgs, defaultStartDateString, defaultEndDateString, FileExtension.ZIP);
    const recentScrapeArgs = getScrapeArgs(loginArgs, recentStartDate, recentEndDate, FileExtension.ZIP);

    before(async () => {
        await removeIfFilesExist(scrapeArgs.testedFilePath!);
        await runCliMainWithConvertedArgs(loginArgs);
    });

    it(
        'should download Meta Quest zipped data and check number of files',
        itShouldDownloadZippedDataAndCheckFileFormat(scrapeArgs, defaultStartDateString, defaultEndDateString)
    );

    it(
        'should download recent Meta Quest zipped data and verify number of files',
        itShouldDownloadZippedDataAndCheckFileFormat(recentScrapeArgs, recentStartDate, recentEndDate)
    );
});
